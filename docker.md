- Common Docker Commands
    - docker build: Builds an image from a Dockerfile.
    - docker run: Runs a container from an image.
    - docker ps: Lists running containers.
    - docker stop: Stops a running container.
    - docker rm: Removes a stopped container.
    - docker rmi: Removes an image.
    - docker system prune: remove all stopped containers, unused networks, build caches, and dangling images
    - docker-compose up: Creates and starts containers defined in a docker-compose.yml file.
    - docker-compose down: Stops and removes containers, networks, and volumes defined in a docker-compose.yml file.

- What is Dockerfile
    - A Dockerfile is a script that defines instructions for building a Docker image. Each command in the Dockerfile sets up a specific part of the environment. When we run these commands, Docker builds an image layer-by-layer.

- What is Docker Compose, and how is it different from Dockerfile?
    - Docker Compose is a tool for defining and managing multi-container Docker applications using a YAML file (docker-compose.yml). It allows us to configure services, networks, and volumes in a single file, making it easier to manage complex applications.
    - Differences from Dockerfile:
        - A Dockerfile is used to build a single Docker image by defining its layers and dependencies.
        - Docker Compose is used to run and orchestrate multiple containers that may rely on each other (e.g., a web app container and a database container).

- Why do we use volumes in Docker? 
    - We use Docker volumes to keep data safe outside of Docker containers. They provide a separate location on hosts where data lives even if the container gets removed. Also, it's easier to manage, back up, and share the volumes among containers. 

- docker-compose command
    - Docker Compose is a tool that allows you to define and manage multi-container Docker applications. It uses a YAML file (docker-compose.yml) to configure the services, networks, and volumes that your application needs. With Docker Compose, you can easily create, start, stop, and manage multiple interconnected containers with a single command.

- What are Docker secrets?
    - Docker secrets are a feature of Docker designed to securely store and manage sensitive data, such as passwords, API keys, certificates, or other credentials, which are required by applications running in Docker containers. Secrets ensure that sensitive information is not hard-coded into the application, Docker images, or configuration files, providing a more secure way to handle credentials.
    - Secure Storage:
        - Secrets are encrypted both at rest and in transit.
        - They are stored in the Docker swarm manager nodes’ Raft logs, encrypted by default.
    - Access Control:
        - Secrets are only accessible to services explicitly authorized to use them.
        - They are not available to other containers or users by default.
    - Runtime Access:
        - Secrets are mounted into containers as files and not stored in the container's image or environment variables.
    - Immutable:
        - Secrets cannot be updated once created. You must create a new secret if modifications are needed.
    - How Docker Secrets Work
        - Docker secrets are designed for use with Docker Swarm.
        - Secrets are created and stored securely in the swarm.
        - Containers running in swarm services can access the secrets if they have been granted access.

- What is docker volume?
    - A Docker volume is a storage mechanism that allows data to persist beyond the lifecycle of a container. Volumes enable data sharing between containers or between the container and the host machine, making them essential for stateful applications like databases.
    - Key Features
        - Persistent Storage: Data stored in volumes is not deleted when the container is removed.
        - Isolation: Volumes are managed by Docker, ensuring better isolation than bind mounts.
        - Sharing Data: Multiple containers can access and share the same volume.
        - Ease of Use: Docker provides commands to manage volumes easily.
        - Backup and Restore: Volumes can be backed up and restored independently of containers.
    - When to Use
        - Stateful Applications: Databases, log storage, and applications requiring persistent data.
        - Data Sharing: Sharing data between containers, such as configurations or shared storage.
        - Container Upgrades: To retain data across container recreations

- Docker networks
    - Docker networks are essential for enabling container communication, providing isolation, and managing application connectivity. By leveraging Docker's networking capabilities, you can build scalable, secure, and highly available containerized applications.

- What is Docker Swarm?
    - Docker Swarm is a container orchestration tool that manages and deploys services across a cluster of Docker nodes. It enables high availability, scalability, and load balancing, allowing multiple hosts to act as a single virtual Docker engine.

- What are dangling images in Docker, and how do you remove them? 
    - Dangling images in Docker are unused image layers that no longer have any tags associated with them. They often build up when you create new images with the same name and tag, leaving the old layers without references. These images can consume significant disk space, so it’s important to clean them up.
    - docker system prune command cleaup those dangling images

- What is the primary difference between Docker and Kubernetes?
    - Docker is a containerization platform that allows you to build, ship, and run containers. It focuses on creating and managing individual containers.Kubernetes, on the other hand, is an orchestration platform designed to manage multiple containers at scale. It handles deployment, scaling, load balancing, and self-healing across clusters of nodes.
    - https://www.datacamp.com/blog/kubernetes-vs-docker

- Compare Docker Swarm with Kubernetes. 
    - Kubernetes and Docker Swarm manage containers, but they work differently:
        - Kubernetes manages large and complex container setups. Its self-healing and built-in monitoring features make it a more suitable option for complex environments.
        - Docker Swarm is suitable for smaller or less complex setups as it doesn't offer any built-in features like Kubernetes. We can easily integrate it with Docker tools like Docker CLI and Docker Compose.

- What is a Pod in Kubernetes, and how is it different from a container?
    - A Pod is the smallest deployable unit in Kubernetes and represents a group of one or more containers that share the same network namespace, storage, and configuration.Unlike individual containers, Pods allow multiple tightly coupled containers to work together as a single unit (e.g., a web server and a sidecar logging container).


- References:
    - https://www.datacamp.com/blog/docker-interview-questions 