---------------------------------------------------------------------------------------------------------------------
### General
---------------------------------------------------------------------------------------------------------------------
- debugging 
- analytical skill
- session management
- security
- performance
- middleware
- routing
- active records
- microservices
- SaaS
- rest api
- design patterns
- solid principles
- indexes
- slow query optimization
- oops
- ci/cd
- unit testing
- tdd/bdd
- graphql
- cache - Redis vs Memcached 
- docker
    - docker volume
    - docker image
    - docker containers
    - can we up container docker without dokcer-compose.yml file?
- template
    - Haml (HTML abstraction markup language)
    - erb (Embedded Ruby)
- aws
    - serverless - AWS Lambda, Amazon EventBridge, AWS IoT Core, Amazon API Gateway, Amazon S3, AWS Step Functions, Amazon DynamoDB
    - serverbase - AWS EC2, Amazon ECS, AWS Batch, Amazon VPC, Amazon RDS, Amazon EKS
- Deployment Strategy
- Agile scrum ceremonies
    - Sprint planning, Daily scrum, Sprint review, Sprint retrospective, Product backlog grooming 

---------------------------------------------------------------------------------------------------------------------
### Python
---------------------------------------------------------------------------------------------------------------------
- Routes 
- API versionning
- OOPs
    - constructor (initialize method)
    - destructor (using ObjectSpace.define_finalizer)
    - Singleton class
    - By Default instance methods are private or public?
    - How to access private method from class object?
    - static method
    - Does Python supports multiple inheritance? 
    - How many ways we can achieve multiple inheritance? 
    - Problem of Multiple inheritance
    - Interface & Abstract class (These 2 lead tightly coupled code)
    - Static polymorphism (compile-time polymorphism - method overloading) - Not supported by Ruby, but there is a way to achieve this
    - Dynamic polymorphism (run-time polymorphism - method overriding) - Supported by Ruby
- debugging tools and library

- Security

- Technical Architect
    - https://github.com/mpatel2280/interview-prep/blob/master/python.md#technical-architect

- Performance

- MicroService
    - Microservice design patterns (system-architect.md)
    - Synchronous vs. Asynchronous Communication
    - Microservice design patterns to aggregate and process data
        - https://github.com/mpatel2280/interview-prep/blob/master/system-architect.md#20-microservice-design-patterns-to-aggregate-and-process-data
    - 
- SaaS - https://github.com/mpatel2280/interview-prep/blob/master/system-architect.md#19-saas
    - Database design for a multi-tenant application


- Design Patterns - https://github.com/mpatel2280/interview-prep/blob/master/ror.md#design-patterns

- References:
    - https://www.djangoproject.com/
    - https://flask.palletsprojects.com/en/stable/
    - https://fastapi.tiangolo.com/
    - https://www.fullstack.cafe/interview-questions/python