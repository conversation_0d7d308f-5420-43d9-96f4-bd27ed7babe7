
https://onecompiler.com/ruby

# Create API endpoint that accepts a list of order IDs and marks each order as "processed." if it hasn’t already been marked as processed. Write a Rails controller action in Ruby that: Accepts a JSON array of order_ids. For each order_id, checks if the order is already marked as "processed" in the database. If not, marks it as "processed." Ensure that your code is efficient, handling cases where there might be a large number of order_ids submitted at once.


input:
{
  "order_ids": [101, 102, 301, 432, 589, 690]
}
output:
{
  "message": "3 orders marked as processed."
}

```ruby
class OrdersController < ApplicationController
  def process_orders
    order_ids = params[:order_ids] # Expecting a JSON array of order IDs

    # Fetch orders that are not already marked as processed
    orders_to_process = Order.where(id: order_ids, processed: false)

    # Update orders in a batch to mark them as processed
    updated_count = orders_to_process.update_all(processed: true, processed_at: Time.current)

    if updated_count.positive?
      render json: { message: "#{updated_count} orders marked as processed." }, status: :ok
    else
      render json: { message: "No orders were updated." }, status: :unprocessable_entity
    end
  rescue => e
    render json: { error: "An error occurred: #{e.message}" }, status: :internal_server_error
  end
end

```
# Write a Ruby on Rails method to handle a common REST API endpoint. Suppose you are given a GET /api/orders/:id endpoint that retrieves order details for a specific order. Implement the Rails controller method to handle this endpoint. The order should include details about items, pricing, and the user associated with the order. Consider using eager loading to optimize for performance.
 
```ruby
module Api
  class OrdersController < ApplicationController
    # GET /api/orders/:id
    def show
      # Find the order by its ID
      order = Order.find_by(id: params[:id])

      if order
        # Return the order details as JSON
        render json: { status: "success", data: order }, status: :ok
      else
        # Handle case where order is not found
        render json: { status: "error", message: "Order not found" }, status: :not_found
      end
    rescue StandardError => e
      # Handle any unexpected errors
      render json: { status: "error", message: e.message }, status: :internal_server_error
    end
  end
end

# Or Path based versioning
# config/routes.rb
- /api/v1/orders 
- /api/v2/orders

Rails.application.routes.draw do
  namespace :api do
    namespace :v1 do
      resources :orders
    end
    namespace :v2 do
      resources :orders
    end
  end
end

# app/controllers/api/v1/posts_controller.rb
class Api::V1::OrdersController < ApplicationController
  def index
    render json: { message: "API V1 - Orders" }
  end
end

# Exception handling 
class ApplicationController < ActionController::API
  rescue_from ActiveRecord::RecordNotFound, with: :record_not_found

  private

  def record_not_found
    render json: { error: "Record not found" }, status: :not_found
  end
end

```
# SOLID Principle

```ruby
# Example 1 
# Open/Closed Principle for diff types of payment method in billing service. 
# e.g payment from card, upi, paypal, amazonpay etc.
 Billing 
   def initialize(billing_method)
    @billing_method = billing_method
  end

  def processPayment(amount)
    @billing_method.pay(amount)
  end
end

class CardPayment
  def pay(amount)
    puts "Card Payment $#{amount}"
  end
end

class UpiPayment
  def pay(amount)
    puts "Upi Payment $#{amount}"
  end
end


objCard = CardPayment.new 
objUpi = UpiPayment.new 

objBilling = Billing.new(objCard)
objBilling.processPayment(10)

objBilling = Billing.new(objUpi)
objBilling.processPayment(20)

# Example 2
# Open/Closed Principle for diff types of notification method 
# e.g SMS Notification, WhatsApp Notification etc.

class Notification 
   def initialize(instance_method)
    @instance_method = instance_method
  end

  def notify()
    @instance_method.notify()
  end
end

class SMSNotification
  def notify()
    puts "SMSNotification"
  end
end

class WhatsAppNotification
  def notify()
    puts "WhatsAppNotification"
  end
end

objSms = SMSNotification.new 
objWhatsApp = WhatsAppNotification.new 

objNotification = Notification.new(objSms)
objNotification.notify()


objNotification = Notification.new(objWhatsApp)
objNotification.notify()

# Example 3
# Open/Closed Principle for diff types of log storage method 
# e.g Log File, Log MongoDB etc.
# Save Log details in User.save action
	# log details in file OR 
	# log details in MongoDB  

# E.g 
# class User 
#   def save
#    // log details (File or MongoDB)
#   end
# end

class CustomLog 
   def initialize(instance_method)
    @instance_method = instance_method
  end

  def log(msg)
    @instance_method.log(msg)
  end
end

class LogFile
  def log(msg)
    puts "From LogFile => #{msg}"
  end
end

class LogMongo
  def log(msg)
    puts "From LogMongo => #{msg}"
  end
end

class User < CustomLog
  def save
    puts 'save user'
    self.log('User Class => save user')
  end
end

objLogFile = LogFile.new 
objLogMongo = LogMongo.new 

# objLog = CustomLog.new(objLogFile)
# objLog.log('objLogFile')


# objLog = CustomLog.new(objLogMongo)
# objLog.log('objLogMongo')

ObjUser = User.new(objLogFile)
ObjUser.save 

```

# Prepend Example
```ruby

# Output:

# 1.method_a_module
# 4.Cmodule method_a_module
# 6.Dmodule method_a_module

 module Amodule 
   def method_a_module
     puts "1.method_a_module"
   end
 end
 
  module Bmodule 
    include Amodule
   def method_b_module
     puts "2.method_b_module"
   end
 end
 
 module Cmodule 
    include Amodule
   def method_c_module
     puts "3.method_c_module"
   end
   def method_a_module 
     puts "4.Cmodule method_a_module"
   end 
 end
 
 module Dmodule 
    include Cmodule
    include Bmodule
   def method_d_module
     puts "5.method_d_module"
   end
   def method_a_module 
     puts "6.Dmodule method_a_module"
   end 
 end
 
 
 class Dclass 
   include Dmodule
 end
 
 objD = Dclass.new 
 objD.method_a_module


######################################################
# Solution 1 - Without prepend
######################################################

 module Amodule 
   def method_a_module
     puts "1.method_a_module"
     # super
   end
 end
 
  module Bmodule 
    include Amodule
   def method_b_module
     puts "2.method_b_module"
   end
 end
 
 module Cmodule 
    include Amodule
   def method_c_module
     puts "3.method_c_module"
   end
   def method_a_module 
     super
     puts "4.Cmodule method_a_module"
     
   end 
 end
 
 module Dmodule 
    include Cmodule
    include Bmodule
   def method_d_module
     puts "5.method_d_module"
   end
   def method_a_module 
    super
     puts "6.Dmodule method_a_module"
     
   end 
 end
 
 
 class Dclass 
   include Dmodule
 end
 
 objD = Dclass.new 
 objD.method_a_module


######################################################
# Solution 2 - With prepend
######################################################

 module Amodule 
   def method_a_module
     puts "1.method_a_module"
     super
   end
 end
 
  module Bmodule 
    include Amodule
   def method_b_module
     puts "2.method_b_module"
   end
 end
 
 module Cmodule 
    prepend Amodule
   def method_c_module
     puts "3.method_c_module"
   end
   def method_a_module 
     puts "4.Cmodule method_a_module"
     super
   end 
 end
 
 module Dmodule 
    prepend Cmodule
    include Bmodule
   def method_d_module
     puts "5.method_d_module"
   end
   def method_a_module 
    # super
     puts "6.Dmodule method_a_module"
     
   end 
 end
 
 
 class Dclass 
   include Dmodule
 end
 
 objD = Dclass.new 
 objD.method_a_module

# Output:
# Loggable method first
# User method second

# Fix it
# app/models/concerns/loggable.rb
module Loggable
  def method_log
    puts "Loggable method first"
  end
end


# app/models/user.rb
class User
  def method_log
    puts "User method second"
  end
end

user = User.new
user.method_log

######### Option 1 Without Prepend
# app/models/concerns/loggable.rb
module Loggable
  def method_log
    puts "Loggable method first"
  end
end


# app/models/user.rb
class User
  extend Loggable
  def method_log
    User.method_log
    puts "User method second"
  end
end

# Usage
user = User.new
user.method_log

######### Option 2 With Prepend
# app/models/concerns/loggable.rb
module Loggable
  def method_log
    puts "Loggable method first"
    super # Calls the original `method_log` method in the User class
  end
end


# app/models/user.rb
class User
  def method_log
    puts "User method second"
  end
end

# Prepending the Loggable module to the User class
User.prepend(Loggable)
# Usage
user = User.new
user.method_log

```

# Multiple Inheritance
```ruby
module ModuleA
  def methodA
    puts "ModuleA methodA!"
  end
end

module ModuleB 
  def methodB
    puts "ModuleB methodB!"
  end
end

class ClassA # If class classA then it thrown an error
  include ModuleA
  include ModuleB 
end

obj = ClassA.new # If class classA then it thrown an error
obj.methodA # Output: ModuleA methodA!
obj.methodB # Output: ModuleB methodB!

```

# Metaprogammig 
```ruby 
class Dynamic
  ["greet", "farewell"].each do |method_name|
    define_method(method_name) do
      "This is #{method_name}"
    end
  end
end

dynamic = Dynamic.new
puts dynamic.greet    # Output: "This is greet"
puts dynamic.farewell # Output: "This is farewell"

```
# Duck typing
```ruby 
# Duck typing means that an object’s suitability is determined by the presence of certain methods and properties 
# rather than the object’s class. 
class Duck
  def quack
   puts "Quack!"
  end
end

class Person
  def quack
    puts "I can quack too!"
  end
end

def make_it_quack(duck_like)
  duck_like.quack
end

make_it_quack(Duck.new)   # Output: "Quack!"
make_it_quack(Person.new) # Output: "I can quack too!"

```
# SQL Injection 

```ruby
# Vulnerable
Product.where("title = '#{params[:title]}'")

# Safe
Product.where(title: params[:title])
```

# Mixins example - Create reusable module and use them in multiple classes - one of the class must access method as class method and one of the class access it as instance method
```ruby
module Logging
  def log(msg)
    puts msg
  end
end

class Student
  include Logging
  def print_log(msg)
    log(msg)
  end
end

class Instructor
  extend Logging
  def print_log(msg)
    Instructor.log(msg)
  end
end

s = Student.new
s.print_log('print student')

i = Instructor.new
i.print_log('print Instructor')
```
```ruby
# puts 1+'5'
# puts 1+'5'.to_i
```
# email validation 
```ruby
class User < ApplicationRecord
  validate :validate_email_format_and_uniqueness, if: -> { email.present? }

  private

  def validate_email_format_and_uniqueness
    unless email.match?(URI::MailTo::EMAIL_REGEXP)
      errors.add(:email, "is not a valid email")
    end

    if User.where(email: email).exists?
      errors.add(:email, "has already been taken")
    end
  end
end
```

# prepend module example 
```ruby
# app/models/concerns/loggable.rb
module Loggable
    def greet
      log_greeting
      super # Calls the original `greet` method in the class
    end
  
    private
  
    def log_greeting
      puts "Greeting method called"
    end
  end
  
  # app/models/user.rb
  class User
    def greet
      "Hello from User!"
    end
  end
  
  # Prepending the Loggable module to the User class
  User.prepend(Loggable)
  
  # Usage
  user = User.new
  puts user.greet
  
  ```
  
  ```ruby
  class User
    attr_accessor :role
  
    def initialize(role)
      @role = role
    end
    def admin?
      role == 'admin'
    end
  end
  
  # Open the User class to add a method
  class User
    def admin?
      role == 'admin1'
    end
  end
  
  user = User.new('admin1')
  puts user.admin? # Output: true
  
  user2 = User.new('user')
  puts user2.admin? # Output: false
  ```
  # Singlton
  ```ruby
  # app/services/app_config.rb
  require 'singleton'
  
  class AppConfig
    include Singleton
  
    def initialize
      @config = {
        site_name: "My Site",
        admin_email: "<EMAIL>"
      }
    end
  
    def site_name
      @config[:site_name]
    end
  
    def admin_email
      @config[:admin_email]
    end
  
    # Add other configuration methods as needed
  end
  # Retrieve the single instance of AppConfig
  config = AppConfig.instance
  puts config.site_name      # => "My Site"
  puts config.admin_email    # => "<EMAIL>"

  # Fix the Err

  # Working Fine
  require 'singleton'

  class SingletonCls
    include Singleton 
    
    def initialize
      @conn = get_instance
    end
    
    def get_instance
      puts 'Get Instance'
    end
    
    def get_data
      puts 'Get Data'
    end
  end

  objS = SingletonCls.instance 
  objS.get_data

  objS1 = SingletonCls.instance 

  puts objS.equal?(objS1)

  obj1 = SingletonCls.instance.object_id
  puts obj1

  obj2 = SingletonCls.instance.object_id
  puts obj2

  # Error with instance 
  objS = SingletonCls.new 

```
  
  ```ruby
  class ParentClass
    attr_accessor :name
    
    def initialize(name)
      @name = name     
    end
    
    def display
      "Name: #{@name}"
    end
    
  end
  
  parentclass = ParentClass.new('Abc')
  puts parentclass.display
  
  class Person
    @@count = 0  # Class variable
  
    def initialize(name, age)
      @name = name
      @age = age
      @@count += 1
    end
  
    def display_info
      "Name: #{@name}, Age: #{@age}"
    end
  
    def get_count
      @@count
    end
  end
  
  # Creating instances of Person
  person1 = Person.new("Alice", 30)
  person2 = Person.new("Bob", 25)
  
  puts person1.display_info    # Output: Name: Alice, Age: 30
  puts person2.display_info    # Output: Name: Bob, Age: 25
  puts person2.get_count            # Output: 2
  ```
  ```ruby
  string1 = "H1ello"
  string2 = "Wo2rld"
  concatenated_string = string1 << " " << string2
  
  puts concatenated_string
  
  string = "apple,banana,grape,orange"
  array = string.split(',')
  puts array.inspect 
 ```
# Duplicate occurance count
```ruby
 # Duplicate occurrences count
scripts = ['ror', 'java', 'php', 'ror', 'javascript', 'java', 'ror'];

# Count occurrences of each element
occurrences = scripts.tally

# Print the occurrences of each element
puts "Occurrences of each element: #{occurrences}"

# Occurrences of each element: {"ror"=>3, "java"=>2, "php"=>1, "javascript"=>1}

def count_occurrences(arr)
  occurrences = Hash.new(0) # Initialize a hash with default value 0

  # Iterate through the array once
  arr.each do |num|
    occurrences[num] += 1
  end

  occurrences
end

# Example usage
numbers = [1, 2, 2, 3, 3, 3, 4, 1, 2, 4, 4, 4, 4]
result = count_occurrences(numbers)
puts  "Occurrences of each element: #{result.inspect}"
```
# Array rearrange
```ruby
# insert array as [1,2,3,4,5] and get output as [5,1,2,3,4]
# without using new array or any inbuilt functions, only with for loop

def array_arrange(arr)
  n = arr.length
  last_element = arr[-1] # Save the last element
  for i in (n - 1).downto(1) # Shift elements to the right
    arr[i] = arr[i - 1]
  end
  arr[0] = last_element # Set the first element to the previously last element
  arr
end

# Example usage
array = [1, 2, 3, 4, 5]
result = array_arrange(array)
puts result.inspect

```
# Count less than and grether than number in an array
```ruby
def find_positions_greater_less_than_n(arr, n)
  sorted_array = arr.sort
  puts sorted_array.inspect
  position = sorted_array.index(n)
  # puts index
  # #puts sorted_array[sorted_array.length-1]
  # if( n == sorted_array[sorted_array.length-1])
  #   position = sorted_array.length
  # else
  #   position = sorted_array.each_with_index.find { |value, _| value > n }&.last
  #   puts position
  # end 
  
  if position
    first_part, second_part = sorted_array.each_with_index.partition { |_, index| index < position }
    [first_part.map(&:first), second_part.map(&:first)]
    "less than #{n} count: #{ first_part.length} and greater than count: #{second_part.length - 1}."
  else
    "Number #{n} not in the array."
  end
end

# Input
# Output => less than 11 count: 9 and greater than count: 6.
numbers = [3, 4, 10, 5, 1, 8, 2, 13,  15, 11, 18, 12, 9, 7, 108, 111]
puts find_positions_greater_less_than_n(numbers, 11)

# Input
# Output => less than 3 count: 2 and greater than count: 3.
numbers = [1,2,3,4,5,6]
puts find_positions_greater_less_than_n(numbers, 3)
``` 
# Find the missing number from a sequence with a given difference with 
```ruby
def find_missing_number(sequence_array, difference_with)
  # Calculate expected length of the sequence if no numbers were missing
  expected_count = ((sequence_array.max - sequence_array.min) / difference_with) + 1

  # Calculate the sum of an ideal sequence
  expected_sum = (expected_count * (sequence_array.min + sequence_array.max)) / 2

  # Subtract the actual sum of the sequence from the expected sum
  missing_number = expected_sum - sequence_array.sum

  missing_number
end

# Simple logic 
def check_missing(sequence_array, difference_with)
 i = 1
 sequence_array.each do |n| 
   missing = 0
   current = i * difference_with 
   if (current != n) 
     return current
   end
   i+=1
 end
end
# Example Usage
sequence_array = [2, 4, 8, 10] # 6 is missing in the sequence
difference_with = 2
puts check_missing(sequence_array, difference_with) # Output: 6

# Example Usage
sequence_array = [2, 4, 8, 10] # 6 is missing in the sequence
difference_with = 2
puts find_missing_number(sequence_array, difference_with) # Output: 6


sequence_array = [4,8,12,16,24] # 20 is missing in the sequence
difference_with = 4
puts find_missing_number(sequence_array, difference_with) # Output: 20
```
# Find missing number from an array
```ruby
def find_missing_number(n, arr)
  total_sum = n * (n+1)/2 
  current_sum = arr.sum 
  missing_numer = total_sum - current_sum
  
end
  
puts find_missing_number(10, [1, 2, 3, 4, 6, 7, 8, 9, 10]) # Output 5
```

# find the first positive missing number 
```ruby 
# Input: [1,2,4] # Output 3
# Input: [-2, 1, 4, 5, -8] # Output 2

def find_first_missing_positive_number(arr)
  arr.sort!
  n = arr.length
  if (arr[0] > 0 )
    first_positive_number = arr[0]
  else 
     first_positive_number = 1 
  end 
  
  (0..n).each do |i|
    if(arr[i] == first_positive_number)
      first_positive_number = first_positive_number + 1
    elsif(arr[i] > first_positive_number)
      break
    end
  end
  first_positive_number
end

puts find_first_missing_positive_number([1,2,4]) # Output 3
puts find_first_missing_positive_number([-2, 1, 4, 5, -8]) # Output 2
puts find_first_missing_positive_number([3,12,14]) # Output 4
```
# Find the triple element from an array to get the sum
```ruby
# https://www.geeksforgeeks.org/find-a-triplet-that-sum-to-a-given-value/

# Input array = [ 2, 5, 9, 8, 12, 4 ] , sum = 18
# Output = 2, 12, 4

# Input array = [ 2, 5, 9, 8, 12, 4 ] , sum = 22
# Output = 2, 8, 12

# Input array = [1,2, 4, 5, 6, 9, 10, 12, 3],  sum = 25
# Output = 4, 9, 12

# Option 1
def find_triplet_with_sum(arr, sum)
  n = arr.length

  (0..n-2).each do |a|
    (a+1..n-1).each do |b|
      (b+1..n).each do |c|
        current_sum = arr[a].to_i + arr[b].to_i + arr[c].to_i
        if current_sum == sum
            return [arr[a], arr[b], arr[c]]
        end
      end
    end
  end
  nil
end

# Example usage
array =[ 2, 5, 9, 8, 12, 4 ]
target_sum = 18
result = find_triplet_with_sum(array, target_sum)

puts "Output: #{result}" # Output: [2, 12, 4]


# Option 2
def find_triplet_with_sum(array, target_sum)
  n = array.length

  # Sort the array to make the process more efficient
  array.sort!

  (0..n-3).each do |i|
    left = i + 1
    right = n - 1

    while left < right
      current_sum = array[i] + array[left] + array[right]

      if current_sum == target_sum
        return [array[i], array[left], array[right]]
      elsif current_sum < target_sum
        left += 1
      else
        right -= 1
      end
    end
  end

  # Return nil if no triplet is found
  nil
end

# Example usage
array =[ 2, 5, 9, 8, 12, 4 ]
target_sum = 18
result = find_triplet_with_sum(array, target_sum)

if result
  puts "Triplet found: #{result}"
else
  puts "No triplet with the given sum found."
end
```
# n queens problem
```ruby
def solve_n_queens(n)
  @queens_in_board = []

  row = 0
  column = 0

  until @queens_in_board.size == n
    if queen_in_row(row) || queen_in_diagonal(row, column, n)
      row += 1

      while row >= n
        row    = @queens_in_board[-1][0] + 1
        column = @queens_in_board[-1][1]

        puts "Backtracking, deleted: #{@queens_in_board.pop}"
      end
    else
      place_queen(row, column)

      p "placing at #{row} #{column}"

      row = 0
      column += 1
    end
  end

  @queens_in_board
end

def queen_in_row(row)
  @queens_in_board.find { |r, c| r == row }
end

def queen_in_diagonal(row, column, n)
  diagonals =
    right_upper_diagonal_for(row, column, n) +
    left_upper_diagonal_for(row, column, n) +
    left_lower_diagonal_for(row, column, n) +
    right_lower_diagonal_for(row, column, n)


  diagonals.any? { |r, c| r == row && c == column } ||
  diagonals.any? { |r, c| @queens_in_board.any? { |qr, qc| r == qr && c == qc } }
end

def top_row?(row, n)
  row == n
end

def place_queen(row, column)
  @queens_in_board << [row, column]
end

def right_upper_diagonal_for(row, column, n)
  diagonals = []

  until row == n || column == n
    diagonals << [row += 1, column += 1]
  end

  diagonals
end

def left_upper_diagonal_for(row, column, n)
  diagonals = []

  until row == n || column == 0
    diagonals << [row += 1, column -= 1]
  end

  diagonals
end

def right_lower_diagonal_for(row, column, n)
  diagonals = []

  until row == 0 || column == n
    diagonals << [row -= 1, column += 1]
  end

  diagonals
end

def left_lower_diagonal_for(row, column, n)
  diagonals = []

  until row == 0 || column == 0
    diagonals << [row -= 1, column -= 1]
  end

  diagonals
end

def print_board(n)
  board = Array.new(n) { Array.new(n) { "." } }

  @queens_in_board.each { |queen| board[queen[0]][queen[1]] = "Q" }

  board.map { |n| n.join("|") }.reverse
end

p solve_n_queens(4)
p solve_n_queens(5)

puts print_board(5)
```
# Kadane's max subarray sum
```ruby
# https://www.geeksforgeeks.org/largest-sum-contiguous-subarray/
# Kadane's algorithm scans through the array and keeps track of the current subarray sum, updating it as it goes
#  takes input as an array of integers and returns the maximum sum of any contiguous subarray

# Finds the maximum subarray sum in a array of numbers
  # Input: [-2, 1, -3, 4, -1, 2, 1, -5, 4]
  # Output: 6 

  # Input: [-2, 1, -3, 4, -1, 2, 1, -5, 8]
  # Output: 9

def kadane_max_subarray_sum(arr)
  return 0 if arr.empty?

  max_sum = arr[0]
  current_sum = arr[0]

  kadane_arr = []

  arr[1..].each do |num|
    #puts num
    current_sum = [num, current_sum + num].max
    if current_sum > max_sum
      kadane_arr << num
      max_sum = [max_sum, current_sum].max
    end
   
  end

  max_sum
  #puts kadane_arr.inspect 
end

puts kadane_max_subarray_sum([-2, 1, -3, 4, -1, 2, 1, -5, 4]) # Output: 6
puts kadane_max_subarray_sum([-2, 1, -3, 4, -1, 2, 1, -5, 8]) # Output: 9
puts kadane_max_subarray_sum([1, 2, 3, 4])                   # Output: 10
puts kadane_max_subarray_sum([-4, -1, -7, -8])               # Output: -1
puts kadane_max_subarray_sum([-2, 3, 1, -4, 5, 2, -1, 2]) # Output: 8
puts kadane_max_subarray_sum([1,-3,2,1,-1]) # Output: 3

puts kadane_max_subarray_sum([-1, 2, 3, -5, 6, 7, -7, 4,-1]) #Output 13
puts kadane_max_subarray_sum([-1, 2, 3, -5, 6, 7, -7, 8,-1]) #Output 14

#  test cases to validate the solution
require 'minitest/autorun'

class TestMaxSubarraySum < Minitest::Test
  def test_basic_case
    assert_equal 6, kadane_max_subarray_sum([-2, 1, -3, 4, -1, 2, 1, -5, 4])
  end

  def test_all_negative
    assert_equal -1, kadane_max_subarray_sum([-4, -1, -7, -8])
  end

  def test_all_positive
    assert_equal 10, kadane_max_subarray_sum([1, 2, 3, 4])
  end

  def test_single_element
    assert_equal 5, kadane_max_subarray_sum([5])
  end

  def test_empty_array
    assert_equal 0, kadane_max_subarray_sum([])
  end

  def test_large_values
    assert_equal 15, kadane_max_subarray_sum([-1, 2, 3, 4, -2, 4])
  end
end


```

# Second Largest Array
```ruby
  def second_largest_array(arr)

    # Sort the array and remove duplicates
    sorted_unique = arr.uniq.sort
    
    # Check if there are at least two distinct elements
    if sorted_unique.length >= 2
      second_largest = sorted_unique[-2]
    else
      second_largest = "Not enough unique elements"
    end
    
    second_largest 
  end

puts second_largest_array([20, 22, 12, 5, 9, 55]) # output 22
```

# Nth Largest Array
```ruby
def nth_largest_array(n, arr)

    # Sort the array and remove duplicates
    sorted_unique = arr.uniq.sort
    i = n*-1
    # Check if there are at least two distinct elements
    if sorted_unique.length >= n
      nth_largest = sorted_unique[i]
    else
      nth_largest = "Not enough unique elements"
    end
    
    nth_largest 
  end

puts nth_largest_array(5, [20, 21, 22, 12, 5, 9, 55]) # oputput 12

```
# Factory Pattern with Duck Typing
```ruby
  class FactoryClass
  
    def get_instance
      raise NotImplementedError, "#{self.class} has not implemented method '#{__method__}'"
    end

  
    def factory_method
      objFactory = get_instance
      objFactory.parent_operation
    end
  end

  class ParentClass
    def parent_operation
      raise NotImplementedError, "#{self.class} has not implemented method '#{__method__}'"
    end
  end


  class ChildofFactoryClass1 < FactoryClass
  
    def get_instance
      ChildClass1.new
    end
  end

  class ChildofFactoryClass2 < FactoryClass
    def get_instance
      ChildClass2.new
    end
  end

  class ChildClass1 < ParentClass
    def parent_operation
      'Result of the ChildClass1'
    end
  end

  class ChildClass2 < ParentClass
    def parent_operation
      'Result of the ChildClass2'
    end
  end


  def client_code(creator)
  puts creator.factory_method
  end

  # objC1 = ChildClass1.new
  # puts objC1.parent_operation

  # objC2 = ChildClass2.new
  # puts objC2.parent_operation

  client_code(ChildofFactoryClass1.new)

  client_code(ChildofFactoryClass2.new)
```
# Chain of Responsibility 
```ruby
  # app/services/chain_handler.rb
  class ChainHandler
    attr_accessor :next_handler

    def initialize
      @next_handler = nil
    end

    def call_next(handler)
      @next_handler = handler
      #handler
    end

    def handle(request)
      if @next_handler
        @next_handler.handle(request)
      else
        puts "Request reached the end of the chain without being handled."
      end
    end
  end


  # app/services/authentication_handler.rb
  class AuthenticationHandler < ChainHandler
    def handle(request)
      if request[:user_authenticated]
        puts "User authenticated successfully."
        super(request)
      else
        puts "Authentication failed. Stopping the chain."
      end
    end
  end


  # app/services/inventory_handler.rb
  class InventoryHandler < ChainHandler
    def handle(request)
      if request[:items_in_stock]
        puts "Items are in stock."
        super(request)
      else
        puts "Items out of stock. Stopping the chain."
      end
    end
  end

  # app/services/payment_handler.rb
  class PaymentHandler < ChainHandler
    def handle(request)
      if request[:payment_successful]
        puts "Payment processed successfully."
        super(request)
      else
        puts "Payment failed. Stopping the chain."
      end
    end
  end



  def call_handler(handler)
    request = {
    user_authenticated: true,
    items_in_stock: true,
    payment_successful: true
  }

      result = handler.handle(request)
    
  
  end

  auth_handler = AuthenticationHandler.new
      inventory_handler = InventoryHandler.new
      payment_handler = PaymentHandler.new

  auth_handler.call_next(inventory_handler).call_next(payment_handler)
  call_handler(inventory_handler)

  # app/services/order_processor.rb
  # class OrderProcessor
  #   def process(request)
  #     auth_handler = AuthenticationHandler.new
  #     inventory_handler = InventoryHandler.new
  #     payment_handler = PaymentHandler.new

  #     # Build the chain
  #     auth_handler.call_next(inventory_handler).call_next(payment_handler)

  #     # Start the chain
  #     auth_handler.handle(request)
  #   end
  # end


  # # Test request
  # request = {
  #   user_authenticated: true,
  #   items_in_stock: true,
  #   payment_successful: true
  # }

  # processor = OrderProcessor.new
  # processor.process(request)

```
# Find Max Element
```ruby
def find_max_arr_element(arr)
  max_elm = arr[0]
  arr.each do |n|
    if n>max_elm
      max_elm = n
    end
  end
   max_elm
end

puts find_max_arr_element( [20, 21, 22, 65, 5, 9, 1 ]) # oputput 65
```

# Fix the err
```ruby
module Logging
  def log_info(message)
    puts "#{message}"
  end
end

class User 
  extend Logging
  def save_user
   log_info("User saved.")
  end
end

user = User.new
user.save_user

- change extend to include
- or update log_info with User.log_info

# Fix it 2
class CountController
  @number = 0
  
  def num_increment
    @number += 1
  end
 
  def num_value
    @number
  end

end


objCount = CountController.new
objCount.num_increment
puts objCount.num_value

# Fixed
class CountController
  def initialize
    @number = 0
  end
  
  def num_increment
    @number += 1
  end
 
  def num_value
    @number
  end

end


objCount = CountController.new
objCount.num_increment
puts objCount.num_value

```

# Lambda Fix the err 

```ruby
test_lambda = ->(x) do
  begin
    raise "Invalid input" if x.nil?
    x * 2
  rescue => e
    puts "Error: #{e.message}"
  end
end

puts test_lambda.call(2) # Output: "Error: Invalid input"

# Error
lambda_function = ->(n) do
  n * 10
end

puts lambda_function.call()

# Fix
lambda_function = ->(n) do
  begin
    raise "Must a positive value" if ->(n) { n < 0 }
    n * 10
  rescue => e
    puts "#{e.message}"
  end
end

puts lambda_function.call(-3)



```
