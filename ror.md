- https://guides.rubyonrails.org
- https://guides.rubyonrails.org/routing.html
- https://guides.rubyonrails.org/command_line.html
- https://edgeguides.rubyonrails.org/api_documentation_guidelines.html
- https://edgeapi.rubyonrails.org/
- https://api.rubyonrails.org/
- https://rubyonrails.org/

- rails new project_name
- bin/rails server
- rails s

- New in Rails 8 - https://guides.rubyonrails.org/8_0_release_notes.html

- Describe your recent work experience with RoR projects?

- Which Version of RoR you worked? 

- Latest Rails (8) and Ruby (3) version

- How to fix error like, "Address already in use - bind(2) for "127.0.0.1" port 3000 "
	- get running port job , or in this case check pid from ps aux | grep puma and kill it

- Why RoR called Convention over configuration, please explain

- Benefits, Features

- rails scaffolding -  that allows you to quickly generate a basic implementation of MVC. E.g rails generate scaffold <model name> User

- rails - help to get all commands

- sessions -  in session_store.rb ->  session_store :active_record_store or :file_store

- How to declare constructor -  by using initialize() method.

- New in RoR 7/8 -  what is where.associated , where.missing 

- Performance improvement (cache, db level of indexes)
---------------------------------------------------------------------------------------------------------------------
### routes qs?
---------------------------------------------------------------------------------------------------------------------
- https://guides.rubyonrails.org/routing.html

- bin/rails routes OR rails routes to get list of existing routes 
	- alternate way to get same result from URL -> http://localhost:3000/rails/info/routes

- Singular Resources
Sometimes, you have a resource that users expect to have only one (i.e. it does not make sense to have an index action to list all values of that resource). In that case, you can use resource (singular) instead of resources.
	```ruby
	Rails.application.routes.draw do
  		resource :profile
	end
	```
- With ref of Singular Resources -> What is the difference between resources and resource?
	- resources: generates routes for a collection of resources (e.g., /articles, /articles/:id).
	- resource: generates routes for a singular resource (e.g., /profile), omitting routes like index.

- How to define custom routes -  by using match or get method

- Nested Routes - How can we create URL Like, /posts/:post_id/comments/:id.
	```ruby
		resources :posts do
			resources :comments
		end
	```
- Difference between member and collection routes?
	- Member routes operate on a single resource (e.g., /articles/:id/publish).
	- Collection routes operate on a group of resources (e.g., /articles/search).

- How can you handle API versioning in Rails routes?
	- Path based Versioning
	```ruby
	namespace :api do
		namespace :v1 do
			resources :articles
		end
	end
	```
	- Header-based Versioning
	- Subdomain-based Versioning
		- e.g., v1.api.mysite.com
		```ruby
		constraints subdomain: 'v1' do
			namespace :api do
				resources :articles
			end
		end

		# OR
		
		 Rails.application.routes.draw do
			namespace :api, constraints: { subdomain: 'v1.api' } do
				namespace :v1 do
				resources :articles
				resources :orders
				end
			end
		end

		```
	- Additional Tools and Gems
		- Versionist: A gem to manage API versioning with multiple strategies.
		- RocketPants: Provides built-in API versioning.
		- Grape: A framework that simplifies API versioning.	```

- What are shallow routes, and why use them?
	- Shallow routes simplify nested routes by limiting how deep the nesting goes:
	```ruby
		resources :articles do
			resources :comments, shallow: true
		end
	```
	- This generates routes like /articles/:id/comments and /comments/:id.

- What is the purpose of route globbing?
	- In Rails, route globbing is implemented using the * wildcard in the route path. The * captures all remaining segments of a URL as a single parameter, which can then be processed in the controller
	```ruby
	# config/routes.rb
	get 'photos/*other', to: 'photos#show'

	# app/controllers/photos_controller.rb
	class PhotosController < ApplicationController
		def show
			render plain: "Requested path: #{params[:other]}"
		end
	end

	Advantages of Route Globbing
	Flexibility: Easily handle dynamic paths and nested resources.
	Simplicity: Reduces the need for multiple specific routes.
	Error Handling: Ensures unmatched routes can be directed to a custom action.
	Limitations
	Routes with globbing are less specific and can overlap with other defined routes.  <br/>Place them at the end of the routing file to avoid conflicts.
	Overuse can lead to harder-to-maintain routes, as they bypass RESTful conventions.
	By carefully using route globbing, you can create powerful and dynamic routing patterns in Rails applications.
	```
- namespace and scope routing diff
	- namespace used to group related routing, grouping routes with separate controllers like admin, API versioning etc
	- scope is more flexible with change URL prefix WITHOUT enforcing directory structure. 
	
- What is route constraints -  to restrict or filter which routes are matched based on condition. Constraints are like matching patterns. 

- Break large Route file with draw() micro
	```ruby
		Rails.application.routes.draw do
  			get "foo", to: "foo#bar"
  			draw(:admin) # Will load another route file located in `config/routes/admin.rb`
		end
		# config/routes/admin.rb
		namespace :admin do
  			resources :comments
		end
	```
---------------------------------------------------------------------------------------------------------------------
### Security technique
---------------------------------------------------------------------------------------------------------------------
- How to fix Cross origin request blocked error. 
- Use Rack CORS (gem "rack-cors") for handling Cross-Origin Resource Sharing (CORS), making cross-origin Ajax possible
	- Gem update, secure config (env var), strong param filters, SQL injection, Authentication, Authorization, CSRF protection, <br/>secure file upload, logging and monitoring, use Https, Secure headers, rate limit with Rale Attack -  rack-attack gem, secure  <br/>API with jwt, oAuth2.
	
- SQL Injection 
```ruby
# Vulnerable
Product.where("title = '#{params[:title]}'")

# Safe
Product.where(title: params[:title])
```

- gem to prevent SQL injection
	- Brakeman
		- Brakeman is a static analysis security tool for Ruby on Rails applications. It helps developers identify vulnerabilities, including SQL injection, cross-site scripting (XSS), and others, during development.
		![alt text](<images/brakeman.png>)
	- bullet


-  CSRF token bt not for all pages, how to exclude 
	- Enable CSRF Protection: Rails enables CSRF protection by default using the protect_from_forgery method.
	- Disabling Protection: For API-only applications or specific controllers, you can disable CSRF protection using skip_before_action.

- How validation works (apply callback)

- What is strong parameter (handle parameter filters in controller -  only allowed defined params -  security practice) 

- How to prevent web attack

- Exception handling (rescue_from method, begin-rescue-end, custom err page, use gems like Sentry, Rollbar, Honeybadger)

- Debugging (rails console, debugging views, log, exception handling, UT,  <br/>Profiling tools like Rake Mini Profile, Bullet (for n+1 query), byebg gem)
- directory traversal attacks in file uploads
	- A directory traversal attack exploits improper handling of file paths in applications, allowing attackers to access restricted directories and files on the server. 
	- E.g
	```ruby
	class UploadsController < ApplicationController
		def create
			uploaded_file = params[:file]
			file_path = Rails.root.join('public', 'uploads', uploaded_file.original_filename)

			# Save file to the constructed path
			File.open(file_path, 'wb') do |file|
			file.write(uploaded_file.read)
			end

			render plain: "File uploaded successfully"
		end
	end

	# An attacker can manipulate the original_filename parameter to include ../ sequences, which can traverse directories outside the intended uploads directory
	```
	- Preventing Directory Traversal Attacks in File Uploads
		- Instead of manually handling file uploads, use established gems like ActiveStorage or CarrierWave
		- Validate and Sanitize File Names
			- 
			```ruby
			# Remove any directory paths and disallowed characters
  			File.basename(filename).gsub(/[^\w\.\-]/, '_')
			```
		- Restrict Upload Directory
		- Validate File Content and Size, Type, Permissions 
		- Log file uploads and monitor for suspicious activity.

- Security related gems
    - ![alt text](<images/security-gems.png>)
- gems for Static code check
	- combine these tools in your CI/CD pipeline to ensure code quality, security, and maintainability.
	```ruby
	rubocop .
	reek .
	brakeman
	fasterer .
	rubycritic .
	```

---------------------------------------------------------------------------------------------------------------------
### Performance technique
---------------------------------------------------------------------------------------------------------------------
- How would you scale a Rails application to handle high traffic?
	- Database Scaling:
		- Use read replicas or sharding.
		- Optimize database queries and add indexing.
	- Horizontal Scaling:
		- Deploy multiple app server instances behind a load balancer.
	- Caching:
		- Implement Redis-based caching for views, queries, and sessions.
	- Asynchronous Jobs:
		- Use background workers for time-intensive tasks.
	- CDN:
		- Offload static assets to a Content Delivery Network (CDN).

- How can you measure the performance of a Rails application?
	- Tools:
		- rack-mini-profiler: For real-time performance profiling.
		- bullet: Detects N+1 queries and unused eager loading.
		- New Relic or Skylight: Application performance monitoring (APM) tools.
		- derailed_benchmarks: Benchmarks memory usage and startup time.
	- Logs:
		- Analyze Rails logs for slow queries and requests.
		- Use lograge for structured logging.

- Lograge
	- Lograge is a gem in Ruby on Rails designed to simplify and improve logging for production environments by providing single-line, <br/> structured logs. It replaces Rails' default multi-line request logs with concise, JSON-like entries, which are easier to parse <br/> and analyze, especially in distributed systems or when using log management tools like Elasticsearch, Splunk, or Datadog.

	```ruby
	Rails.application.configure do
		config.lograge.enabled = true
		config.lograge.formatter = Lograge::Formatters::Json.new
	end
	```
	- Use Cases
		- Production Logging: Ideal for high-traffic environments where performance and log size matter.
		- Distributed Systems: Easier correlation and analysis with centralized log management tools.
		- Monitoring and Alerting: Structured logs can trigger alerts in tools like Datadog or CloudWatch.

- size vs count vs length
	- Use size:
		- When you need a quick check on the collection's size, and the collection might already be loaded.
	- Use count:
		- When you're querying large datasets or want to avoid loading all objects into memory.
	- Use length:
		- When the collection is guaranteed to be small or already fully loaded into memory.
	- Which one is more efficient?
		- count is most efficient if you only need the number of records and don’t need to load them into memory.
		![alt text](<images/size-vs-count-vs-length.png>)
		
- Turbolink
	- Turbolinks in Rails serve the purpose of making page transitions faster. They achieve this by loading the body of a page without a full reload. This process reduces the time taken to navigate between pages. Turbolinks enhances web application speed by using JavaScript to replace the page content. This method conserves server resources and improves user experience.

- Database scalling
	- Indexing: Create indexes on frequently searched columns to speed up query times. Rails supports adding indexes directly through migrations.
    - Caching: Use Rails caching mechanisms, like page, action, or fragment caching, to reduce database load by storing and serving repeated requests.
    - Read replicas: Deploy read replicas of the database to distribute read queries, thus reducing the load on the primary database.
    - Database partitioning: Partition large tables into smaller, more manageable pieces to improve query performance and maintenance.
    - Background jobs: Move heavy computations or non-critical database operations to background jobs using Sidekiq or Delayed Job, to keep the web request cycle as fast as possible.
    - Connection pooling: Manage database connections efficiently to reduce the overhead of establishing connections frequently.
    - Monitoring and tuning: Regularly monitor database performance using tools like New Relic or pgBadger, and tune queries and indexes based on insights.
    - Selective loading: Use select, joins, and includes wisely to load only the necessary data, avoiding N+1 queries problem.

- Memory management

	- Employ techniques like ActiveRecord Query Optimization, caching strategies, and proper association loading.
	- Leverage tools such as Rack Mini Profiler to identify and address memory bottlenecks. 
	- Regularly monitor and analyze memory usage using tools like New Relic or Scout. 
	- Implement connection pooling to efficiently manage database connections and reduce memory overhead. 
	- Regularly review and optimize code to minimize unnecessary object instantiation and enhance garbage collection efficiency.

- Performance improvement gems
	- bullet -  Detects N+1 queries and unused eager loading in ActiveRecord
	- rack-mini-profiler - Provides performance profiling for each request, including database queries, view rendering, and more
	- oj (Optimized JSON) -  A high-performance JSON parser and serializer.
	- dalli - High-performance Memcached client for Rails
	- redis-rails - Redis cache store for Rails applications
	- sidekiq - A background job processor
	- puma -  A high-performance, multithreaded web server for Ruby/Rails.
	- bootsnap - Speeds up boot times for Rails applications.
	- rack-cache - Middleware for HTTP caching
	- ![alt text](<images/performance-gems.png>)
	- 

---------------------------------------------------------------------------------------------------------------------
### Metaprogamming 
---------------------------------------------------------------------------------------------------------------------

	- Ruby has strong metaprogramming capabilities that allowing developers to write code that writes code.  <br/>Means,  writing code that can create or modify methods classes at runtime
	```ruby
	class Dynamic
		["greet", "farewell"].each do |method_name|
			define_method(method_name) do
			"This is #{method_name}"
			end
		end
	end

	dynamic = Dynamic.new
	puts dynamic.greet    # Output: "This is greet"
	puts dynamic.farewell # Output: "This is farewell"

	```
-  Explain the use of class_eval and module_eval
	- class_eval and module_eval are used to add methods or modify behavior at runtime
	- 
	```ruby
	class User; end
	User.class_eval do
		def greet
			"Hello from #{self.class}!"
		end
	end

	puts User.new.greet  # Output: "Hello from User!"

	module Greeter end;

	# Dynamically defining a method using module_eval
	Greeter.module_eval do
		def say_hello(name)
			"Hello, #{name}!"
		end
	end

	# Including the module in a class
	class Person
		include Greeter
	end

	person = Person.new
	puts person.say_hello("Alice")  # Output: Hello, Alice!
	```
- Difference between class << self and self.method
	- class << self: Opens the singleton class to define methods for a single object or class.
	- self.method: Defines or calls a class method
	- 
	```ruby
	class Example
		class << self
			def singleton_method
			"I'm a singleton method!"
			end
		end

		def self.class_method
			"I'm a class method!"
		end
	end

	puts Example.singleton_method  # Output: "I'm a singleton method!"
	puts Example.class_method      # Output: "I'm a class method!"
	```
- What are the advantages and disadvantages of metaprogramming?
	- Advantages:
		- Reduces boilerplate code.
		- Enhances flexibility and abstraction.
		- Powers Rails' dynamic features.
	- Disadvantages:
		- Can make code harder to read and debug.
		- Introduces runtime errors if not carefully implemented.
		- Can impact performance due to runtime evaluations.
- How would you debug a metaprogramming-related issue?
	- Use puts, pry, or byebug to inspect runtime behavior.
- When should you avoid using metaprogramming?
	- When code clarity and performance are critical

- How sessions works (config/initializers/session_store.rb -  active_record_store / file_store)

- How to implement Authentication and Authorization

---------------------------------------------------------------------------------------------------------------------
### Active Record Qs?
---------------------------------------------------------------------------------------------------------------------

- Explain Active Record and Active Record Association https://guides.rubyonrails.org/association_basics.html 
	- Represent models and their data.
	- Represent associations between models.
	- Represent inheritance hierarchies through related models.
	- Validate models before they get persisted to the database.
	- Perform database operations in an object-oriented fashion.

- Save Vs Update method
	- save(): If the object is new (i.e., not yet persisted), save will insert a new record in the database.<br/>If the object has been loaded from the database and modified, save will update the existing record.
	- update(): It works only with records that already exist in the database. It cannot be used to create a new record.

- Composite Primary key and how to find records 
	- create_table :products, primary_key: [:store_id, :sku] do |t|
	- Find the product with store_id 3 and sku "XYZ12345"
	- product = Product.find([3, "XYZ12345"])

- execute method - How to run SQL commands like update in migration script?
	-  use the execute method to execute SQL commands.
		```ruby
		class UpdateProductPrices < ActiveRecord::Migration[8.0]
  			def up
    			execute "UPDATE products SET price = 'free'"
  			end

  			def down
    			execute "UPDATE products SET price = 'original_price' WHERE price = 'free';"
  			end
		end
		```
- reversible
	- reversible is useful when executing raw SQL queries or performing database operations that do not have a direct equivalent in ActiveRecord methods. <br/>You can use reversible to specify what to do when running a migration (up) and what else to do when reverting (down) it.
		
- Run specific migration: - bin/rails db:migrate VERSION=20240428000000 / bin/rails db:migrate:up VERSION=20240428000000
- Rollback: bin/rails db:rollback
- UUIs instead of PK
	- In distributed systems or when integration with external services is necessary. UUIDs provide a globally unique identifier without relying on a centralized authority for generating IDs.
	- In your Rails application configuration file (config/application.rb), add the following line to configure Rails to generate UUIDs as primary keys by default:
		```ruby
		config.generators do |g|
			g.orm :active_record, primary_key_type: :uuid
		end

		```
- maintenance_tasks - To perform data migrations separately (gem 'maintenance_tasks')
	- https://github.com/Shopify/maintenance_tasks

	- Data migrations involve transforming or moving data within your database. In Rails, it is generally not advised to perform data migrations using migration files. Here’s why:

		- Separation of Concerns: Schema changes and data changes have different lifecycles and purposes. Schema changes alter the structure of your database, while data changes alter the content.
		- Rollback Complexity: Data migrations can be hard to rollback safely and predictably.
		- Performance: Data migrations can take a long time to run and may lock your tables, affecting application performance and availability.
	- Instead, consider using the maintenance_tasks gem. This gem provides a framework for creating and managing data migrations and other maintenance tasks in a way that is safe and easy to manage without interfering with schema migrations.

-  includes and join difference - includes vs joins
	- includes and joins are both used to load associated records
	
		1. joins
		Purpose: Used to fetch associated records by performing a SQL JOIN between tables.  <br/>It only loads the data you explicitly select (or all columns by default) and does not eager-load associated records into memory.

		When to Use: Use joins when you want to query based on associated records  <br/>(e.g., filtering results by a condition in a related table) but don’t necessarily need to access the associated records later in your code.

		Example:

		- Get all users who have at least one post
		User.joins(:posts).where(posts: { published: true })
		This generates an SQL INNER JOIN, so it only returns users who have published posts. joins is generally faster for querying large datasets because it doesn’t load associated records into memory.

		Output: joins does not retrieve associated records in a way that allows them to be accessed directly.  <br/>If you try to access user.posts in this case, it will trigger an additional query.

		2. includes
		Purpose: Used for eager loading associated records to avoid the N+1 query problem.  <br/>It fetches the associated records in separate queries (using WHERE IN clauses) or performs a LEFT OUTER JOIN if you use where on the associated table.

		When to Use: Use includes when you need to access associated records after the initial query, as it loads the records into memory, preventing additional queries.

		Example:

		- Eager load posts for each user
		users = User.includes(:posts)

		- Access associated posts without extra queries
		```ruby
		users.each do |user|
			user.posts.each do |post|
				puts post.title
			end
		end
		```
		Here, includes loads User records and then performs a second query to fetch all related Post records.  <br/>This way, user.posts will not trigger additional queries, as the associated records are already loaded.

		Output: includes eager-loads associated records, so accessing user.posts won’t trigger additional queries.

		In short:

		Use joins for filtering and conditions on associated records without needing to access them directly.
		Use includes when you need to load associated records to avoid repeated queries (N+1 issue) when accessing associations.
		
- What is eager loading, and how does it improve performance?
	- Eager loading reduces database queries by loading associated records in a single query.
	- ```ruby posts = Post.includes(:comments).all ```

- How to override <i>default_scope</i>
	- The unscoped method ignores all default_scope settings for the current query.
	```ruby
	class User < ApplicationRecord
		default_scope { where(active: true) }
	end

	# Without overriding default_scope
	User.all
	# Output: SELECT "users".* FROM "users" WHERE "users"."active" = 1

	# Overriding default_scope with unscoped
	User.unscoped.all
	# Output: SELECT "users".* FROM "users"
	```

- How to do a migrations (files stored in db/migrate, rails generate migration --ModelName, rails db:migrate, 

- How to <b>disable database migration</b> tasks → <i>database_tasks : false</i> to set it up in prod env to prevent any possible changes in PROD env. 
	```ruby
	# config/application.rb or in specific environment files (e.g., config/environments/production.rb)
	Rails.application.configure do
		config.active_record.database_tasks = false
	end
	```
- How are scopes different from class methods?
	- Scopes always return an ActiveRecord::Relation, enabling chaining.
	- Class methods may return other types, like arrays or booleans.

- destroy Vs delete
	- When you call destroy, Rails ensures that all associated callbacks (like before_destroy) and validations are run before deleting the record
		- If callbacks or validations prevent destruction, the record won't be deleted. <i>record.destroy</i>
	- calling delete directly removes the record from the database without running callbacks or validations: <i>record.delete</i>

- custom query for complex logic
	- Using Arel for complex queries
	```ruby
		users = User.arel_table
		query = users.project(users[:id]).where(users[:age].gt(30))
		User.find_by_sql(query.to_sql)

	```
	- Using ActiveRecord::Base.connection for raw SQL
	```ruby
	ActiveRecord::Base.connection.select_all("SELECT id FROM users WHERE age > 30")
	```
- What are the potential downsides of polymorphic associations?
	- They can make queries complex and less efficient.
	- Difficult to enforce referential integrity in the database
	
- union and subquery 
	- union
		```ruby
		#Using RAW SQL:
		query1 = User.select(:id, :name).where(active: true).to_sql
		query2 = User.select(:id, :name).where(admin: true).to_sql

		union_query = "#{query1} UNION #{query2}"

		result = ActiveRecord::Base.connection.execute(union_query)

		# Using Arel:
		users = User.arel_table

		query1 = users.project(users[:id], users[:name]).where(users[:active].eq(true))
		query2 = users.project(users[:id], users[:name]).where(users[:admin].eq(true))

		union_query = query1.union(query2)

		result = ActiveRecord::Base.connection.execute(union_query.to_sql)
		```
	- subquery
		```ruby
		subquery = Order.select(:user_id).distinct
		users_with_orders = User.where(id: subquery)
		```

- Since Rails doesn't know which database is the replica for your writer you will need to add this to the abstract class after you're done.
	```ruby
	class AnimalsRecord < ApplicationRecord
	self.abstract_class = true

	connects_to database: { writing: :animals }
	end
	```

- If we wanted to use MongoDB then what command we should use to create project -  rails new <project_name> --skip-active-record (As Mongo does not support traditional RDBMS ActiveRecord)

- Explain the Single Table Inheritance (STI) pattern in Rails and when to use it.
		- Single Table Inheritance (STI) is a design pattern where multiple classes inherit from a single model/table. 
		- It’s implemented by having a type column in the table to differentiate between subclasses.
		- Example:
		```ruby
			# app/models/employee.rb
			class Employee < ApplicationRecord
			# app/models/manager.rb
			class Manager < Employee
			# app/models/developer.rb
			class Developer < Employee

			rails generate migration CreateEmployees name:string salary:decimal type:string

		```
		- Summary
			- Single Table Inheritance is ideal when you have models that share common attributes but also require some specific behaviors. 
			- It’s effective in reducing database complexity for these related classes. However, if subclasses have many unique attributes, 
			consider Polymorphic Associations or Class Table Inheritance (which Rails doesn’t support natively) as alternatives.
- What is scope
	- a scope is a way to define reusable, query-building methods on an ActiveRecord model. 
	- scope :name_of_scope, -> { query }
	- scope :active, -> { where(active: true) }
	- Usage: Product.active
	- Scopes can be chained together to refine queries:
		- Product.active.priced_above(50)

- What is scope and default_scope
	- Scope: define reusable query on Active Record Model. scope :active, -> { where(active: true) } -  Usage -  Product.active -  which fetch all active records
	- default_scope: is used to apply default query condition on Active Record model. Useful to get common filters like fetch only active records, active status etc. default_scope { where(published: true) } -  Usage Product.all -  fetch published products only
- scope vs class method
	- While both scopes and class methods can be used to define reusable queries, scopes are slightly more expressive and chainable by default. Rails will automatically handle scopes as ActiveRecord::Relation objects, making them easily combinable with other queries.

	- In general, use scopes for simple, reusable query fragments, and class methods for more complex logic or when you need parameters or additional custom behavior.

	- A scope is a way to define commonly used queries as methods in your models. Scopes are chainable, and they are intended for filtering ActiveRecord queries in an elegant and declarative manner.
	- A class method is a standard Ruby method defined on the model class. It can perform any kind of logic, not just queries, and is more flexible than a scope.
	- ![alt text](<images/scope-vs-class-method.png>)
	- When to Use Scope
		- When the logic is simple and reusable.
		- For defining query filters that return ActiveRecord::Relation.
		- To keep the code declarative and chainable.
		```ruby
		scope :verified, -> { where(verified: true) }
		```
	- When to Use Class Methods
		- When the logic is more complex than just a query.
		- When you need to perform operations that don’t necessarily return ActiveRecord::Relation.
		- For non-query-related functionality or computations
		```ruby
		def self.total_revenue
			sum(:revenue)
		end
		```

- Example of Polymorphic Association - https://guides.rubyonrails.org/association_basics.html#polymorphic-associations

---------------------------------------------------------------------------------------------------------------------
### Active Record Associations
---------------------------------------------------------------------------------------------------------------------

- https://guides.rubyonrails.org/association_basics.html

- How would you implement a polymorphic association in Rails?
	-	A polymorphic association allows a model to belong to more than one other model, using a single association. 
	-	For example, a Comment model could belong to either a Post or an Image.
	- Implementation:
		```ruby
		# Migration to add polymorphic fields
		create_table :comments do |t|
			t.text :content
			t.references :commentable, polymorphic: true, index: true
			t.timestamps
		end

		class Comment < ApplicationRecord
			belongs_to :commentable, polymorphic: true
		end

		class Post < ApplicationRecord
			has_many :comments, as: :commentable
		end

		class Image < ApplicationRecord
			has_many :comments, as: :commentable
		end	
		```
- Explain belongs_to and has_many (give example)
	- optional: true in belongs_to association
		- without optional: true - belongs_to :author - table must have not null value for author_id
		- with optional: true - belongs_to :author, optional: true - author_id column can be NULL

- belongs_to vs has_one
	- belongs_to: This association indicates that the current model contains the foreign key and is a child in the relationship.
	- has_one: This association indicates that the current model is the parent in the relationship.
	```ruby
	class Supplier < ApplicationRecord
		has_one :account
	end

	class Account < ApplicationRecord
		belongs_to :supplier
	end
	```

- diff between has_many :through has_and_belongs_to_many associations
	- has_many :through establishes a many-to-many relationship between models, allowing instances of one model (Physician) to be associated with multiple instances of another model (Patient) through a third "join" model (Appointment).
		![alt text](<images/has_many-through.png>)
	- The has_and_belongs_to_many association creates a many-to-many relationship with another model. In database terms, this associates two classes via an intermediate join table that includes foreign keys referring to each of the classes.
	- has_and_belongs_to_many (HABTM): Direct many-to-many association without an explicit join model. The join table exists only to associate the two models and does not have additional attributes.
		![alt text](<images/has_and_blongs_to_many.png>)
	- When to use which?
		- Use HABTM when there’s no need to store additional data in the join table.
		- Use has_many :through when the join table needs additional attributes or logic.
- Polymorphic associations - https://guides.rubyonrails.org/association_basics.html#polymorphic-associations
	- Polymorphic associations in Rails allow a model to belong to multiple other models through a single association. This can be particularly useful when you have a model that needs to be linked to different types of models.
	- 
	```ruby
		class Picture < ApplicationRecord
			belongs_to :imageable, polymorphic: true
		end

		class Employee < ApplicationRecord
			has_many :pictures, as: :imageable
		end

		class Product < ApplicationRecord
			has_many :pictures, as: :imageable
		end
	```
	- ![alt text](<images/polymorphic-association.png>)

- How do you handle circular dependencies in associations?
	- Circular dependencies occur when two models depend on each other in such a way that they cannot be loaded independently. Use optional: true for belongs_to to resolve.
- What happens if you define belongs_to but the foreign key is not present in the table?
	- Active Record will raise an error when you try to use the association because it cannot find the foreign key to establish the relationship.
	- Fix: Ensure the foreign key exists in the table or use the foreign_key option:
	- 
	```ruby
	class Comment < ApplicationRecord
		belongs_to :article, foreign_key: "post_id"
	end
	```
- How can you create conditional associations in Rails?
	- You can use the -> (lambda) syntax with conditions.
	- Only fetch active users in an association:
	- 
	```ruby
	class Team < ApplicationRecord
		has_many :members, -> { where(active: true) }
	end
	```
- What is inverse_of, and why is it used?
	- The inverse_of option tells Rails about the relationship between two models to avoid unnecessary database queries.
	- inverse_of is used to establish a bidirectional link between two related models, enabling Rails to reuse in-memory objects rather than fetching them from the database again. This is particularly useful when associations are modified and you want those changes to be reflected automatically in both directions.
	- 
	```ruby
	class Author < ApplicationRecord
		has_many :books, inverse_of: :author
	end

	class Book < ApplicationRecord
		belongs_to :author, inverse_of: :books
	end
	```
	- When you fetch an Author along with its Books and modify the Author object through one of its Books, Rails will recognize the in-memory Author object because of inverse_of.
	
- students , assignments associations with example
	- a student can have many assignments and an assignment belongs to a specific student.
	- 
	```ruby
	class Student < ApplicationRecord
		has_many :assignments, dependent: :destroy
	end
	
	class Assignment < ApplicationRecord
		belongs_to :student
	end
	```	
- Example of Self-join associations
	- A Category can have multiple sub-categories, and each sub-category can belong to a parent category. 
		- 
		```ruby
			class CreateCategories < ActiveRecord::Migration[6.1]
				def change
					create_table :categories do |t|
					t.string :name
					t.references :parent, foreign_key: { to_table: :categories }
					t.timestamps
					end
				end
			end
		```
		- A parent_id column is added to the categories table to reference itself.
		-
		```ruby
		# Self-join associations
		class Category < ApplicationRecord			
			belongs_to :parent, class_name: 'Category', optional: true
			has_many :subcategories, class_name: 'Category', foreign_key: 'parent_id'

			validates :name, presence: true
		end

		# Another example
		# Employee - Manager Self Join Association
		belongs_to :manager, class_name: 'Employee', optional: true
		has_many :subordinates, class_name: 'Employee', foreign_key: 'manager_id'

		# Creating employees
		manager = Employee.create(name: 'Alice')
		employee1 = Employee.create(name: 'Bob', manager: manager)
		employee2 = Employee.create(name: 'Charlie', manager: manager)

		# Accessing relationships
		manager.subordinates    # => [employee1, employee2]
		employee1.manager       # => manager (Alice)
		```
		- belongs_to :parent: Defines that a category can have a parent category.
		- has_many :subcategories: Defines that a category can have multiple sub-categories.
		- 
		```ruby
		# Create parent category
		electronics = Category.create(name: 'Electronics')

		# Create sub-categories
		mobiles = Category.create(name: 'Mobiles', parent: electronics)
		laptops = Category.create(name: 'Laptops', parent: electronics)

		# Add sub-categories to another sub-category
		smartphones = Category.create(name: 'Smartphones', parent: mobiles)

		# Access relationships
		electronics.subcategories    # => [mobiles, laptops]
		mobiles.subcategories        # => [smartphones]
		mobiles.parent               # => electronics
		smartphones.parent           # => mobiles

		```
- 

---------------------------------------------------------------------------------------------------------------------
### Active Support
---------------------------------------------------------------------------------------------------------------------

- https://guides.rubyonrails.org/active_support_core_extensions.html 
- Active Support is an extensive <b>collection of utility classes and standard Ruby library extensions</b> that are used in Rails, both by the core code and by your applications.

---------------------------------------------------------------------------------------------------------------------
### Active Jobs
---------------------------------------------------------------------------------------------------------------------

- https://guides.rubyonrails.org/active_job_basics.html
- Active Job is a framework for declaring jobs and making them run on a variety of queuing backends. <br/>These jobs can be everything from regularly scheduled clean-ups, to billing charges, to mailings. <br/>Anything that can be chopped up into small units of work and run in parallel.

- Create the Job
	```ruby
	#app/jobs
	bin/rails generate job guests_cleanup
	OR
	bin/rails generate job guests_cleanup --queue urgent

	```
- SideKiq Vs Resque
	- Sidekiq:
		- Multithreaded: Uses threads within a single process to handle multiple jobs simultaneously.
		- High performance due to concurrent job execution.
		- Suitable for applications with a large number of background jobs
		- Efficient memory usage due to threads sharing the same memory space.
		- E.g Sending Bulk email notification (does not required much CPU intensive calculations)
	- Resque:
		- Forks a new process for each job, leading to higher memory usage.
		- Single-threaded: Only processes one job per worker.
		- Performs better with CPU-intensive tasks where isolation is beneficial
		- Higher memory usage since each job runs in its own process
		- E.g Generate PDF invoice with lot of calculations (CPU intensive tasks)
---------------------------------------------------------------------------------------------------------------------
### Rake
---------------------------------------------------------------------------------------------------------------------

- https://guides.rubyonrails.org/rails_on_rack.html
- https://rack.github.io/
- http://railscasts.com/episodes/151-rack-middleware
- https://leahneukirchen.org/blog/archive/2007/02/introducing-rack.html
- Rake:
	- Rake (short for Ruby Make) is a Ruby-based build program included in Ruby on Rails. It allows developers to automate administrative <br/>tasks, such as database migrations, environment setup, and data seeding, through a series of "tasks" defined in Rakefiles. It's <br/>similar to make in Unix systems but tailored for Ruby projects.
- Key Features:
	- Task Automation: Automates repetitive tasks such as clearing logs, creating databases, and deploying code.
	- Custom Tasks: Developers can define their own tasks in a Rakefile or within Rails in the lib/tasks directory.
	- Namespace Support: Tasks can be grouped logically using namespaces.
	- Integration with Rails: Rake tasks often interact directly with the Rails application environment.
- Common Rake Tasks in Rails
	- rake db:create       # Creates the database
	- rake db:migrate      # Runs database migrations
	- rake db:seed         # Seeds the database with default data
	- rake db:rollback     # Rolls back the last database migration
- Advantages of Rake
	- Simplicity: Provides a concise way to automate tasks in Ruby applications.
	- Flexibility: Supports defining complex dependencies between tasks.
	- Rails Integration: Comes preloaded with Rails-specific tasks for database and application management.
- Rake Middleware
	- List down all middlware
		- bin/rails middleware OR rails middleware
	- Rack::Runtime: Logs the time taken to process a request
	- Rack::Cache: Provides HTTP caching
	- Rack::Attack: A tool for rate-limiting and request blocking
	- ActionDispatch::Static: Serves static assets
	- Rack::Cors: Handles Cross-Origin Resource Sharing (CORS)
	- Rack::Deflater: Compresses responses using gzip
- Alternatives to Rake
	- Although Rake is widely used, tools like Thor or Capistrano may be preferred for specific use cases such as deployment or more <br/>complex CLI task management.
- Rake Vs Rack
	- Rake: Rake (Ruby Make) is a Ruby-based task management tool that automates tasks such as database migrations, data imports, and testing. It is part of Rails and is used for defining and running tasks.
		- Key Features
			- Task Automation: Define tasks with dependencies in a Rakefile.
			- Common Use Cases:
			- Database migrations: rake db:migrate
			- Seeding data: rake db:seed
			- Running tests: rake test
			- Custom Tasks: Developers can define their own Rake tasks for project-specific requirements.
	- Rack: Rack is a modular interface between web servers and Ruby applications. Rails itself is built on top of Rack, which processes incoming HTTP requests and outgoing responses.
		- Key Features
			- Middleware Framework: Allows developers to insert middleware components in the request/response cycle.
			- Server Agnostic: Works with various web servers (e.g., Puma, WEBrick, Unicorn).
			- Custom Middleware: Define middleware to process requests, handle authentication, logging, or caching.

---------------------------------------------------------------------------------------------------------------------
### multi-threading
---------------------------------------------------------------------------------------------------------------------

- Ruby supports multi-threading
- GIL 
- libraries used for threading
	- Thread: Built-in Ruby class to create threads.
	- Concurrent-Ruby: Offers thread pools, actors, and other concurrency primitives.
	- Sidekiq: A background job processor leveraging threads for concurrent job execution.
	- Celluloid: Used for building concurrent Ruby applications.
- difference between threading and forking
	- Threading: Runs multiple threads in the same process, sharing memory.
	- Forking: Creates separate processes, each with its own memory space (e.g., Unicorn server).
- Thread Vs Background Job
	- Threading: Use threading for tasks directly related to web requests, like parallelizing multiple API calls within a single request, or managing real-time socket connections.
	- Background Jobs: Use for deferred tasks that don’t need to block the user, such as sending emails, bulk updates, or media processing.

	![alt text](<images/thread-vs-backgroundjob.png>)

---------------------------------------------------------------------------------------------------------------------
### OOPs 
---------------------------------------------------------------------------------------------------------------------

- By Default instance methods are private or public?
    - public by default, then only class instance will be able to access it.
- How to access private method from class object?
    - using send method => classObject.send(:private_method_name) Or for Ruby3+ classObject.__send__(:private_method_name)
- static method
	- create with <i>self.static_method</i>
	- create with <i>class << self</i>
	- static methods are implemented as class methods. These are methods that belong to the class itself, <i>not instances of the class</i>. To access a class method from a class object (i.e., the class itself), you simply call it on the class name.
	- Static methods are commonly used in Rails for utility functions or business logic that doesn't depend on instance variables. 
		```ruby
		class DateFormatter
			def self.format(date)
				date.strftime("%Y-%m-%d")
			end
		end

		# Usage
		formatted_date = DateFormatter.format(Date.today)
		puts formatted_date # Output: "2024-12-14" (current date in the format)
		```
		- When to Use Static Methods in Rails
			- Utility methods: Token generation, data parsing, etc.
			- Business logic: Calculations or operations that aren’t tied to a specific object instance.
			- Background jobs: Tasks like notifications or file processing.
			- Configuration: Application-wide settings and constants.

	- More static and private method access example.
		```ruby
		class Example
			class << self
				def static_method
				"I am static method!"
				end
			end
			private 
			def private_method
				"I am privat method!"
			end
		end

		# Call the method - Working
		puts Example.static_method # Output: "I am static method!"
		obj = Example # a reference to a class as an object, you can still call its class methods.
		puts Example.static_method # Output: "I am static method!"


		obj = Example.new
		puts obj.send(:private_method)

		# Error - class instance can't access static method
		puts obj.static_method
		puts obj.send(:static_method)
		```
- Singleton class	
	- In Ruby on Rails, a singleton class refers to the class of a single object, allowing you to define methods specific to that object without affecting other instances of the same class
		```ruby
		# Create a new User instance
		user = User.new
		user.name = "Alice"

		# Define a singleton method specific to this user instance
		class << user
			def greet
				"Hello, #{name}!"
			end
		end

		# Test the method
		puts user.greet # Output: "Hello, Alice!"

		# Another User instance does not have the `greet` method
		user2 = User.new
		user2.name = "Bob"
		# user2.greet # This will raise NoMethodError
		```

- Does Ruby supports multiple inheritance? 
	- Ruby doesn’t support multiple inheritance directly but uses modules (mixins) to share functionality across classes. Modules are included using the include or extend keywords
- How many ways we can achieve multiple inheritance?
	- Using Modules (Mixins)
	- Using Delegation
		- Delegation involves using one object to delegate specific methods to another object, effectively splitting behavior between classes.
		```ruby
		class Engine
			def start
				"Engine started"
			end
		end

		class Transmission
			def shift
				"Gear shifted"
			end
		end

		class Car
			def initialize
				@engine = Engine.new
				@transmission = Transmission.new
			end

			def start
				@engine.start
			end

			def shift
				@transmission.shift
			end
		end

		car = Car.new
		puts car.start # => "Engine started"
		puts car.shift # => "Gear shifted"
		```
	- Using forwardable Module
	```ruby
	require 'forwardable'

	class Car
		extend Forwardable

		def_delegators :@engine, :start
		def_delegators :@transmission, :shift

		def initialize
			@engine = Engine.new
			@transmission = Transmission.new
		end
	end
	```
	- Metaprogramming
		- Dynamic method creation using metaprogramming can simulate multiple inheritance by defining methods at runtime.
		```ruby
		module Greeting
			def say_hello
				"Hello!"
			end
		end

		module Farewell
			def say_goodbye
				"Goodbye!"
			end
		end

		class Person
			[Greeting, Farewell].each do |mod|
				include mod
			end
		end

		person = Person.new
		puts person.say_hello   # => "Hello!"
		puts person.say_goodbye # => "Goodbye!"
		```
	- Conclusion
		- Ruby provides flexible ways to achieve multiple inheritance-like behavior, such as modules, delegation, composition, and metaprogramming. Each approach has trade-offs, and the choice depends on the specific use case. While Rails often emphasizes simplicity and convention over configuration, these patterns can be used effectively to share behavior across your application.

- Problem of Multiple inheritance
	- Diamond problem
	- Increase complexity which leads difficult to debug
	- Conflict in Method (Same method in inherited parents)
- If Same Method in Multiple Included Modules, which one get precedence?
	- Ruby resolves method calls based on the order of inclusion. The module included last takes precedence over earlier ones.
	- If you use prepend instead of include, the prepended module takes higher precedence than the class itself.
	- If the class defines a method with the same name, the method in the class takes precedence over methods in included modules.
	-  To call methods from modules or parent classes intentionally, use super in the overridden method.
	- You can inspect the method lookup path using the ancestors method on a class: <i>MyClass.ancestors</i>
- Static polymorphism
	- Static polymorphism, also known as compile-time polymorphism, is achieved through method overloading in programming languages. However, Ruby does not support traditional method overloading like languages such as Java or C++. Instead, Ruby achieves similar behavior using techniques such as default arguments or variable-length argument lists (*args), which mimic method overloading functionality.
	```ruby
	class DiscountCalculator
		def calculate(*args)
			case args.length
			when 1
			calculate_by_percentage(args[0])  # Single argument: percentage
			when 2
			calculate_by_fixed_discount(args[0], args[1])  # Two arguments: fixed discount and price
			else
			raise ArgumentError, "Invalid arguments"
			end
		end

		private

		def calculate_by_percentage(percentage)
			"Calculating discount as #{percentage}%"
		end

		def calculate_by_fixed_discount(discount, price)
			"Calculating discount of #{discount} on price #{price}"
		end
	end

	# Usage
	calculator = DiscountCalculator.new
	puts calculator.calculate(10)             # Output: "Calculating discount as 10%"
	puts calculator.calculate(20, 100)        # Output: "Calculating discount of 20 on price 100"
	```
	- Key Points
		- Ruby doesn’t use compile-time method overloading; instead, it provides flexibility through dynamic argument handling.
		- This approach aligns with Ruby’s philosophy of simplicity and the principle of <i>"duck typing."</i>

- Dynamic polymorphism
	- Dynamic polymorphism, also known as runtime polymorphism, is achieved in Ruby (and Rails) through method overriding. In Ruby, dynamic polymorphism allows a subclass to provide a specific implementation of a method that is already defined in its superclass or included module. This is especially useful in Rails when defining behavior for models, controllers, or services.
	- Suppose you are building an e-commerce application where different types of products calculate their price differently. This can be achieved using inheritance and method overriding.
	```ruby
	# app/models/product.rb
	class Product < ApplicationRecord
		def calculate_price
			raise NotImplementedError, "Subclasses must define calculate_price"
		end
	end

	# app/models/book.rb
	class Book < Product
		def calculate_price
			base_price - (base_price * 0.1) # 10% discount for books
		end
	end

	# app/models/electronic.rb
	class Electronic < Product
		def calculate_price
			base_price + (base_price * 0.2) # 20% tax for electronics
		end
	end
	```
- Interface & Abstract class 
	- Ruby was designed to prioritize developer happiness and simplicity. Adding constructs like interfaces and abstract classes would introduce complexity and enforce strict structures that go against Ruby's philosophy of being flexible and developer-friendly. Instead, Ruby offers tools like modules and duck typing to achieve similar goals without enforcing rigid constraints.
	- These 2 lead tightly coupled code
---------------------------------------------------------------------------------------------------------------------
### Gems Qs?
---------------------------------------------------------------------------------------------------------------------

- Devise
	- For Authentication
	- Core Features of Devise:
		- Authentication: Provides login, logout, and registration functionality.
		- Password Management: Handles forgotten passwords, password reset functionality, and encrypted passwords.
		- Session Management: Manages user sessions, ensuring secure logins and logouts.
		- Confirmation: Provides email confirmation for account activation.
		- Recoverable: Allows users to reset their passwords via email.
		- Trackable: Tracks user sign-ins, including timestamps and IP addresses.
		- Lockable: Locks accounts after a specified number of failed sign-in attempts.
		- Timeoutable: Automatically logs out users after a period of inactivity.
		- Token-based Authentication: Useful for APIs and mobile app integration.
		- Customizable Models: Allows for extending the default user model to include additional attributes or logic.
- OmniAuth
	- OmniAuth is a Ruby gem that provides a standardized way to add third-party authentication (OAuth) to Ruby and Ruby on Rails applications. It simplifies the integration of various authentication providers such as Google, Facebook, GitHub, Twitter, and more.
- Pundit
	- For ABAC - Attribute-Based Access Control
	```ruby
	# app/policies/document_policy.rb
	class DocumentPolicy < ApplicationPolicy
		attr_reader :user, :document

		def initialize(user, document)
			@user = user
			@document = document
		end

		def view?
			# Allow access based on attributes
			if user.role == "admin"
			true
			elsif user.role == "manager" && user.department == document.department
			document.sensitivity_level.in?(["low", "medium"])
			elsif user.role == "employee" && user.department == document.department
			document.sensitivity_level == "low"
			else
			false
			end
		end

		def edit?
			# Define edit rules
			user.role == "admin" || (user.role == "manager" && user.department == document.department)
		end
	end

	```
	- In this policy:
		- Admins can view all documents.
		- Managers can view documents if the sensitivity_level is "low" or "medium" and it belongs to their department.
		- Employees can only view documents with "low" sensitivity that belong to their department.
		- Edit permissions are limited to admins and managers in the same department.

- RBAC - CanCanCan Vs Pundit
	- ![alt text](<images/cancancan-vs-pundit.png>)
- RBAC Vs ABAC
	- When to Use Each?
		- RBAC is ideal for simpler applications with predefined roles and straightforward permissions.
		- ABAC is better for complex applications where access decisions depend on multiple dynamic factors.
	- ![alt text](<images/rbac-vs-abac.png>)
	- 
- 
---------------------------------------------------------------------------------------------------------------------
### RESTFul API & Microservice Qs?
---------------------------------------------------------------------------------------------------------------------
- key features of REST?
	- Statelessness: Each request is independent, and the server does not store client context.
	- Cacheability: Responses can be marked as cacheable to improve performance.
	- Client-Server Architecture: Separation of concerns between client and server.
	- Uniform Interface: A standard way of interaction (resources accessed via URLs and standard HTTP methods).

- What is HATEOAS, and why is it important?
	- HATEOAS (Hypermedia as the Engine of Application State) ensures REST APIs include links in their responses for further actions. 
	- Example:
	```ruby
	{
		"user": {
			"id": 1,
			"name": "John Doe",
			"links": [
			{ "rel": "self", "href": "/users/1" },
			{ "rel": "orders", "href": "/users/1/orders" }
			]
		}
	}
	```
- rate-limiting in RESTful APIs?
	- Token bucket algorithms.
	- HTTP headers (e.g., X-RateLimit-Limit, X-RateLimit-Remaining) to inform clients about limits

---------------------------------------------------------------------------------------------------------------------
### Design Patterns
---------------------------------------------------------------------------------------------------------------------
- Creational
	- Creational design patterns provide various object creation mechanisms, which increase flexibility and reuse of existing code.
	- Singlton
	- Factory
	- Abstract Factory
	- Prototype
- Structural
	- Structural design patterns explain how to assemble objects and classes into larger structures, while keeping these structures flexible and efficient.
	- Adaptor
	- Bridge
	- Decorator
	- Facade
	- Flyweight
	- Proxy
- Behavioral
	- Behavioral design patterns are concerned with algorithms and the assignment of responsibilities between objects.
	- Chain of Responsibility
	- Command
	- Observer
	- Strategy
	- Memento
- Singleton Pattern
	- https://www.rubyguides.com/2018/05/singleton-pattern-in-ruby/
	- The Singleton pattern ensures that a class has only one instance and provides a global point of access to it.
	```ruby
	class Configuration
		@instance = new

		private_class_method :new

		def self.instance
			@instance
		end

		def settings
			@settings ||= {}
		end
	end

	config = Configuration.instance
	config.settings[:app_name] = "MyApp"
	```

- Factory Pattern
	- The Factory Pattern in Ruby on Rails is often used to create objects without specifying the exact class or the full instantiation logic. This pattern is particularly useful when working with polymorphic relationships, varying initialization logic, or test data setup.
	```ruby
	class Notification
		def send_message
			raise NotImplementedError, "Subclasses must implement this method"
		end
	end

	class EmailNotification < Notification
		def send_message
			"Sending email notification"
		end
	end

	class SMSNotification < Notification
		def send_message
			"Sending SMS notification"
		end
	end

	class NotificationFactory
		def self.create(type)
			case type
			when :email
			EmailNotification.new
			when :sms
			SMSNotification.new
			else
			raise "Unknown notification type"
			end
		end
	end

	# Usage
	notification = NotificationFactory.create(:email)
	puts notification.send_message # Output: Sending email notification
	```
- Chain of Responsibility - https://github.com/mpatel2280/interview-prep/blob/master/ror-example.md#chain-of-responsibility
	- The Chain of Responsibility is a behavioral design pattern where multiple objects (handlers) are linked in a chain. Each handler processes a request or passes it down the chain. This allows dynamic request handling and reduces coupling between the sender and receiver.
- Decorator Pattern
	- The Decorator pattern is used to dynamically add behavior or responsibilities to an object.
	```ruby
	class Coffee
		def cost
			5
		end

		def description
			"Coffee"
		end
	end

	class MilkDecorator
		def initialize(coffee)
			@coffee = coffee
		end

		def cost
			@coffee.cost + 2
		end

		def description
			@coffee.description + " with Milk"
		end
	end

	coffee = Coffee.new
	milk_coffee = MilkDecorator.new(coffee)
	puts milk_coffee.cost         # 7
	puts milk_coffee.description  # Coffee with Milk
	```

---------------------------------------------------------------------------------------------------------------------
### Mixins and Concerns
---------------------------------------------------------------------------------------------------------------------

- What is Concerns
	- Concerns are a way to extract and organize reusable code in Rails. They are typically modules placed in the app/models/concerns or app/controllers/concerns directories. Concerns help in adhering to the Single Responsibility Principle by allowing developers to split functionalities into manageable and reusable modules.

-  What are the key components of a Concern?
	- Module Definition: Define a module with the reusable functionality.
	- included Block: Code that needs to run when the module is included.
	- Instance Methods: Methods added to the class where the Concern is included.
	- Class Methods (optional): Methods available on the class itself, defined using class_methods.

- What are the benefits of using Concerns in Ruby on Rails?
	- Reusability: Code can be shared across multiple models or controllers.
	- Separation of Concerns: Keeps code organized and adheres to the Single Responsibility Principle.
	- Readability: Simplifies complex models or controllers by moving related functionalities into separate modules.
	- Testability: Concerns can be tested independently.

- What are the potential pitfalls of using Concerns?
	- Overuse: Excessive use of Concerns can lead to scattered and hard-to-track code.
	- Hidden Dependencies: Concerns might rely on methods or attributes in the including class, making them harder to reuse.
	- Complexity: Mixing too many Concerns in a single class can lead to a lack of clarity.

- How do you include a Concern in a model or controller?
	- You include a Concern in a model or controller using the include keyword.
	```ruby
	# app/models/concerns/auditable.rb
	module Auditable
		extend ActiveSupport::Concern

		included do
			before_save :log_changes
		end

		def log_changes
			puts "Changes logged for #{self.class.name}"
		end
	end

	# app/models/user.rb
	class User < ApplicationRecord
	include Auditable
	end
	```
- What is ActiveSupport::Concern? Why is it preferred over plain Ruby modules?
	- ActiveSupport::Concern is a module provided by Rails to simplify the inclusion of modules into classes. It adds additional functionality, such as:
		- Automatically handling included blocks.
		- Simplifying dependency management within modules.

- Can Concerns be used in other Ruby frameworks?
	- Yes, Concerns are essentially Ruby modules and can be used in other frameworks. However, ActiveSupport::Concern is specific to Rails.

- What are some best practices for using Concerns in Rails?
	- Limit Responsibility: Ensure that each Concern handles only a single responsibility.
	- Avoid Excessive Coupling: Concerns should not depend heavily on the including class's internal structure.
	- Use ActiveSupport::Concern: Use ActiveSupport::Concern to handle dependencies and included blocks neatly.
	- Document: Clearly document the purpose of the Concern.

- Can you use callbacks in a Concern?
	- Yes, callbacks like before_save, after_create, etc., can be defined in a Concern using the included block.

- What’s the difference between include and extend in Concerns?
	- include: Adds instance methods to the including class.
	- extend: Adds class methods to the including class.

- Mixins
	- A Mixin is a module in Ruby that allows you to add shared functionality to classes. Mixins are used to achieve code reuse and are included in classes using the include or extend keywords. In Rails, Mixins are often used to share behavior among models, controllers, or other classes.

- How do Mixins differ from inheritance?
	- Mixins provide a way to include functionality into a class without using inheritance
	- Inheritance: A class can inherit from only one superclass (single inheritance).
	- Mixins: Allow a class to include multiple modules, thereby enabling multiple inheritances.

- How do you include a Mixin in a Ruby class?
	- You use the include keyword to add instance methods from a module to a class.

- Can you use Mixins to define callbacks in Rails?
	- Yes, Mixins can define callbacks for Rails models or controllers. You typically do this using ActiveSupport::Concern.

- How do Mixins support metaprogramming in Rails?
	- Mixins can dynamically define methods or add behavior to classes using Ruby's metaprogramming features.
	```ruby
	module DynamicMethods
		def define_method(name, &block)
			define_singleton_method(name, &block)
		end
	end

	class User
	extend DynamicMethods
	end

	User.define_method(:greet) { "Hello from metaprogramming!" }
	puts User.greet
	```

- How are Mixins different from Concerns in Rails?
	- Concerns are a specialized form of Mixins provided by Rails through ActiveSupport::Concern. They simplify dependency management and allow the use of included and class_methods blocks for better structure.


- Diff between Mixins and Concerns
	- mixins is module we can include or extend. Plain Ruby feature, that works outside rails too. 
	- concerns -  Specific to Rails. Which include module in class to execute callbacks before_actions etc in controller. 
		- If we want to use concerns outside the Rails framework then we need to include the activesupport gem.
		- <i>gem 'activesupport'</i>
		```ruby
		# app/models/concerns/auditable.rb
			module Auditable
				extend ActiveSupport::Concern

				included do
					before_save :audit_changes
				end

				def audit_changes
					raise NotImplementedError, "#{self.class} must implement audit_changes"
				end
			end

			# app/models/user.rb
			class User < ApplicationRecord
				include Auditable

				def audit_changes
					# Define audit logic specific to User
				end
			end
		```
	- Can we execute callbacks with mixins without using ActiveSupport::Concern?
		- using ActiveSupport::Concern
			```ruby
			module Auditable
				extend ActiveSupport::Concern

				included do
					before_save :log_before_save
					after_save :log_after_save
				end
				........
			end
			```
		- without using ActiveSupport::Concern
			```ruby
			module Auditable
				def self.included(base)
					base.before_save :log_before_save
					base.after_save :log_after_save
				end
				........
			end
			```
	- ![alt text](<images/mixins-vs-concerns.png>)
	- When to Choose
		- Use Mixins:
			- Place them in a dedicated folder, such as lib/mixins.
			- When you are creating reusable, general-purpose functionality not tied to Rails (e.g., logging, utilities, string manipulations).
			- If the code will be reused outside of Rails or across unrelated objects.
		- Use Concerns:
			- When working within a Rails application and sharing business/domain logic between models or controllers (e.g., validations, callbacks, or scopes).
			- When following Rails conventions for better organization and readability.
	- Best Practices
		- Avoid overusing mixins or concerns, as they can lead to tightly coupled and hard-to-debug code.
		- Keep mixins and concerns focused and small. Each should ideally serve a single purpose.
		- For concerns, prefer organizing them in app/models/concerns or app/controllers/concerns to maintain consistency.

- Diff include and extend and prepend
	- include: When you use include, the methods from the module are added as instance methods to the class. This means that objects created from the class can call the methods.
	- extend: When you use extend, the methods from the module are added as class methods to the class. This means that the class itself (not its instances) can call the methods.
	- prepend:  When you prepend a module to a class, the methods defined in the module take precedence over the methods defined in the class itself.

- How to create reusable / sharable functionality? using concerns, mixins 

---------------------------------------------------------------------------------------------------------------------
### Other Qs?
---------------------------------------------------------------------------------------------------------------------

- .. and ... operators
	- The exclusive range (...) excludes the end value.
		- (1...5)  # Represents 1, 2, 3, 4 (excluding 5)
	- The inclusive range (..) includes the end value.
		- (1..5)  # Represents 1, 2, 3, 4 (including 5)

- Comparable objects
	- to implement a comparable object (an object that can be compared with others using operators like <, <=, >, >=, ==, !=), you need to define the <=> operator, which is known as the spaceship operator.
	- Key Points:
		- <=> (Spaceship Operator): This operator is used to compare two objects. It returns:
			- -1 if the left object is less than the right one,
			- 0 if they are equal,
			- 1 if the left object is greater than the right one.

- super Vs super()
	- super passes the current method's arguments to the parent method.
	- super() does not pass any arguments, even if the current method has arguments.

- throw/catch Vs raise/rescue
	- throw/catch
		- Purpose: Handles non-local jumps in code, enabling early exits from deeply nested structures.
		- Usage: Used for control flow, not error handling.
		- Mechanism: throw jumps to a corresponding catch block identified by a matching symbol. If no matching catch is found, it raises an error.
	- raise/rescue
		- Purpose: Handles exceptions and errors in the program.
		- Usage: Used to signal and recover from error conditions.
		- Mechanism: raise generates an exception, and rescue blocks catch and handle the exception.

- What is dynamic finders
	- Dynamic finders in Ruby on Rails are a feature that provides a way to generate methods on the fly for querying the database. They allow developers to construct query methods dynamically based on the naming conventions and the attributes of the model. This approach makes querying the database more intuitive and concise.
		- user = User.find_by_email("<EMAIL>")
		- user = User.find_by_email_and_username("<EMAIL>", "john_doe")
		- user = User.find_or_create_by_email("<EMAIL>")

- Why eager_load set to false in development env?
	- eager_load set to false in development env and set true in production env
	- If eager_load is set to true, Rails would load all the application's classes and modules at the start, which defeats the purpose of class reloading in development because it would load everything once and not reload specific changes when files are updated. This would force you to restart the server after every code change, making the development experience less efficient.

	```ruby
	#config/environments/development.rb
	# Do not eager load code on boot.
  	config.eager_load = false

	#config/environments/production.rb
	config.eager_load = true
	```

- How to do a real-time communicate (Action Cable)

- debugging tools and gems
	- debug (Built-In Gem in Ruby 3.1+)
		-  A modern debugger included with Ruby 3.1 and above
	- bullet
		- Detects N+1 query problems and unused eager loading in Active Record
	- byebug
		- Set breakpoints in your code.
		- Inspect variables and application state at runtime.
		- Step through code execution line-by-line.
	- rack-mini-profiler
		- A profiler for Rails applications
	- pry
		- An advanced REPL (Read-Eval-Print Loop) for Ruby
	- pry-byebug
		- Combines the features of pry and byebug
	- better_errors
		- Provides detailed and interactive error pages
	- rails-footnotes
		- Adds debugging information directly into Rails views
	- stackprof
		- A fast, low-overhead profiler for Ruby applications
	- flamegraph
		- Visualizes performance bottlenecks with flame graphs
	- Choosing the Right Debugging Tool
		- Use byebug or pry for interactive debugging.
		- Use better_errors for enhanced error pages in development.
		- Use bullet to identify Active Record inefficiencies.
		- Use stackprof or flamegraph for performance profiling.

- middleware 
	- What is and how we can get list of middleware 
		- bin/rails middleware 

- prepend module 
	- When you prepend a module to a class, the methods defined in the module take precedence over the methods defined in the class itself. User.prepend(Loggable)

- Diff nil? Empty? And blank? -  Nil for Object, empty for arrays, hashes, string, blank for all

- Diff present? And any? (present check object neither nil nor empty, any check collection contains an element)

- Diff find and find_by (find based on id -  PK, find_by fetch based on matching condition)

- eql? Vs equal? -  eql to check object equality based on their value, equal check object identity where object having same instance.

- Object Comparison in Rails
	- Use == for general equality checks.
	- Use equal? when you need to ensure object identity.
	- Use eql? and hash when working with hashes or custom data structures.
	- Use <=> for sorting and comparison logic.
	- Customize == or <=> in Active Record models if needed to handle domain-specific equality logic.

- create vs create? (create save object and do not throw an err, manually we have to handle it, create? Raise exception if save failed)

- Find value in an array (included? Any? Find, detect, index, grep, member?)

- What is the purpose of - skip-active-record (useful when we used db which does not support / req Active Record like MongoDB)

- has_secure_password works with MongoDB (no ,its part of Active Record and which does not support by MongoDB, but we can use Bcrypt to achieve similar thing)

- API only app 

- What are some popular gems or libraries for building APIs in Rails (rails-api, grape, jbuilder, fast_jsonapi, devise, devise-jwt, doorkeeper, cancancan, pundit, versionist, sidekiq, 
resque, delay_jobs)

- SOLID principles with example in ROR
	- 1. Single Responsibility Principle (SRP)
		- Every class should have a single responsibility and reason to change
		- In Rails, it's easy to violate SRP by overloading models with business logic and complex methods. Instead, use service objects to keep your models clean.
	- 2. Open/Closed Principle (OCP)
		- Classes should be open for extension but closed for modification.
		- Using inheritance, modules, or design patterns like decorators, you can extend behavior without modifying existing code.
	- 3. Liskov Substitution Principle (LSP)
		- Objects of a superclass should be replaceable with objects of a subclass without altering the behavior.
		- LSP encourages the creation of classes that can act interchangeably. For example, all subclasses of a Notifier should be usable as replacements for each other.
	- 4. Interface Segregation Principle (ISP)
		- Clients should not be forced to depend on methods they do not use.
		- In Ruby, interfaces aren’t enforced, but you can achieve a similar effect by creating specialized modules rather than large, catch-all interfaces.
	- 5. Dependency Inversion Principle (DIP)
		- High-level modules should not depend on low-level modules but on abstractions.
		- Dependency Injection can be used in Rails by passing dependencies into initializers, making your code more flexible and easier to test.

- Service Object 

- Difference between include and extend module

- Scaffolding

- Gemfile, Rake

- Difference between class and module

- How would you declare and use a constructor (initialize method)

- How would you declare and use a destructor (def self.finalize method)
	```ruby
	#1 Simulate Destructor by manually calling cleanup method
	class Resource
		def initialize
			puts "Resource acquired"
		end

		def cleanup
			puts "Resource released"
		end
	end

	# Using the Resource
	resource = Resource.new
	# Manually calling cleanup when needed
	resource.cleanup

	#2 By ObjectSpace.define_finalizer
	class Resource
		def initialize
			puts "Resource initialized"
			ObjectSpace.define_finalizer(self, self.class.finalize)
		end

		def self.finalize
			proc { puts "Resource finalized" }
		end
	end

	resource = Resource.new
	# When the program ends or the object is garbage collected, "Resource finalized" is printed.
	```

- Difference between class and instance variable and global variable
	- Instance variable begins with — @
	- Class variables begin with — @@
	- Global variables begin with — $


- What is clousers
	-  a closure refers to a construct that combines a block of code and its binding, meaning the variables and context in which it was defined.
	- In Ruby, Blocks, procs, lambdas are clousers.
	- Use of Closures in Rails:
		- Active Record Scopes:
			-  scope :by_status, ->(status) { where(status: status) }
		- Callbacks: before_save or after_commit can use blocks (closures) to encapsulate code
			- before_save { puts "Saving user: #{self.name}" }
		- Routes: constraints and route blocks
			- constraints lambda { |req| req.env["REMOTE_ADDR"] == "127.0.0.1" } do

- Diff block vs Proc Vs lambda
	- Argument Handling:
		- Blocks: Do not enforce the number of arguments. Extra arguments are ignored, and missing arguments are set to nil.
		- Procs: Behave like blocks in terms of argument handling.
		- Lambdas: Strictly check the number of arguments. If the number of arguments does not match, an error is raised.
	- Return Behavior:
		- Blocks: Use return to exit from the method that yielded to the block.
		- Procs: If a return statement is used inside a Proc, it will return from the enclosing method where the Proc is defined.
		- Lambdas: Treat return as a return from the lambda itself, not the enclosing method.
	- Use Cases in Rails
		- Blocks: Commonly used with methods that need to execute some custom code, like iterators (each, map), ActiveRecord query methods (find_each), and Rails view helpers.
		- Procs: Useful when you need to store reusable code, pass code around as an object, or use as callback handlers in Rails models (e.g., before_save).
		- Lambdas: Often used for scopes in ActiveRecord, where strict argument checking and local return are beneficial.
	- Exmple
		```ruby
		# Using a block with an ActiveRecord method
		User.all.each do |user|
			puts user.name
		end

		# Using a Proc as a callback in a model
		class User < ApplicationRecord
			before_save Proc.new { puts "User is about to be saved" }
		end

		# Using a lambda for an ActiveRecord scope
		class User < ApplicationRecord
			scope :active, -> { where(active: true) }
		end

		# Lambda vs Proc
		l = ->(x) { x }
		l.call() # ArgumentError for lambda
		p = Proc.new { |x| x }
		p.call() # Returns nil for Proc

		```

- How to create 
	- Using attr_accessor, attr_reader, and attr_writer
		- attr_accessor: Creates both getter and setter methods for an instance variable.
		- attr_reader: Creates a getter method for an instance variable.
		- attr_writer: Creates a setter method for an instance variable.

- What is callback - In Ruby on Rails, filters (also known as callbacks) 
	- In Ruby on Rails, callbacks are methods that get called at certain points during an object's lifecycle, such as before or after saving, updating, or destroying a record.
	- List of callbacks
		- before_validation
		- after_validation
		- before_save
		- after_save
		- before_create
		- after_create
		- before_update
		- after_update
		- before_destroy
		- after_destroy
		- after_commit
		- after_rollback
		- skip_callback
		- before_action
		- after_action


- Relational callbacks
	- Relational callbacks are callbacks that rely on the presence or association of related models to trigger. They are typically used when you want to perform certain actions if related records exist or don’t exist.

	- Example: Relational Callback with before_destroy
	Suppose you have a Project model that has many Tasks, and you want to prevent a project from being destroyed if it still has tasks associated with it.
	```ruby
	class Project < ApplicationRecord
		has_many :tasks, dependent: :destroy

		before_destroy :ensure_no_tasks

		private

		def ensure_no_tasks
			if tasks.any?
			errors.add(:base, "Cannot delete project with tasks")
			throw :abort # Prevents the project from being destroyed
			end
		end
	end

	```

- Conditional callbacks
	- Conditional callbacks are callbacks that run only if a specific condition is met. Rails allows you to define conditions directly 	within callbacks using if and unless options, making it easy to control their execution.
	- Example: Conditional Callback with before_save
		Let’s say you have a User model and want to automatically set a user's status to "active" when they provide their email for the first time. This could be accomplished using a before_save callback with a condition.
	```ruby
	class User < ApplicationRecord
		before_save :set_active_status, if: :email_changed?

		private

		def set_active_status
			self.status = "active"
		end
	end

	```

- diff after_save vs after_commit -  

	- after_save: Runs after a record is saved but before the transaction completes. This callback may not run if the transaction fails.
	- after_commit: Runs only after the transaction is fully committed to the database, ensuring all data is persisted.

- How to create Jobs (Sidekiq, Resque)

- lazy loading vs eager loading
	- Lazy Loading: Rails will load the associated records only when they are accessed.
		- @posts = Post.all  (default behavior)
		- Can cause performance issues in terms of multiple database queries but is more memory-efficient as it loads data only when needed.
	- Eager Loading: Rails fetches the associated records in the same query or a small number of queries, reducing database roundtrips.
		- @posts = Post.includes(:comments) # Comments will be loaded in the same query
		- Helps in performance optimization when accessing associated data multiple times, but might lead to higher memory consumption due to loading extra data upfront

		![alt text](<images/lazy-loading-vs-eager-loading.png>)

- load_async
	- The load_async feature in Ruby on Rails (introduced in Rails 7) allows for database queries to be executed asynchronously, meaning the query will run in a background thread, and the result will be loaded when needed, improving the responsiveness of the application. 
	- The benefit of using load_async comes from offloading expensive or time-consuming queries to a separate thread.
	- When to Use load_async (and When Not To)
		- Good for:
			- Long-running queries that do not need immediate responses.
			- Scenarios where you want to maximize throughput by offloading work to background threads.
			- Non-blocking operations in high-traffic applications where multiple database calls can be made in parallel.
		- Avoid in:
			- load_async won’t be available on older Ruby versions (pre-Ruby 3.0)
			- Applications with high complexity where thread safety is a concern.
			- Small, fast queries where async execution adds unnecessary overhead.
			- Applications with sequential dependencies or limited thread resources.

- new query method -  where.associated (Inner join), where.missing (Left outer join)

- Explain boot process

- Rate limiting and Throttling

- Popular Gems -  Devise, Pundit, CanCanCan, SideKiq, ResQue, Rake-Attack, Bullet, Paperclip, CarrierWave, Bundler-Audit, Paranoia, 

- Explain autoload_paths and eager_load_paths

- Explain boot process
	- initialization -  loading config/environment.rb file, dev, test, prod load
	- load condig/application.rb to load other config settings
	- load config/routes.rb -  for routing
	- load app/controllers -  app/models -  app/helpers 
	- Request processing and Generate Response
	- 

- Multiple database connection -  Rails6+, database.yml with primary and secondary properties
In models using connects_to - 

- Multi-threading (Thread, concurrent-ruby-gem)

- Ractor (Ruby Actor) for concurrent programming 
	- Ractor is a feature introduced in Ruby 3.0 that allows you to write concurrent programs without thread-safety issues. 
	- Considerations When Using Ractors
		- Isolation: Each Ractor has its own memory space. Shared mutable objects between Ractors can lead to errors. Only immutable objects can be shared directly.

		- Communication: Ractors communicate by sending and receiving messages. You can use Ractor#send to send messages and Ractor#take or Ractor#receive to receive them.

		- Performance: Ractors can introduce overhead due to message passing. They are beneficial when you have tasks that can run independently in parallel and where the overhead of message passing is outweighed by the performance gain from parallel execution.

		- Thread Safety: While Ractors avoid many of the thread-safety issues of traditional threads, you still need to ensure that the code within each Ractor is thread-safe, especially when interacting with shared resources like databases.
	- Conclusion
		- Ractor is a powerful tool for parallelism in Ruby, and it can be integrated into Rails applications to perform concurrent tasks efficiently. However, careful consideration is needed to manage isolation, communication, and thread safety. Properly used, Ractors can help you leverage the full power of modern multi-core processors in your Rails applications.

- How to manage thread safety (avoid class variable, use thread safe db)

- GIL (Global Interpreter Lock)

- RVM (Ruby Version Manger)

- splat (*) operator -  used for dynamic no. of arguments

- Duck Typing -  What object can do, rather what they are. Associated with polymorphism and adaptability of RoR.
	- Key Principles of Duck Typing
		- Focus on Behavior, Not Class: In duck typing, what matters is that an object can respond to a method call or exhibit certain behavior, not the specific class of the object.
		- Flexibility: This allows for more flexible and reusable code, as objects of different classes can be used interchangeably if they implement the required methods.
		```ruby
		class Person
			attr_accessor :name
			# attr_accessor :name, :address, :birthday, :zodiac_sign

			def initialize(name)
				@name = name
			end
		end

		class Pet
			attr_accessor :name

			def initialize(name)
				@name = name
			end
		end

		def print_name(entity)
			puts "Name: #{entity.name}"
		end

		person = Person.new("Alice")
		pet = Pet.new("Fido")

		print_name(person)  # Output: Name: Alice
		print_name(pet)     # Output: Name: Fido

		```

- Symbols datatype
•	Immutability: Symbols are immutable, ensuring keys remain constant.
•	Memory Efficiency: Symbols are stored in memory only once.
•	Performance: Symbols offer faster lookup and comparison.
•	Semantic Clarity: Symbols indicate identifiers or keys clearly.
•	Rails Convention: Rails favors symbols for configuration and options.
Using symbols as hash keys in Ruby on Rails provides practical benefits in terms of memory efficiency, performance, readability, and consistency, making them the preferred choice in many scenarios.

- extract_options! 
	- To handle optional param -  rails internally used it with ActiveRecord and ActionController has_many :posts, dependent: :destroy. <br/>In the has_many method, Rails uses extract_options! to handle optional configurations like dependent: :destroy without <br/>requiring specific positional parameters for options.

- Open classes (ability to modify or extend existing class -  mainly core libraries and framework)
	- In Ruby on Rails (and Ruby in general), the concept of open classes refers to the ability to modify or extend existing classes at <br/>runtime. This means that you can add methods, change existing methods, or alter the behavior of built-in classes or your own <br/>classes. This feature is a powerful aspect of Ruby's flexibility and is commonly used in Rails for various purposes, including <br/>adding functionality to existing libraries and frameworks.
		- Characteristics of Open Classes
			- Dynamic Modification: Classes in Ruby can be opened and modified at any point in the program. This includes built-in classes like Array, String, or your own classes.
			- Enhancing Functionality: You can add methods to existing classes to enhance their functionality without altering the original source code.
			- Common in Rails: Rails makes extensive use of open classes to extend core classes and modules, allowing for greater flexibility in its design.

	```ruby
	class String
		def vowel_count
			self.scan(/[aeiou]/i).count
		end
	end

	puts "Hello".vowel_count # Output: 2
	puts "Ruby on Rails".vowel_count # Output: 5

	```
	- Conclusion
		- Open classes are a powerful feature in Ruby and Rails that allow you to modify and enhance classes dynamically. While they <br/>provide great flexibility, it’s essential to use them with caution to maintain code clarity and avoid potential issues in your <br/>application. When done responsibly, open classes can lead to cleaner, more expressive, and adaptable code.


- Close classes (non modifying existing class, instead create sub-class and then extend base class)
	- In Ruby on Rails (RoR) and Ruby in general, a closed class refers to the practice of not modifying or extending existing classes <br/>at runtime. Instead, closed classes involve creating new classes or modules that encapsulate functionality without altering the <br/>original class definitions. This approach adheres to principles such as encapsulation and prevents potential issues caused by <br/>modifying existing classes, which can lead to unexpected behavior and maintenance challenges.

	- Characteristics of Closed Classes
		- Encapsulation: Closed classes promote encapsulation by containing behavior and state within their own definitions without <br/>altering external classes.
		- Maintainability: By avoiding changes to existing classes, closed classes enhance code maintainability and readability, making <br/>it easier for other developers to understand the structure and behavior of the code.
		- Reduced Risk of Side Effects: Closed classes minimize the risk of introducing bugs in the codebase due to unintentional <br/>changes to existing functionality.

- refinements
	- Refinements in Ruby provide a way to apply changes to classes or modules in a scoped and controlled manner, avoiding the global impact of monkey patching. This allows you to modify or extend functionality of existing classes or modules in a localized context.
	- 
	```ruby
	module StringRefinements
		refine String do
			def shout
			upcase + "!"
			end
		end
	end

	# Without enabling the refinement
	puts "hello".respond_to?(:shout)  # Output: false

	# Enabling the refinement
	using StringRefinements

	puts "hello".shout  # Output: HELLO!

	```
	- ![alt text](<images/monkeypatching-vs-refinement.png>)

- Monkey Patching 
	- monkey patching is the practice of reopening and modifying existing classes or modules, typically from the Ruby core library or Rails framework, to add or alter their functionality. This can be powerful for customizing behavior but should be used with caution as it can lead to unexpected issues.
E.g add method in String class to check string is palindrome or not (which is not part of core String class)
	- 
	```ruby
	class String
		def palindrome?
			self == self.reverse
		end
	end

	puts "madam".palindrome? # => true
	puts "hello".palindrome? # => false

	```
	- Monkey Patching a Gem
		- If you use a gem and need to override one of its methods, you can reopen its classes to modify behavior. For instance, if you use the Devise gem and want to override a method in Devise::SessionsController, you can do this in an initializer.
		- 
		```ruby
		# config/initializers/custom_gem_patch.rb
		# For small customizations without altering the gem's source code directly.
		module GemModule
			class GemClass
				def new_method
				# Custom behavior
				end
			end
		end

		# config/initializers/devise_monkey_patch.rb
		Devise::SessionsController.class_eval do
			# Override or add custom functionality here
			def create
				super
				# Add custom logging or tracking after user sign-in
			end
		end

		```

- Monitor and ensure the heath of Rails application in Prod env.
	- Employ robust logging mechanisms, utilize application performance monitoring (APM) tools like New Relic or AppSignal, set up alerts for key metrics such as response times and error rates, conduct regular health checks, and implement a centralized error tracking system like Sentry to monitor and ensure the health of a Rails application in production. Additionally, leverage server monitoring tools like Nagios or Prometheus to keep tabs on system-level metrics, ensuring a proactive approach to addressing any issues that may arise.

- How do you managing and configuring Rails assets in a large-scale application
	- Managing and configuring Rails assets in a large-scale application involves leveraging the asset pipeline. This system organizes and processes assets, like stylesheets and JavaScript, to enhance efficiency. Developers specify asset dependencies and load order by using manifest files. Additionally, asset precompilation optimizes assets for production, reducing load times. Asset digests ensure cache-busting, preventing issues with outdated versions. 

	- Consider splitting assets based on functionality or modules. Proper configuration of asset paths, fingerprinting, and compression enhances performance and maintains a streamlined asset pipeline in large-scale Rails applications.

- What are your strategies for code refactoring in a mature Rails codebase?
	- Begin by conducting a comprehensive code analysis to identify areas for improvement. 
	- Prioritize refactoring based on code complexity and potential impact on performance. 
	- Employ incremental refactoring techniques to minimize risks and maintain system stability. 
	- Leverage automated testing extensively to ensure that refactored code remains functional. 
	- Consider breaking down monolithic structures into modular components for enhanced maintainability. 
	- Document changes thoroughly to facilitate collaboration and knowledge transfer within the development team. 
	- Regularly review and update coding standards to align with best practices. 
	- Continuous integration and deployment pipelines can streamline the refactoring process, promoting a seamless transition. 
	- Regularly monitor application performance post-refactoring to address any unforeseen issues promptly.


- Upgrading a Rails application to a newer version
	- Begin by reviewing the release notes of the target Rails version to identify any breaking changes or deprecated features. 
	- Update gem dependencies in the Gemfile, considering compatibility with the new Rails version. 
	- Run test suites to catch potential issues early in the process. 
	- Utilize tools like Rails' built-in upgrade tasks to automate code adjustments. 
	- Monitor and address deprecation warnings during the testing phase. 
	- Implement necessary changes to routes, controllers, and views based on the updated syntax and conventions. 
	- Finally, validate the application's functionality thoroughly before deploying to production.

- SOLID Principles
	- The SOLID principles are a set of design principles that help developers build software that's modular, testable, and maintainable. In Ruby on Rails, these principles can be applied through careful structuring of models, services, and other application components. Here’s an overview of each SOLID principle with examples in the context of a Rails application.

	## 1. Single Responsibility Principle (SRP)
	Every class should have a single responsibility and reason to change.

	In Rails, it's easy to violate SRP by overloading models with business logic and complex methods. Instead, use service objects to keep your models clean.

	![alt text](<images/solid-1.png>)

	## 2. Open/Closed Principle (OCP)
	Classes should be open for extension but closed for modification.

	Using inheritance, modules, or design patterns like decorators, you can extend behavior without modifying existing code.

	![alt text](<images/solid-2.png>)

	Here, Payment is open for extension, and new payment types (like PaypalPayment) can be added without modifying existing code in Payment.

	## 3 Liskov Substitution Principle (LSP)
	Objects of a superclass should be replaceable with objects of a subclass without altering the behavior.

	LSP encourages the creation of classes that can act interchangeably. For example, all subclasses of a Notifier should be usable as replacements for each other.

	![alt text](<images/solid-3.png>)

	Here, EmailNotifier and SmsNotifier both implement send_message and can be used interchangeably as Notifier types without breaking the code.

	## 4. Interface Segregation Principle (ISP)
	Clients should not be forced to depend on methods they do not use.

	In Ruby, interfaces aren’t enforced, but you can achieve a similar effect by creating specialized modules rather than large, catch-all interfaces. For example, in a reporting system

	![alt text](<images/solid-4.png>)

	## 5. Dependency Inversion Principle (DIP)
	High-level modules should not depend on low-level modules but on abstractions.

	Dependency Injection can be used in Rails by passing dependencies into initializers, making your code more flexible and easier to test

	![alt text](<images/solid-5.png>)

	Here, PaymentProcessor relies on the abstraction StripeGateway, and we can easily swap in another gateway (like PaypalGateway) by passing it to PaymentProcessor without modifying existing code.

	## Summary
	SRP: Separate responsibilities using Service Objects.
	OCP: Use abstract classes or inheritance to add new functionality without modifying existing code.
	LSP: Design classes so they can act interchangeably without breaking functionality.
	ISP: Define separate, purpose-driven modules rather than large, complex interfaces.
	DIP: Rely on abstractions rather than concrete implementations, making code flexible and testable.
	Applying SOLID principles in Rails leads to a more modular, testable, and maintainable codebase. Rails patterns like concerns, service objects, and dependency injection help facilitate these principles, adapting Rails conventions to align with SOLID architecture.

- What is the difference between require and require_relative?
	- require is used to load external libraries or files using an absolute path or from the load path.
	- require_relative loads a file relative to the file in which it is called.

- Version Combinations
	- 
	![alt text](<images/Screenshot from 2024-12-04 21-43-55.png>)

- Version History
	![alt text](<images/Screenshot from 2024-12-05 09-08-35.png>)
- 
- Imp References
	- https://guides.rubyonrails.org/active_record_multiple_databases.html#horizontal-sharding
-
- 	