- https://symfony.com/doc/7.3/quick_tour/the_architecture.html

- symfony new my-project --webapp
- composer create-project symfony/website-skeleton my-project
- composer create-project symfony/skeleton my-api # Api Only
- symfony serve

- What is Symfony?
    - Symfony is a PHP web application framework for building web applications and APIs. It follows the MVC (Model-View-Controller) design pattern and provides components and tools to accelerate web development.
- Symfony Code Structure
    - https://piotr.gg/php/symfony-directory-structure-comparison.html     
- Symfony Boot Process Flow
    - Kernel Initialization: Environment setup, bundle registration.
    - Bundle Boot: Bundle-specific logic initialized.
    - Service Container Compilation: Dependency injection services are prepared.
    - Routing Setup: Routes are loaded.
    - Request Handling: The HTTP request is matched to a controller.
    - Controller Execution: The controller processes the request.
    - Response Handling: The response is created and sent back to the client.
    - Kernel Shutdown: Cleanup and finalization.
- Symfony Lifecycle
    - Request Handling: A request is sent to index.php.
    - Kernel Initialization: The kernel is bootstrapped, services and bundles are loaded.
    - Event Dispatching: Various events are triggered during request processing.
    - Routing: Matches the request to a defined route.
    - Controller Execution: Determines and executes the controller.
    - Data Processing: (Optional) Data is validated, serialized, or transformed.
    - Response Generation: The controller returns a Response.
    - Response Modification: Middleware or listeners modify the response.
    - Sending the Response: The response is sent to the client.
    - Kernel Termination: Post-response tasks are executed.
- Symfony Scaffholding
    - Symfony does not provide direct scaffolding commands like some other frameworks (e.g., Laravel's Artisan commands for scaffolding). However, Symfony's ecosystem includes tools and commands that help generate boilerplate code for controllers, entities, forms, and more, which can be considered as scaffolding in practice.
    - 
    ```php
    php bin/console make:controller ControllerName
    php bin/console make:controller BlogController

    php bin/console make:entity
    php bin/console make:entity BlogPost

    php bin/console make:crud EntityName
    php bin/console make:crud BlogPost
    # A controller with actions for CRUD operations.
    # Twig templates for the CRUD interface.

    php bin/console make:form
    php bin/console make:form BlogPostType

    php bin/console make:migration

    php bin/console make:fixtures
    # To create a class for loading initial data into your database, use the make:fixtures command (with DoctrineFixturesBundle installed).
    ```
    ![alt text](<images/symfony-scaffholding.png>)
    

- What are bundles in Symfony?
    - Bundles are reusable sets of files like controllers, views, and configurations in Symfony. They help organize and modularize Symfony applications by grouping related functionality together.
    - A bundle in Symfony is similar to a plugin or module in other frameworks. It is a package of code that encapsulates specific functionality, such as controllers, services, configurations, and templates, and can be reused across different projects. Bundles allow developers to organize code in a modular way and can be distributed and reused as packages via Composer.
- Explain the Symfony kernel.
    - The Symfony kernel is the heart of the Symfony application. It manages bundles, initializes services, handles requests, and returns responses. It serves as an entry point and orchestrates the entire application lifecycle.
- How do you list all available Symfony commands?
    - php bin/console list
- What is the Symfony routing component used for?
    - The Symfony routing component maps URLs to controllers and actions. It defines how incoming requests should be handled and which controller method should be invoked based on the URL structure.
    - Define Routes in YAML Files
        - 
        ```php
        # config/routes.yaml
        blog_index:
            path: /blog
            controller: App\Controller\BlogController::index

        blog_show:
            path: /blog/{slug}
            controller: App\Controller\BlogController::show
            requirements:
                slug: \w+
        ```
    - Define Routes in PHP Files
        - 
        ```php
        // config/routes.php
        use Symfony\Component\Routing\RouteCollection;
        use Symfony\Component\Routing\Route;

        $routes = new RouteCollection();

        $routes->add('blog_index', new Route('/blog', [
            '_controller' => 'App\Controller\BlogController::index',
        ]));

        $routes->add('blog_show', new Route('/blog/{slug}', [
            '_controller' => 'App\Controller\BlogController::show',
        ], [
            'slug' => '\w+',
        ]));

        return $routes;
        ```
    - Summary
        - Routing in Symfony is flexible and can be managed using annotations, YAML, XML, or PHP configurations. You can define routes, generate URLs, access route parameters, and specify requirements and defaults for route parameters. This flexibility allows you to choose the method that best fits your project's needs and your personal preferences.
- How can a controller be created in Symfony
    - php bin/console make:controller
- How to create model
    - php bin/console make:entity Article
- Explain Dependency Injection in Symfony.
    - Dependency Injection (DI) is a design pattern used in Symfony to manage dependencies between objects. Symfony's Dependency Injection Container (DIC) injects dependencies into objects, allowing for more modular, testable, and maintainable code.    
- What is an event listener in Symfony?
    - Event listeners in Symfony are classes that respond to specific events triggered during the application lifecycle. They allow developers to execute custom logic when certain events occur, such as before or after a controller action is executed.
- What is Doctrine in Symfony?
    - Doctrine is an Object-Relational Mapping (ORM) library used in Symfony for database interaction. It provides a set of PHP libraries for database abstraction, data persistence, and querying using an object-oriented approach.
- How does Symfony handle caching?
    - Symfony provides various caching mechanisms to improve application performance. This includes HTTP caching, template caching with Twig, database query caching with Doctrine, and more.
- Explain Symfony Profiler.
    - Symfony Profiler is a developer tool that provides detailed information about the performance and behavior of Symfony applications. It offers insights into database queries, HTTP requests, routing, and more, aiding in debugging and optimization.
- What is the difference between Symfony Flex and Symfony Standard Edition?
    - Symfony Flex is a modern way of managing Symfony applications that allows for automatic installation, configuration, and management of Symfony bundles. Symfony Standard Edition, on the other hand, is the traditional way of building Symfony applications with manual bundle management.
    - Key features and benefits of Flex:
        - Automatic Dependency Management: Symfony Flex automatically manages dependencies by analyzing the needs of your application based on the packages you require. It installs and configures dependencies, including Symfony components and third-party bundles, without manual intervention.
        - Bundle Management: With Symfony Flex, managing bundles becomes more straightforward. It allows you to enable, disable, or remove bundles directly from the command line, reducing the complexity of managing dependencies.
        - Configuration Made Easy: Symfony Flex simplifies configuration by providing default configurations for commonly used bundles and libraries. It also offers interactive configuration prompts to help you set up your project according to your requirements.
        - Environment Customization: Flex provides environment-specific configuration options, enabling you to define different settings for development, testing, and production environments. This facilitates smoother deployment workflows and ensures consistency across environments.
        - Recipe System: Symfony Flex introduces the concept of "recipes," which are sets of instructions for configuring and integrating bundles and libraries into Symfony applications. Recipes automate the installation and configuration of dependencies, making it easier to add new functionality to your project.
        - Optimized Development Workflow: By automating common tasks and simplifying configuration, Symfony Flex enhances the development workflow, allowing developers to focus more on writing code and less on managing dependencies and configurations.
        - Compatibility: Symfony Flex is fully compatible with Symfony 4 and later versions. It seamlessly integrates with Symfony projects and aligns with Symfony's philosophy of promoting best practices and developer productivity.
- How can you create a custom service in Symfony?
    - To create a custom service in Symfony, you define it in the services.yaml (or services.xml) configuration file, specifying its class and dependencies. Then, you can access this service anywhere in your application using Symfony's Dependency Injection Container.
- How can you debug Symfony applications?
    - Symfony provides several debugging tools, including Symfony Profiler, debug toolbar, logging, and error handling. Additionally, you can use Symfony Console commands for debugging, such as cache clearing and configuration validation.
- Explain Symfony's support for RESTful APIs.
    - Symfony provides tools and components for building RESTful APIs, including routing, serialization, authentication, and versioning. It allows developers to create APIs that adhere to REST principles and integrate smoothly with client applications.
- How does Symfony handle sessions and cookies?
    - Symfony manages sessions and cookies using the Symfony Session Component. It provides APIs for working with session data, setting and retrieving cookies, and managing session storage options like files, databases, or caching systems.

- How to handle authentication and Authorization in Symfony
    - Authentication and authorization in Symfony are managed through its Security Component.
    - Symfony provides several ways to authenticate users, including form-based login, HTTP basic/digest authentication, token-based authentication, and OAuth.
    - Install Symfony and Security Bundle
        - composer require symfony/security-bundle
    - Define security settings in the config/packages/security.yaml file.
    - 
    ```php
     access_control:
        - { path: ^/admin, roles: ROLE_ADMIN }
        - { path: ^/profile, roles: ROLE_USER }
        - { path: ^/, roles: IS_AUTHENTICATED_ANONYMOUSLY }
    ```
    - Create a User Entity
        - 
        ```php
        // src/Entity/User.php
        namespace App\Entity;
        use Symfony\Component\Security\Core\User\UserInterface;
        class User implements UserInterface
        ```
    - Use Voters for Complex Authorization
    - Summary
        - Authentication: Configured using firewalls and user providers.
        - Authorization: Controlled via roles, access control, and voters.
        - Key Tools: Annotations like @IsGranted, methods like isGranted(), and security voters.
        - Symfony's Security Component is highly flexible, enabling integration with various authentication mechanisms and fine-grained access control using roles and voters.
- Steps to migrate from Symfony 5 to Symfony 6:
    - Review Symfony 6 requirements.
    - Update Composer dependencies.
    - Check and resolve deprecations in Symfony 5.
    - Update configuration files.
    - Update code for removed or changed features.
    - Run tests and fix issues.
    - Update Symfony Flex recipes.
    - Check third-party bundle and library compatibility.
    - Review and implement new features.
    - Deploy and monitor the application.        
- How do you handle caching in Symfony?
    - Install Symfony Cache Component: Install the required cache component.
    - Configure Cache Pools: Define cache pools in config/packages/cache.yaml.
    - Using Cache in Controllers or Services: Inject and use cache pools in your controllers or services.
    - Cache Expiration and Invalidations: Set expiration times and invalidate cache items as needed.
    - Using Doctrine Cache: Configure Doctrine to use cache for metadata, query, and result caching.
    - Using HTTP Caching: Implement HTTP caching for responses.
- How to access environment variable in symfony
    - Environment variables are usually defined in the .env file at the root of your project. For production environments, these variables can be set directly on the server.
    - You can access environment variables in Symfony configuration files using the %env()% function. Example config/services.yaml
    - To access environment variables directly in your controllers or services, you can use the getParameter() method.
    - Access Environment Variables Directly by Using  getenv('APP_SECRET');
- How many types of bundles are there in Symfony?
    - Application Bundles - Specific to your application and not intended for reuse.
    - Reusable Bundles - Designed to be reused across multiple projects.
        - Create the Bundle Structure: Organize your bundle with the necessary directories and files.
        - Create the Bundle Class: Extend the Bundle class.
        - Create Dependency Injection Configuration: Define the configuration and extension classes.
        - Define Services: Configure the services provided by the bundle.
        - Create a Service: Implement the service logic.
        - Configure Autoloading: Set up autoloading in composer.json.
        - Use the Bundle: Integrate the bundle into a Symfony project.
    - Third-Party Bundles - Developed by the community and provide various features that can be easily integrated into your project.
- Can you explain the lifecycle of a bundle in Symfony?
    - The lifecycle of a bundle in Symfony involves several steps:
        - Initialization: The bundle is instantiated and registered during the kernel's boot process.
        - Configuration: The bundle’s configuration is loaded and processed. This is where custom configuration files are read, and services are registered.
        - Build: The build() method of the bundle class can be overridden to modify the service container during the compilation phase.
        - Runtime: Once the kernel is booted, the bundle's services, routes, controllers, and other components are available for use within the application.
- Helper function in Symfony
    - Helper Service: Create a service class containing helper functions. This is the recommended approach for better dependency management and testability.
        - // src/Service/HelperService.php
    - Twig Extension: Create a Twig extension to use helper functions in your Twig templates.
        - // src/Twig/AppExtension.php
    - Global Helper Functions: Define global helper functions, although this is not commonly recommended due to potential issues with testability and maintainability.
        - // src/Helper/functions.php
- Cache adapter in Symfony
    - Install Symfony Cache Component: Ensure you have the Cache component installed.
    - Configure Cache Adapters: Define cache pools in config/packages/cache.yaml using various adapters like filesystem, Redis, Memcached, etc.
    - Using Cache Adapters: Use the cache pools in your controllers, services, or other parts of your application.
    - Set Expiration and Invalidate Cache: Manage cache expiration and invalidation as needed.
    - Use Doctrine Cache: Integrate Doctrine ORM with the Symfony Cache component for metadata, query, and result caching.
- When does Symfony deny the user access?
    - Access Control Configuration
        - Symfony uses access control rules defined in the security.yaml configuration file to restrict access to specific routes or paths based on user roles.    
    - Role-Based Access Control
        - Symfony checks the roles assigned to a user to determine if they have the necessary permissions to access a resource. If a user does not have the required role, access is denied.
    - Security Voters
        - Security voters are a more flexible way to handle complex access control logic. A security voter is a class that implements the VoterInterface and is used to determine whether a user has access to a specific resource.
    - Access Denied Handlers
        - Symfony allows you to customize the behavior when access is denied by using access denied handlers.
- How sessions are managed in Symfony?
    - In Symfony, sessions are managed using the session service provided by the Symfony HttpFoundation component. 
    - Basic Session Usage: Access the session service in controllers to set, get, and remove session data.
    - Configuring Session Storage: Customize session storage using the framework.yaml configuration file. Options include file storage, Redis, Memcached, etc.
    - Using Redis for Session Storage: Install and configure the Redis bundle for session storage.
    - Session Lifetime and Cookie Configuration: Configure session lifetime, cookie settings, and other parameters for security and session management.
    - Flash Messages: Use flash messages for one-time notifications that persist for the next request.
- Create events 
    - Define the Event: Create a class representing the event.
    - Create a Listener: Write a class with methods that will be called when the event is dispatched.
    - Register the Listener: Register the listener in your service configuration file.
    - Dispatch the Event: Use the EventDispatcherInterface to dispatch the event in your code.
    - (Optional) Create a Subscriber: Use an event subscriber to listen to multiple events if needed.
- What is webhook in symfony
    - A webhook in Symfony is a way for your application to receive real-time notifications from an external service. When certain events happen on the external service, it sends an HTTP request to a specified URL in your Symfony application. This allows your application to react to events such as payment confirmations, form submissions, or changes in third-party services.
- New in Symfony 7
- Symfony Contracts
    - Symfony Contracts is a set of abstractions, also known as "contracts," that decouple Symfony components from their implementation. This allows you to use these abstractions in your applications or libraries without being tied to specific Symfony components. The idea is to provide a standard way of defining interfaces that other libraries or components can implement, ensuring a high level of interoperability within the Symfony ecosystem and beyond.
    - Installation
        - composer require symfony/contracts
    - Examples of Symfony Contracts
        - Symfony\Contracts\Service\ServiceSubscriberInterface
        - Symfony\Contracts\Translation\TranslatorInterface
        - Symfony\Contracts\Cache\CacheInterface
        - Symfony\Contracts\EventDispatcher\EventDispatcherInterface
        - Symfony\Contracts\HttpClient\HttpClientInterface
    - Benefits of Using Symfony Contracts
        - Flexibility: You can easily switch out components without changing your application code.
        - Interoperability: Ensures that different components and libraries can work together smoothly.
        - Maintenance: Makes your codebase easier to maintain and evolve over time.
        - Standards Compliance: Many Symfony Contracts align with PHP-FIG standards (e.g., PSR-3 for logging, PSR-6 for caching), ensuring wide compatibility.
    - Summary
        - Symfony Contracts provide a powerful way to write decoupled, interoperable, and maintainable code by defining a set of interfaces (contracts) that different components or libraries can implement. This approach aligns with modern PHP practices, making it easier to build flexible applications that can integrate various services and components.
- What are events in Symfony?
    - Events in Symfony are a way of decoupling different parts of an application by allowing components to communicate with each other without being directly dependent on each other. When an event occurs (such as a request being made or an entity being created), it can trigger listeners or subscribers that react to that event. Symfony uses an event dispatcher to manage this communication.
- What is the EventDispatcher component in Symfony?
    - The EventDispatcher component in Symfony is responsible for dispatching events and managing event listeners. It allows you to define events, register listeners or subscribers, and dispatch events to those listeners. This enables you to extend and customize the behavior of your application in a flexible and decoupled manner.
- How do you create a custom event in Symfony?
    - Define a new event class that extends Symfony\Contracts\EventDispatcher\Event.
    - Add any necessary properties and methods to this class.
    - Dispatch the event using the EventDispatcher service.        
- What is the difference between an event listener and an event subscriber in Symfony?
    - Event Listener: An event listener is a PHP method that is executed when a specific event is dispatched. It is directly associated with an event in the service configuration, usually by tagging a service with the kernel.event_listener tag and specifying the event name and method to call.

    - Event Subscriber: An event subscriber is a class that implements the EventSubscriberInterface and defines which events it wants to listen to and the corresponding methods to handle those events. Subscribers are more flexible and allow you to listen to multiple events within a single class.
- Can you modify the event object in a listener or subscriber?
    - Yes, you can modify the event object in a listener or subscriber. The event object is passed by reference to the listener or subscriber method, so any changes you make to the event object will affect the rest of the application. This is useful when you want to pass data back to the code that dispatched the event.
- What are some common events provided by Symfony?
    - Symfony provides several common events, including:
        - kernel.request: Dispatched at the very beginning of the request handling process.
        - kernel.controller: Dispatched when the controller is chosen to handle the request.
        - kernel.view: Dispatched when the controller returns data instead of a Response object.
        - kernel.response: Dispatched after the controller returns a response object.
        - kernel.exception: Dispatched when an exception is thrown during the request handling process.
        - kernel.terminate: Dispatched after the response has been sent.
- What is the purpose of the Event class in Symfony?
    - The Event class in Symfony is a base class that can be extended to create custom events. It provides basic functionality like stopping propagation. While it’s common to extend this class, Symfony also allows using plain objects or classes that implement the Symfony\Contracts\EventDispatcher\Event interface.
- What is the Symfony Kernel?
    - The Symfony Kernel is the core of the Symfony application. It is responsible for bootstrapping the application, managing the service container, handling requests, and returning responses. The Kernel initializes bundles, handles the HTTP request/response lifecycle, and manages configuration and environment settings.
- What are the main responsibilities of the Symfony Kernel during an HTTP request?
    - During an HTTP request, the Symfony Kernel performs the following tasks:
    - Bootstrapping: Initializes the environment and loads configurations.
    - Loading Bundles: Registers and initializes bundles.
    - Handling the Request: Converts the HTTP request into a Symfony Request object.
    - Routing: Matches the request to a route and selects the appropriate controller.
    - Executing the Controller: Executes the controller action.
    - Handling the Response: Converts the controller's return value into a Response object.
    - Dispatching Events: Dispatches various events during the request handling process.
    - Sending the Response: Sends the response back to the client.
-  What is the Kernel::boot() method used for?
    - The Kernel::boot() method initializes the kernel and prepares the service container for use. This method loads all registered bundles, processes configuration, and compiles the service container. It is automatically called when handling an HTTP request but can also be invoked manually if needed, such as in a CLI context.
- What are the differences between the Symfony Kernel and HttpKernel components?
    - Kernel: The Kernel is the core of a Symfony application and extends the HttpKernel component. It is responsible for managing bundles, configuration, and handling the application's environment. The Kernel is specific to the Symfony framework and is the starting point of the application.

    - HttpKernel: The HttpKernel component is more generic and can be used independently of the Symfony framework. It is responsible for handling HTTP requests and dispatching them to the appropriate controller. The HttpKernel component can be used in other frameworks or standalone applications to manage the HTTP request/response cycle.
- How do you customize the boot process in Symfony?
    - You can customize the boot process in Symfony by overriding the Kernel::boot() method in your custom Kernel class. This allows you to add custom initialization logic or modify the default behavior of the Kernel during the boot process.
- What is a MicroKernelTrait in Symfony?
    - The MicroKernelTrait is a trait provided by Symfony to simplify the creation of microservices or small Symfony applications. It allows you to define routes and configuration directly in the Kernel class without needing external configuration files. The MicroKernelTrait is commonly used for creating lightweight, single-purpose applications.
    - 
    ```php
    use Symfony\Bundle\FrameworkBundle\Kernel\MicroKernelTrait;
    use Symfony\Component\HttpKernel\Kernel;

    class MyKernel extends Kernel
    {
        use MicroKernelTrait;

        protected function configureRoutes(RouteCollectionBuilder $routes): void
        {
            $routes->add('/hello', 'App\Controller\HelloController::index');
        }

        protected function configureContainer(ContainerBuilder $container, LoaderInterface $loader): void
        {
            // Custom configuration here
        }
    }
    ```
- How do Symfony Contracts ensure forward compatibility in Symfony components?
    - Symfony Contracts are designed with forward compatibility in mind. By providing stable interfaces that are less likely to change over time, Symfony ensures that components depending on these contracts will continue to function correctly even after major framework updates. This reduces the risk of breaking changes and simplifies the upgrade process for Symfony applications.
- What are the advantages of using Symfony Contracts over directly using Symfony components?
    - Decoupling: Contracts allow your code to depend on interfaces rather than specific implementations, making it more modular and adaptable.
    - Interchangeability: You can easily swap out implementations without changing your code.
    - Reusability: Code relying on contracts can be reused across different projects, even those that don't use Symfony.
    - Testability: Contracts make it easier to mock dependencies during testing, improving test coverage and reliability.    
- middleware in Symfony
    - Symfony does not have a concept of "middleware" in the same way that frameworks like Laravel or Express.js do, but similar functionality can be achieved through the use of Event Listeners or Event Subscribers in Symfony.
- How Symfony traverse request
    - Flowchart Overview:
        - Request →
        - Kernel Bootstrapping →
        - kernel.request Event →
        - Routing →
        - kernel.controller Event →
        - Controller Execution →
        - kernel.view Event (Optional) →
        - kernel.response Event →
        - Response →
        - kernel.terminate Event  
- What is the Symfony Service Container?
    - The Symfony Service Container is a dependency injection container that manages the instantiation of services (objects) and their dependencies. It allows for the automatic injection of dependencies into services, making the code more modular and easier to manage.  
- What is the difference between public and private services in Symfony?
    - Public services: These can be retrieved directly from the service container using the get() method. They are accessible from anywhere in the application.
    - Private services: These cannot be retrieved directly from the container. They are meant to be used internally by other services. Making services private encourages better design practices by limiting service access and promoting dependency injection.
    - 
    ```php
    services:
    App\Service\MyService:
        public: false
    ```       
- What is the difference between getParameter() and get() in Symfony's Service Container?
    - getParameter(): Retrieves a parameter defined in the service container. Parameters are typically used for configuration values like strings, integers, or arrays.
    - get(): Retrieves a service instance from the service container
- How can you make a service lazy in Symfony?
    - A service can be made lazy by setting the lazy option to true in its definition. This means the service will only be instantiated when it is actually needed, which can save resources and improve performance.
-  How do you configure environment-specific services in Symfony?
    - Environment-specific services can be configured by using environment-specific service configuration files, such as services_dev.yaml or services_prod.yaml. Symfony automatically loads the appropriate configuration file based on the environment (dev, prod, etc.).
- How does Symfony handle errors?
    - Symfony handles errors through its ExceptionListener, which catches exceptions and converts them into an appropriate Response object. Depending on the environment (dev or prod), Symfony will either show a detailed error page (with stack trace and debugging information) or a generic error page.
- What is the Symfony Debug Mode?
    - Symfony’s Debug Mode is enabled when the application is running in the dev environment. It provides detailed error messages, stack traces, and access to the Symfony Profiler. The Debug Mode helps developers identify and fix issues during development.  
    - 
    ```php
    // public/index.php
    $kernel = new Kernel('dev', true);
    ```   
- How do you log errors in Symfony?
    - Symfony uses the monolog library for logging. Errors and exceptions are automatically logged to files under the var/log directory. You can configure different logging channels and handlers in the config/packages/monolog.yaml file.
- What is the Symfony Profiler?
    - The Symfony Profiler is a powerful tool available in the dev environment that provides detailed information about each request, such as execution time, memory usage, SQL queries, and more. It’s accessible via the Symfony web debug toolbar or directly at /app_dev.php/_profiler.
- What are some common debugging tools used in Symfony?
    - Symfony Profiler: Provides detailed insights into each request.
    - Xdebug: A PHP extension that allows step-by-step debugging.
    - VarDumper Component: Symfony’s dump() function provides a better way to inspect variables, offering more readable output than var_dump().
    - Monolog: For logging errors and other important events. 
- How do you enable and disable the Symfony Debug Toolbar?
    - The Symfony Debug Toolbar is automatically enabled in the dev environment. You can disable it by modifying the config/packages/dev/web_profiler.yaml file:
    - 
    ```php
    web_profiler:
        toolbar: false
    ```
- What is the debug:event-dispatcher command used for?
    - The debug:event-dispatcher command is used to list all event listeners registered with Symfony's Event Dispatcher. It shows the events they are attached to and the priority of each listener.
    - php bin/console debug:event-dispatcher
- How do you handle 404 errors in Symfony?
    - Symfony automatically returns a 404 error when a route is not found. You can customize the 404 page by creating an error404.html.twig template in the templates/bundles/TwigBundle/Exception/ directory.
    - Alternatively, you can create an exception listener that listens for NotFoundHttpException and returns a custom response.
- How does Symfony handle fatal errors and recover from them?
    - Symfony catches fatal errors through a special shutdown function registered during the kernel initialization. This function converts the error into a FatalThrowableError exception and handles it through the normal exception handling process, generating a response accordingly.
- How do you create a custom command in Symfony?
    - To create a custom command in Symfony, you need to create a class that extends Symfony\Component\Console\Command\Command and define the command's configuration and logic in the configure() and execute() methods, respectively.
- How do you run a Symfony command?
    - Symfony commands are executed via the command-line interface using the php bin/console command followed by the command name.
    - php bin/console app:my-custom-command
- How do you pass arguments and options to a Symfony command?
    - Arguments and options can be defined in the configure() method using the addArgument() and addOption() methods. Arguments are required or optional values, while options are typically flags or key-value pairs.
-  How can you schedule a command to run at regular intervals in Symfony?
    - Symfony itself doesn't provide built-in scheduling. However, you can use a system scheduler like cron on Linux or Task Scheduler on Windows to run Symfony commands at regular intervals.
    - * * * * * /usr/bin/php /path-to-your-project/bin/console app:my-custom-command
- What is the initialize() method in a Symfony command?
    - The initialize() method is called before the execute() method in a Symfony command. It’s used to set up any necessary prerequisites, such as validating input or initializing services.
- What is the difference between the configure(), initialize(), and execute() methods in a Symfony command?
    - configure(): Defines the command’s configuration (name, description, arguments, options).
    - initialize(): Initializes the command just before execution, often used for pre-execution setup.
    - execute(): Contains the main logic of the command, which is executed when the command is run.
- How do you prevent a Symfony command from running in a production environment?
    - You can check the current environment in the execute() method and exit the command with an error if it’s running in production.
    - 
    ```php
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        if ($this->getApplication()->getKernel()->getEnvironment() === 'prod') {
            $output->writeln('This command cannot be run in production.');
            return Command::FAILURE;
        }
        
        // Command logic...
    }
    ```
- How do you execute another Symfony command from within a command?
    - You can execute another Symfony command from within a command using the Application's find() method to retrieve the command and then the run() method to execute it.
    - 
    ```php
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $command = $this->getApplication()->find('app:another-command');
        $commandInput = new ArrayInput([]);
        $command->run($commandInput, $output);

        return Command::SUCCESS;
    }
    ```
- Symfony Polyfills
    - Symfony Polyfills are a set of PHP libraries that provide compatibility for features that may not be available in all versions of PHP. Essentially, they "polyfill" or fill in the gaps by implementing features in userland PHP code that might be missing in older PHP versions or on systems where certain extensions aren't available.
    - Symfony Polyfills are a powerful tool for ensuring that your PHP codebase is future-proof, backward-compatible, and consistent across various environments and PHP versions. They allow developers to use modern features without being constrained by the minimum PHP version requirement of their projects, providing a seamless developer experience.
- Symfony Entity Manager
    - In Symfony, the Entity Manager is a core part of the Doctrine ORM (Object-Relational Mapper). It manages the persistence of entities (objects that represent rows in a database table) and provides an interface to perform operations like finding, saving, updating, and deleting records in the database.
- Framework-Agnostic Code in Symfony
    - Design with Decoupling in Mind
        - Use Interfaces: Define interfaces for your services, and program to those interfaces rather than concrete classes. This allows for easier swapping of implementations.
        - Separate Concerns: Ensure that your code follows the Single Responsibility Principle (SRP). Each class or module should have one and only one reason to change.
        - Use Dependency Injection: Instead of hardcoding dependencies, inject them through the constructor, setters, or other methods. This makes your code more flexible and easier to test.
    - Avoid Framework-Specific Code
        - No Direct Access to Framework Services: Avoid directly accessing Symfony services like the service container, request, or response objects in your core logic. Instead, pass these as dependencies if needed.
        - No Use of Framework Annotations: While Symfony provides convenient annotations for routing, validation, etc., these are tied to the framework. Use PHP attributes or plain configuration files where possible.
    - Use Symfony Components as Standalone Libraries
        - Symfony’s components are designed to be used independently. For example, you can use the HttpFoundation component to handle HTTP requests and responses without requiring the entire Symfony framework.
    - Create Reusable Libraries
        - Organize Code into Packages: If you have logic that can be reused across multiple projects, consider organizing it into a standalone package. Use tools like Composer to manage dependencies.
        - Avoid Hard-Coding Framework Paths: Avoid assumptions about file paths or the presence of specific directories used by Symfony. Make your code configurable so it can work in different environments.
    - Use Symfony Contracts
        - Symfony Contracts are a set of abstractions extracted out of the Symfony components. These contracts help to create reusable, decoupled libraries.
        - For instance, instead of depending on Symfony's EventDispatcher, you can depend on the EventDispatcherInterface from the symfony/contracts package, making your code more portable.
- Symfony component used in laravel
    - HttpFoundation Component
        - Purpose: Handles the HTTP request and response objects.
        - Usage in Laravel: Laravel’s Illuminate\Http\Request and Illuminate\Http\Response classes extend Symfony’s Symfony\Component\HttpFoundation\Request and Symfony\Component\HttpFoundation\Response respectively. This component manages HTTP requests, responses, sessions, and cookies in Laravel.
    - Routing Component
        - Purpose: Handles URL routing and generates URLs based on route definitions.
        - Usage in Laravel: Laravel’s routing system is built on top of Symfony’s Routing component. This component matches incoming URLs to the defined routes and dispatches them to the appropriate controllers.
    -  EventDispatcher Component
        - Purpose: Manages event listeners and dispatches events to them.
        - Usage in Laravel: Laravel’s event system is heavily inspired by Symfony’s EventDispatcher component. Laravel uses this component to allow the application to listen to and dispatch events.
     Console Component
        - Purpose: Provides the tools to build command-line applications.
        - Usage in Laravel: Laravel’s Artisan command-line interface is powered by Symfony’s Console component. This component handles input, output, command definitions, and command execution.
    - VarDumper Component
        - Purpose: Provides tools for dumping variables.
        - Usage in Laravel: Laravel’s dd() and dump() functions use Symfony’s VarDumper component to output debug information.
    - Process Component
        - Purpose: Manages system processes in PHP.
        - Usage in Laravel: The Process component is used in Laravel for running system commands, such as when running tasks in Artisan commands.
    - Translation Component
        - Purpose: Handles translation and localization.
        - Usage in Laravel: Laravel uses Symfony’s Translation component to manage translation files and translation strings.
    - Yaml Component
        - Purpose: Parses and dumps YAML files.
        - Usage in Laravel: While not as commonly used directly in Laravel, the Yaml component can be employed in Laravel applications to manage configuration files or other YAML-based data.
    - Mime Component
        - Purpose: Handles MIME types and file extensions.
        - Usage in Laravel: Laravel’s file upload and response systems use the Mime component to determine the MIME type of files.
- How do you share configuration across multiple bundles?
    - You can share configuration across multiple bundles by creating a shared configuration file, like config/packages/shared_config.yaml, and then using it in each bundle. Alternatively, you can create a base service configuration file in one bundle and import it in other bundles using the imports key in services.yaml.
- What is the Extension class in a Symfony bundle?
    - The Extension class in a Symfony bundle is responsible for loading and managing the bundle's configuration. It processes the configuration files (e.g., services.yaml) and registers services within the Symfony Dependency Injection Container. The Extension class typically implements the ExtensionInterface and is named following the pattern {BundleName}Extension.
- What is a Compiler Pass in Symfony and how is it used in bundles?
    - A Compiler Pass in Symfony allows you to modify or optimize the Dependency Injection Container after all services have been defined but before the container is compiled. This is often used in bundles to modify service definitions, add method calls, or inject dependencies dynamically. To use a compiler pass in a bundle, you implement the CompilerPassInterface and register the compiler pass in the bundle's build() method.
- What is the purpose of Bundle::shutdown() method?
    - The Bundle::shutdown() method is called during the kernel's shutdown process. It is used to clean up resources, close connections, or perform other teardown operations. This method is rarely used in practice, but it can be useful in certain scenarios where a bundle needs to release resources or reset states before the application is fully shut down.
- Database Entity Associations 
    - Mapping Associations
        - OneToOne: Each entity has one related entity.
        - ManyToOne: Many entities are related to one entity.
        - OneToMany: One entity is related to many entities.
        - ManyToMany: Many entities are related to many entities.
        - 
        ```php
        #[ORM\OneToMany(mappedBy: 'category', targetEntity: Product::class)]

        #[ORM\ManyToOne(targetEntity: Category::class, inversedBy: 'products')]
        #[ORM\JoinColumn(nullable: false)]

        #[ORM\ManyToMany(targetEntity: Course::class, inversedBy: 'students')]

        #[ORM\ManyToMany(targetEntity: Student::class, mappedBy: 'courses')]

        php bin/console make:migration
        php bin/console doctrine:migrations:migrate
        ```
- ORM\JoinColumn(nullable: false meaning in symfony
    - The @ORM\JoinColumn(nullable: false) annotation in Symfony (which uses Doctrine ORM) is used to define the behavior of the foreign key column in a database when mapping entity associations, such as ManyToOne, OneToOne, or ManyToMany.
    - What Does nullable: false Mean?
    - When you specify nullable: false on a @JoinColumn, it means that the foreign key column in the database cannot be NULL. In other words, the association is required, and the related entity must exist.
- FlashBag vs Flash message?
- Background job
    - In Symfony, background jobs can be managed using a variety of tools and libraries. One common approach is to use Symfony Messenger with a transport like RabbitMQ or Redis to handle asynchronous tasks or background jobs. Below is an example of how to set up and run a background job in Symfony using the Messenger component.
    - 
    ```php
    composer require symfony/messenger  
    composer require symfony/redis-messenger
    # config/packages/messenger.yaml
    ```
    - 
- What is asset mapper in Symfony
    - The Asset Mapper is a new tool introduced in Symfony 6.3 as a modern alternative for managing and organizing frontend assets (like CSS, JavaScript, and images) in Symfony applications. It provides a streamlined and efficient way to handle assets without the need for complex tools like Webpack, Vite, or Gulp, which have traditionally been used in Symfony projects.
    - Benefits of the Asset Mapper
        - Simplicity: It provides a much simpler alternative to managing assets compared to traditional JavaScript-based tools like Webpack or Vite.
        - No Dependency on Node.js: Since there’s no need for a build step, you avoid the complexity of Node.js and its ecosystem.
        - Speed: Faster development cycles as you don’t need to run asset compilation commands.
        - Symfony Integration: Tight integration with Symfony makes it easier to manage assets in a Symfony-centric way.
- Symfony Vs Laravel -> https://chatgpt.com/c/7876303c-c68f-41d3-8017-0389fe9ae81e
- Symfony USP over Laravel
    - 1. Modularity and Reusability
        - Component-Based Architecture: Symfony is built from a collection of independent components, which can be used in any PHP project, not just within the Symfony framework. This modular approach allows developers to use only what they need, making Symfony highly flexible and reusable.
        - Framework-Agnostic Components: Many popular PHP projects, including Laravel itself, use Symfony components. This means that Symfony is not just a framework but also a set of tools that can enhance any PHP application.
    - 2. Enterprise-Grade Features
        - Designed for Large-Scale Applications: Symfony is particularly well-suited for complex, large-scale, and enterprise-level applications. It offers robust features, including advanced configuration, dependency injection, and service-oriented architecture, which are crucial for building and maintaining large systems.
        - Long-Term Support (LTS): Symfony provides long-term support versions, making it a reliable choice for enterprise projects that require stability and extended maintenance periods.
    - 3. Strict Adherence to Standards
        - PSR Compliance: Symfony strictly adheres to PHP Standards Recommendations (PSR), ensuring that your code is consistent with industry best practices. This makes Symfony projects more maintainable, especially in environments where developers may come and go.
        - Interoperability: Because Symfony follows standards so closely, it plays well with other tools and frameworks, making it easier to integrate with third-party libraries and services.
    - 4. Customization and Flexibility
        - Highly Configurable: Symfony provides extensive configuration options, allowing you to tailor the framework to your specific needs. This is particularly valuable in complex projects where the default setup may not be sufficient.
        - Custom Architecture: With Symfony, you can create a highly customized application architecture that fits your exact requirements. This is possible thanks to Symfony’s flexible design, which allows developers to override and extend almost any part of the framework.
    - 5. Internationalization and Localization
        - Built-in Support for Multilingual Applications: Symfony offers comprehensive support for internationalization (i18n) and localization (l10n), making it easier to build applications that support multiple languages and regions.
        - Advanced Translation Features: Symfony’s translation component allows for sophisticated handling of text in different languages, including pluralization and message catalog management.
    - 6. Robust Security Features
        - Comprehensive Security Component: Symfony includes a powerful security component that provides built-in authentication, authorization, and other security mechanisms. It is designed to handle complex security requirements, including roles, permissions, and access control lists (ACLs).
        - Regular Security Audits: Symfony’s focus on security is backed by regular audits and a strong commitment to maintaining a secure framework. This is crucial for enterprise applications where security is a top priority.
    - 7. Performance and Scalability
        - Optimized for Performance: Symfony is designed with performance in mind, offering tools and features to optimize your application’s speed and efficiency. This includes HTTP caching, built-in profiling tools, and support for advanced caching strategies.
        - Scalable Architecture: Symfony’s architecture is built to scale, making it a great choice for projects that need to grow over time. Its ability to handle high traffic and large datasets makes it suitable for enterprise-grade applications.
    - 8. Professional Support and Ecosystem
        - Backed by SensioLabs: Symfony is backed by SensioLabs, a company that provides professional support, training, and consulting services. This is a significant advantage for businesses that require guaranteed support and expertise.
        - Mature Ecosystem: Symfony has a mature and extensive ecosystem, including a wide range of third-party bundles, plugins, and tools that can extend the framework’s capabilities.
    - 9. Doctrine ORM Integration
        - Advanced ORM Capabilities: Symfony integrates seamlessly with Doctrine ORM, which is one of the most powerful object-relational mapping (ORM) tools in the PHP ecosystem. Doctrine provides advanced features like complex queries, lazy loading, and database migrations, making it a strong choice for projects that require sophisticated database interactions.
    - 10. Symfony Flex
        - Flexible and Modern Package Management: Symfony Flex is a tool that streamlines the installation and configuration of packages in Symfony projects. It allows for easier management of dependencies, configuration, and environment settings, making Symfony more accessible and easier to maintain.
    - Conclusion
        - While Laravel excels in rapid development and ease of use, Symfony’s USPs lie in its modularity, flexibility, and adherence to standards, making it particularly well-suited for enterprise-level applications and projects requiring high customization and scalability. If you are working on a complex application where performance, security, and long-term maintainability are critical, Symfony’s robust architecture and feature set provide a strong foundation.
- 
- 
