f = Fiber.new { puts 1; Fiber.yield; puts 2 }
f.resume
f.resume


# output = `ls -l`
# puts output

# output = %x{ls -l}
# puts output

module StringRefinements
    refine String do
      def shout
        upcase
      end
      def palindrome1?
        self == self.reverse
      end
    end
  end
  
  # Without enabling the refinement
  puts "hello".respond_to?(:shout)  # Output: false
  
  # Enabling the refinement
  using StringRefinements
  
  puts "hello".palindrome1?  # Output: HELLO!
  
  
  class String
          def vowel_count
              self.scan(/[aeiou]/i).count
          end
      end
  
      puts "Hello".vowel_count # Output: 2
      puts "Ruby on Rails".vowel_count # Output: 5
  
  
  class String
          def palindrome?
              self == self.reverse
          end
          def downcase!
        downcase
      end
 end

puts "<PERSON>am".shout # => true
puts "hello".palindrome? # => false