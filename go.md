---------------------------------------------------------------------------------------------------------------------
### References
---------------------------------------------------------------------------------------------------------------------
- https://go.dev/learn/
- https://go.dev/doc/install
- https://go.dev/doc/tutorial/getting-started
- https://pkg.go.dev/search?q=quote
- https://go.dev/doc/modules/developing
- https://go.dev/doc/tutorial/workspaces
- https://github.com/golang

- Go Version History
- 	| Year | Major Release | Key Features                                   |
	| ---- | ------------- | ---------------------------------------------- |
	| 2012 | Go 1.0        | First stable release                           |
	| 2015 | Go 1.5        | Self-hosted compiler, vendor support           |
	| 2017 | Go 1.7        | `context` package                              |
	| 2018 | Go 1.11       | Go Modules (experimental)                      |
	| 2020 | Go 1.14       | Go Modules default, goroutine preemption       |
	| 2022 | Go 1.18       | **Generics introduced**, fuzz testing          |
	| 2023 | Go 1.21       | `log/slog`, `cmp`, `min`/`max`                 |
	| 2024 | Go 1.22–1.23  | Performance, module improvements, small syntax |

- Basic command
	- go version
	- go run . (Compile and run the current file immediately)
	- go build .
	- go build -o <output-file> .
	- go run main.go 
	- go env
	- go mod init <module-name>
	- go mod tidy
	- go mod vendor
	- go mod graph
	- go mod why
	- go mod download
	