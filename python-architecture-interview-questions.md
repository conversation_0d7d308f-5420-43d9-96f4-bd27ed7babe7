# Top 10 Python Interview Questions for Architecture Role

## 1. How would you design a scalable microservices architecture using Python?

**Answer:**
For a scalable microservices architecture in Python, I would consider:

- **Framework Selection**: Use FastAPI or Flask for lightweight services, Django for complex business logic
- **Service Communication**: Implement REST APIs with proper versioning, consider gRPC for internal communication
- **Database Strategy**: Database per service pattern, use appropriate databases (PostgreSQL, MongoDB, Redis)
- **Message Queues**: Implement async communication using Celery with Redis/RabbitMQ
- **Containerization**: Docker containers with multi-stage builds for optimization
- **Orchestration**: Kubernetes for container orchestration and auto-scaling
- **API Gateway**: Use Kong or AWS API Gateway for routing and rate limiting
- **Monitoring**: Implement distributed tracing with <PERSON>aeger, logging with ELK stack
- **Circuit Breaker**: Use libraries like pybreaker for fault tolerance

```python
# Example FastAPI microservice structure
from fastapi import FastAPI
from pydantic import BaseModel
import asyncio

app = FastAPI()

class UserService:
    async def get_user(self, user_id: int):
        # Database operation
        pass

    async def create_user(self, user_data: dict):
        # Business logic
        pass

@app.get("/users/{user_id}")
async def get_user(user_id: int):
    service = UserService()
    return await service.get_user(user_id)
```

## 2. Explain the differences between threading, multiprocessing, and asyncio in Python. When would you use each?

**Answer:**

**Threading:**
- Best for I/O-bound tasks
- Limited by GIL (Global Interpreter Lock)
- Shared memory space
- Lower memory overhead

**Multiprocessing:**
- Best for CPU-bound tasks
- Bypasses GIL limitation
- Separate memory spaces
- Higher memory overhead

**Asyncio:**
- Best for I/O-bound tasks with many concurrent operations
- Single-threaded event loop
- Cooperative multitasking
- Excellent for web servers and API calls

```python
import asyncio
import threading
import multiprocessing
import time

# Asyncio example
async def async_task():
    await asyncio.sleep(1)
    return "Async completed"

# Threading example
def thread_task():
    time.sleep(1)
    return "Thread completed"

# Multiprocessing example
def cpu_task(n):
    return sum(i*i for i in range(n))

# Usage scenarios:
# - Use asyncio for handling thousands of HTTP requests
# - Use threading for file I/O operations
# - Use multiprocessing for mathematical computations
```

## 3. How would you implement caching strategies in a Python application?

**Answer:**

**Multi-level Caching Strategy:**

1. **Application Level**: In-memory caching with LRU
2. **Distributed Cache**: Redis for shared cache across instances
3. **Database Level**: Query result caching
4. **CDN Level**: Static content caching

```python
import redis
import functools
import time
from typing import Any, Optional

class CacheManager:
    def __init__(self):
        self.redis_client = redis.Redis(host='localhost', port=6379, db=0)
        self.local_cache = {}
        self.cache_ttl = 300  # 5 minutes

    def get_from_cache(self, key: str) -> Optional[Any]:
        # Try local cache first
        if key in self.local_cache:
            data, timestamp = self.local_cache[key]
            if time.time() - timestamp < self.cache_ttl:
                return data
            else:
                del self.local_cache[key]

        # Try Redis cache
        cached_data = self.redis_client.get(key)
        if cached_data:
            return cached_data

        return None

    def set_cache(self, key: str, value: Any, ttl: int = 300):
        # Set in both local and Redis
        self.local_cache[key] = (value, time.time())
        self.redis_client.setex(key, ttl, value)

# Decorator for caching
def cache_result(ttl: int = 300):
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
            cache_manager = CacheManager()

            cached_result = cache_manager.get_from_cache(cache_key)
            if cached_result:
                return cached_result

            result = func(*args, **kwargs)
            cache_manager.set_cache(cache_key, result, ttl)
            return result
        return wrapper
    return decorator
```

## 4. How would you design a database abstraction layer in Python?

**Answer:**

A well-designed database abstraction layer should provide:
- Database agnostic operations
- Connection pooling
- Transaction management
- Query optimization
- Migration support

```python
from abc import ABC, abstractmethod
from contextlib import contextmanager
import sqlalchemy as sa
from sqlalchemy.orm import sessionmaker
from typing import List, Dict, Any, Optional

class DatabaseInterface(ABC):
    @abstractmethod
    def connect(self):
        pass

    @abstractmethod
    def execute_query(self, query: str, params: Dict = None):
        pass

    @abstractmethod
    def get_session(self):
        pass

class PostgreSQLAdapter(DatabaseInterface):
    def __init__(self, connection_string: str):
        self.engine = sa.create_engine(
            connection_string,
            pool_size=20,
            max_overflow=30,
            pool_pre_ping=True
        )
        self.SessionLocal = sessionmaker(bind=self.engine)

    def connect(self):
        return self.engine.connect()

    @contextmanager
    def get_session(self):
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()

    def execute_query(self, query: str, params: Dict = None):
        with self.get_session() as session:
            return session.execute(sa.text(query), params or {})

class Repository:
    def __init__(self, db_adapter: DatabaseInterface):
        self.db = db_adapter

    def find_by_id(self, table: str, id: int):
        query = f"SELECT * FROM {table} WHERE id = :id"
        return self.db.execute_query(query, {"id": id})

    def create(self, table: str, data: Dict):
        columns = ", ".join(data.keys())
        placeholders = ", ".join([f":{key}" for key in data.keys()])
        query = f"INSERT INTO {table} ({columns}) VALUES ({placeholders})"
        return self.db.execute_query(query, data)
```

## 5. Explain how you would implement event-driven architecture in Python.

**Answer:**

Event-driven architecture promotes loose coupling and scalability through asynchronous event processing:

```python
import asyncio
from typing import Dict, List, Callable, Any
from dataclasses import dataclass
from datetime import datetime
import json

@dataclass
class Event:
    event_type: str
    data: Dict[str, Any]
    timestamp: datetime
    correlation_id: str

class EventBus:
    def __init__(self):
        self.subscribers: Dict[str, List[Callable]] = {}
        self.middleware: List[Callable] = []

    def subscribe(self, event_type: str, handler: Callable):
        if event_type not in self.subscribers:
            self.subscribers[event_type] = []
        self.subscribers[event_type].append(handler)

    def add_middleware(self, middleware: Callable):
        self.middleware.append(middleware)

    async def publish(self, event: Event):
        # Apply middleware
        for middleware in self.middleware:
            event = await middleware(event)

        # Notify subscribers
        if event.event_type in self.subscribers:
            tasks = []
            for handler in self.subscribers[event.event_type]:
                tasks.append(asyncio.create_task(handler(event)))
            await asyncio.gather(*tasks, return_exceptions=True)

# Event handlers
class UserService:
    def __init__(self, event_bus: EventBus):
        self.event_bus = event_bus
        self._register_handlers()

    def _register_handlers(self):
        self.event_bus.subscribe("user.created", self.handle_user_created)
        self.event_bus.subscribe("user.updated", self.handle_user_updated)

    async def handle_user_created(self, event: Event):
        print(f"User created: {event.data}")
        # Send welcome email, create profile, etc.

    async def handle_user_updated(self, event: Event):
        print(f"User updated: {event.data}")
        # Update cache, notify other services, etc.

# Middleware example
async def logging_middleware(event: Event) -> Event:
    print(f"Event logged: {event.event_type} at {event.timestamp}")
    return event

# Usage
event_bus = EventBus()
event_bus.add_middleware(logging_middleware)
user_service = UserService(event_bus)

# Publishing events
user_created_event = Event(
    event_type="user.created",
    data={"user_id": 123, "email": "<EMAIL>"},
    timestamp=datetime.now(),
    correlation_id="req-123"
)

await event_bus.publish(user_created_event)

## 6. How would you implement a robust error handling and logging system?

**Answer:**

A comprehensive error handling system should include structured logging, error categorization, and monitoring:

```python
import logging
import traceback
import sys
from typing import Optional, Dict, Any
from enum import Enum
from dataclasses import dataclass
from datetime import datetime
import json

class ErrorSeverity(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class ErrorContext:
    user_id: Optional[str] = None
    request_id: Optional[str] = None
    service_name: Optional[str] = None
    additional_data: Optional[Dict[str, Any]] = None

class CustomException(Exception):
    def __init__(self, message: str, error_code: str, severity: ErrorSeverity, context: ErrorContext = None):
        self.message = message
        self.error_code = error_code
        self.severity = severity
        self.context = context or ErrorContext()
        super().__init__(self.message)

class StructuredLogger:
    def __init__(self, service_name: str):
        self.service_name = service_name
        self.logger = logging.getLogger(service_name)
        self._setup_logger()

    def _setup_logger(self):
        handler = logging.StreamHandler(sys.stdout)
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)

    def log_error(self, error: Exception, context: ErrorContext = None):
        error_data = {
            "timestamp": datetime.now().isoformat(),
            "service": self.service_name,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "traceback": traceback.format_exc(),
        }

        if isinstance(error, CustomException):
            error_data.update({
                "error_code": error.error_code,
                "severity": error.severity.value,
                "context": error.context.__dict__ if error.context else {}
            })

        if context:
            error_data["context"] = context.__dict__

        self.logger.error(json.dumps(error_data))

    def log_info(self, message: str, extra_data: Dict = None):
        log_data = {
            "timestamp": datetime.now().isoformat(),
            "service": self.service_name,
            "message": message,
            "level": "info"
        }
        if extra_data:
            log_data.update(extra_data)

        self.logger.info(json.dumps(log_data))

class ErrorHandler:
    def __init__(self, logger: StructuredLogger):
        self.logger = logger

    def handle_error(self, error: Exception, context: ErrorContext = None) -> Dict[str, Any]:
        self.logger.log_error(error, context)

        if isinstance(error, CustomException):
            return {
                "error_code": error.error_code,
                "message": error.message,
                "severity": error.severity.value
            }

        return {
            "error_code": "INTERNAL_ERROR",
            "message": "An unexpected error occurred",
            "severity": ErrorSeverity.HIGH.value
        }

# Decorator for error handling
def handle_exceptions(logger: StructuredLogger):
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                context = ErrorContext(
                    service_name="user_service",
                    additional_data={"function": func.__name__, "args": str(args)}
                )
                error_handler = ErrorHandler(logger)
                return error_handler.handle_error(e, context)
        return wrapper
    return decorator

# Usage example
logger = StructuredLogger("user_service")

@handle_exceptions(logger)
def create_user(user_data: Dict):
    if not user_data.get("email"):
        raise CustomException(
            message="Email is required",
            error_code="VALIDATION_ERROR",
            severity=ErrorSeverity.MEDIUM,
            context=ErrorContext(additional_data={"field": "email"})
        )
    # User creation logic here
    return {"user_id": 123}

## 7. Explain how you would implement authentication and authorization in a distributed system.

**Answer:**

For distributed systems, I would implement JWT-based authentication with role-based access control:

```python
import jwt
import bcrypt
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from functools import wraps
from flask import request, jsonify

class AuthService:
    def __init__(self, secret_key: str, algorithm: str = "HS256"):
        self.secret_key = secret_key
        self.algorithm = algorithm
        self.token_expiry = timedelta(hours=24)
        self.refresh_token_expiry = timedelta(days=7)

    def hash_password(self, password: str) -> str:
        return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

    def verify_password(self, password: str, hashed: str) -> bool:
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))

    def generate_tokens(self, user_id: str, roles: List[str]) -> Dict[str, str]:
        now = datetime.utcnow()

        access_payload = {
            "user_id": user_id,
            "roles": roles,
            "exp": now + self.token_expiry,
            "iat": now,
            "type": "access"
        }

        refresh_payload = {
            "user_id": user_id,
            "exp": now + self.refresh_token_expiry,
            "iat": now,
            "type": "refresh"
        }

        access_token = jwt.encode(access_payload, self.secret_key, algorithm=self.algorithm)
        refresh_token = jwt.encode(refresh_payload, self.secret_key, algorithm=self.algorithm)

        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "expires_in": int(self.token_expiry.total_seconds())
        }

    def verify_token(self, token: str) -> Optional[Dict]:
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return payload
        except jwt.ExpiredSignatureError:
            return None
        except jwt.InvalidTokenError:
            return None

class RoleBasedAccessControl:
    def __init__(self):
        self.permissions = {
            "admin": ["read", "write", "delete", "manage_users"],
            "user": ["read", "write"],
            "viewer": ["read"]
        }

    def has_permission(self, user_roles: List[str], required_permission: str) -> bool:
        for role in user_roles:
            if role in self.permissions and required_permission in self.permissions[role]:
                return True
        return False

# Decorators for authentication and authorization
def require_auth(auth_service: AuthService):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            token = request.headers.get('Authorization')
            if not token:
                return jsonify({"error": "Token missing"}), 401

            if token.startswith('Bearer '):
                token = token[7:]

            payload = auth_service.verify_token(token)
            if not payload:
                return jsonify({"error": "Invalid token"}), 401

            request.current_user = payload
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_permission(permission: str, rbac: RoleBasedAccessControl):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not hasattr(request, 'current_user'):
                return jsonify({"error": "Authentication required"}), 401

            user_roles = request.current_user.get('roles', [])
            if not rbac.has_permission(user_roles, permission):
                return jsonify({"error": "Insufficient permissions"}), 403

            return f(*args, **kwargs)
        return decorated_function
    return decorator

# Usage example
auth_service = AuthService("your-secret-key")
rbac = RoleBasedAccessControl()

@require_auth(auth_service)
@require_permission("write", rbac)
def create_resource():
    return jsonify({"message": "Resource created"})

## 8. How would you design a data pipeline architecture for processing large datasets?

**Answer:**

A robust data pipeline should handle batch and stream processing with fault tolerance:

```python
import asyncio
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Iterator
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import pandas as pd
from datetime import datetime

@dataclass
class DataBatch:
    data: List[Dict[str, Any]]
    batch_id: str
    timestamp: datetime
    metadata: Dict[str, Any] = None

class DataProcessor(ABC):
    @abstractmethod
    async def process(self, batch: DataBatch) -> DataBatch:
        pass

class ValidationProcessor(DataProcessor):
    def __init__(self, schema: Dict[str, type]):
        self.schema = schema

    async def process(self, batch: DataBatch) -> DataBatch:
        validated_data = []
        for record in batch.data:
            if self._validate_record(record):
                validated_data.append(record)

        return DataBatch(
            data=validated_data,
            batch_id=batch.batch_id,
            timestamp=batch.timestamp,
            metadata={"validation_passed": len(validated_data)}
        )

    def _validate_record(self, record: Dict[str, Any]) -> bool:
        for field, expected_type in self.schema.items():
            if field not in record or not isinstance(record[field], expected_type):
                return False
        return True

class TransformationProcessor(DataProcessor):
    def __init__(self, transformations: List[callable]):
        self.transformations = transformations

    async def process(self, batch: DataBatch) -> DataBatch:
        transformed_data = []
        for record in batch.data:
            for transform in self.transformations:
                record = transform(record)
            transformed_data.append(record)

        return DataBatch(
            data=transformed_data,
            batch_id=batch.batch_id,
            timestamp=batch.timestamp,
            metadata=batch.metadata
        )

class DataPipeline:
    def __init__(self, processors: List[DataProcessor], max_workers: int = 4):
        self.processors = processors
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)

    async def process_batch(self, batch: DataBatch) -> DataBatch:
        current_batch = batch
        for processor in self.processors:
            current_batch = await processor.process(current_batch)
        return current_batch

    async def process_stream(self, data_stream: Iterator[DataBatch]):
        tasks = []
        async for batch in data_stream:
            if len(tasks) >= self.max_workers:
                # Wait for at least one task to complete
                done, pending = await asyncio.wait(tasks, return_when=asyncio.FIRST_COMPLETED)
                tasks = list(pending)

                # Process completed tasks
                for task in done:
                    result = await task
                    yield result

            # Add new task
            task = asyncio.create_task(self.process_batch(batch))
            tasks.append(task)

        # Process remaining tasks
        if tasks:
            results = await asyncio.gather(*tasks)
            for result in results:
                yield result

# Data source and sink abstractions
class DataSource(ABC):
    @abstractmethod
    async def read_batches(self) -> Iterator[DataBatch]:
        pass

class DataSink(ABC):
    @abstractmethod
    async def write_batch(self, batch: DataBatch):
        pass

class DatabaseSink(DataSink):
    def __init__(self, connection_string: str):
        self.connection_string = connection_string

    async def write_batch(self, batch: DataBatch):
        # Simulate database write
        print(f"Writing batch {batch.batch_id} with {len(batch.data)} records")
        await asyncio.sleep(0.1)  # Simulate I/O

# Usage example
schema = {"id": int, "name": str, "email": str}
transformations = [
    lambda record: {**record, "email": record["email"].lower()},
    lambda record: {**record, "processed_at": datetime.now().isoformat()}
]

pipeline = DataPipeline([
    ValidationProcessor(schema),
    TransformationProcessor(transformations)
])

# Process data
async def run_pipeline():
    # Mock data stream
    async def mock_data_stream():
        for i in range(10):
            yield DataBatch(
                data=[{"id": i, "name": f"User{i}", "email": f"USER{i}@EXAMPLE.COM"}],
                batch_id=f"batch_{i}",
                timestamp=datetime.now()
            )

    sink = DatabaseSink("postgresql://...")

    async for processed_batch in pipeline.process_stream(mock_data_stream()):
        await sink.write_batch(processed_batch)

## 9. How would you implement a configuration management system for different environments?

**Answer:**

A flexible configuration system should support multiple environments, secret management, and runtime updates:

```python
import os
import json
import yaml
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, field
from enum import Enum
import boto3
from pathlib import Path

class Environment(Enum):
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"
    TESTING = "testing"

@dataclass
class DatabaseConfig:
    host: str
    port: int
    database: str
    username: str
    password: str
    pool_size: int = 10

    @property
    def connection_string(self) -> str:
        return f"postgresql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"

@dataclass
class RedisConfig:
    host: str
    port: int = 6379
    db: int = 0
    password: Optional[str] = None

@dataclass
class AppConfig:
    environment: Environment
    debug: bool
    secret_key: str
    database: DatabaseConfig
    redis: RedisConfig
    api_keys: Dict[str, str] = field(default_factory=dict)
    feature_flags: Dict[str, bool] = field(default_factory=dict)

class ConfigLoader:
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.secret_manager = SecretManager()

    def load_config(self, environment: Environment) -> AppConfig:
        # Load base configuration
        base_config = self._load_file("base.yaml")

        # Load environment-specific configuration
        env_config = self._load_file(f"{environment.value}.yaml")

        # Merge configurations
        merged_config = self._merge_configs(base_config, env_config)

        # Load secrets
        secrets = self.secret_manager.get_secrets(environment)
        merged_config.update(secrets)

        # Override with environment variables
        self._apply_env_overrides(merged_config)

        return self._create_app_config(merged_config, environment)

    def _load_file(self, filename: str) -> Dict[str, Any]:
        file_path = self.config_dir / filename
        if not file_path.exists():
            return {}

        with open(file_path, 'r') as f:
            if filename.endswith('.yaml') or filename.endswith('.yml'):
                return yaml.safe_load(f) or {}
            elif filename.endswith('.json'):
                return json.load(f)
        return {}

    def _merge_configs(self, base: Dict, override: Dict) -> Dict:
        result = base.copy()
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        return result

    def _apply_env_overrides(self, config: Dict[str, Any]):
        # Support nested environment variable overrides
        # e.g., DATABASE__HOST=localhost
        for key, value in os.environ.items():
            if '__' in key:
                keys = key.lower().split('__')
                current = config
                for k in keys[:-1]:
                    if k not in current:
                        current[k] = {}
                    current = current[k]
                current[keys[-1]] = self._convert_env_value(value)

    def _convert_env_value(self, value: str) -> Union[str, int, bool]:
        # Convert string environment variables to appropriate types
        if value.lower() in ('true', 'false'):
            return value.lower() == 'true'
        try:
            return int(value)
        except ValueError:
            return value

    def _create_app_config(self, config: Dict[str, Any], environment: Environment) -> AppConfig:
        return AppConfig(
            environment=environment,
            debug=config.get('debug', False),
            secret_key=config.get('secret_key', ''),
            database=DatabaseConfig(**config.get('database', {})),
            redis=RedisConfig(**config.get('redis', {})),
            api_keys=config.get('api_keys', {}),
            feature_flags=config.get('feature_flags', {})
        )

class SecretManager:
    def __init__(self):
        self.aws_client = boto3.client('secretsmanager') if self._is_aws_available() else None

    def _is_aws_available(self) -> bool:
        try:
            boto3.client('secretsmanager')
            return True
        except Exception:
            return False

    def get_secrets(self, environment: Environment) -> Dict[str, Any]:
        if self.aws_client:
            return self._get_aws_secrets(environment)
        else:
            return self._get_local_secrets(environment)

    def _get_aws_secrets(self, environment: Environment) -> Dict[str, Any]:
        secret_name = f"app-secrets-{environment.value}"
        try:
            response = self.aws_client.get_secret_value(SecretId=secret_name)
            return json.loads(response['SecretString'])
        except Exception:
            return {}

    def _get_local_secrets(self, environment: Environment) -> Dict[str, Any]:
        # For local development, read from .env files
        env_file = f".env.{environment.value}"
        secrets = {}
        if os.path.exists(env_file):
            with open(env_file, 'r') as f:
                for line in f:
                    if '=' in line and not line.startswith('#'):
                        key, value = line.strip().split('=', 1)
                        secrets[key.lower()] = value
        return secrets

# Singleton configuration manager
class ConfigManager:
    _instance = None
    _config = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def initialize(self, environment: Environment):
        if self._config is None:
            loader = ConfigLoader()
            self._config = loader.load_config(environment)

    @property
    def config(self) -> AppConfig:
        if self._config is None:
            raise RuntimeError("Configuration not initialized")
        return self._config

# Usage
config_manager = ConfigManager()
config_manager.initialize(Environment.DEVELOPMENT)
config = config_manager.config

print(f"Database URL: {config.database.connection_string}")
print(f"Debug mode: {config.debug}")

## 10. How would you design a monitoring and observability system for Python applications?

**Answer:**

A comprehensive observability system should include metrics, logging, tracing, and health checks:

```python
import time
import psutil
from typing import Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime
import asyncio
from prometheus_client import Counter, Histogram, Gauge, start_http_server
import structlog
from opentelemetry import trace
from opentelemetry.exporter.jaeger.thrift import JaegerExporter
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor

# Metrics collection
class MetricsCollector:
    def __init__(self):
        # Prometheus metrics
        self.request_count = Counter('http_requests_total', 'Total HTTP requests', ['method', 'endpoint', 'status'])
        self.request_duration = Histogram('http_request_duration_seconds', 'HTTP request duration')
        self.active_connections = Gauge('active_connections', 'Number of active connections')
        self.memory_usage = Gauge('memory_usage_bytes', 'Memory usage in bytes')
        self.cpu_usage = Gauge('cpu_usage_percent', 'CPU usage percentage')

    def record_request(self, method: str, endpoint: str, status: int, duration: float):
        self.request_count.labels(method=method, endpoint=endpoint, status=status).inc()
        self.request_duration.observe(duration)

    def update_system_metrics(self):
        # Update system metrics
        memory = psutil.virtual_memory()
        self.memory_usage.set(memory.used)
        self.cpu_usage.set(psutil.cpu_percent())

# Distributed tracing
class TracingManager:
    def __init__(self, service_name: str, jaeger_endpoint: str):
        self.service_name = service_name

        # Configure tracing
        trace.set_tracer_provider(TracerProvider())
        tracer = trace.get_tracer(__name__)

        jaeger_exporter = JaegerExporter(
            agent_host_name="localhost",
            agent_port=6831,
        )

        span_processor = BatchSpanProcessor(jaeger_exporter)
        trace.get_tracer_provider().add_span_processor(span_processor)

        self.tracer = tracer

    def start_span(self, operation_name: str, parent_context=None):
        return self.tracer.start_span(operation_name, context=parent_context)

# Health check system
@dataclass
class HealthCheckResult:
    name: str
    status: str  # "healthy", "unhealthy", "degraded"
    message: str
    timestamp: datetime
    response_time_ms: float

class HealthChecker:
    def __init__(self):
        self.checks = {}

    def register_check(self, name: str, check_func: callable):
        self.checks[name] = check_func

    async def run_all_checks(self) -> Dict[str, HealthCheckResult]:
        results = {}
        for name, check_func in self.checks.items():
            start_time = time.time()
            try:
                result = await check_func()
                response_time = (time.time() - start_time) * 1000
                results[name] = HealthCheckResult(
                    name=name,
                    status="healthy" if result else "unhealthy",
                    message="OK" if result else "Check failed",
                    timestamp=datetime.now(),
                    response_time_ms=response_time
                )
            except Exception as e:
                response_time = (time.time() - start_time) * 1000
                results[name] = HealthCheckResult(
                    name=name,
                    status="unhealthy",
                    message=str(e),
                    timestamp=datetime.now(),
                    response_time_ms=response_time
                )
        return results

# Application monitoring
class ApplicationMonitor:
    def __init__(self, service_name: str):
        self.service_name = service_name
        self.metrics = MetricsCollector()
        self.tracing = TracingManager(service_name, "http://localhost:14268/api/traces")
        self.health_checker = HealthChecker()
        self.logger = structlog.get_logger()

        # Register default health checks
        self._register_default_health_checks()

        # Start metrics server
        start_http_server(8000)

    def _register_default_health_checks(self):
        async def database_check():
            # Simulate database connectivity check
            await asyncio.sleep(0.01)
            return True

        async def redis_check():
            # Simulate Redis connectivity check
            await asyncio.sleep(0.01)
            return True

        self.health_checker.register_check("database", database_check)
        self.health_checker.register_check("redis", redis_check)

    async def monitor_request(self, method: str, endpoint: str, handler: callable):
        start_time = time.time()

        with self.tracing.start_span(f"{method} {endpoint}") as span:
            try:
                result = await handler()
                status = 200
                span.set_attribute("http.status_code", status)
                return result
            except Exception as e:
                status = 500
                span.set_attribute("http.status_code", status)
                span.set_attribute("error", True)
                span.set_attribute("error.message", str(e))
                self.logger.error("Request failed", error=str(e), endpoint=endpoint)
                raise
            finally:
                duration = time.time() - start_time
                self.metrics.record_request(method, endpoint, status, duration)

    async def get_health_status(self) -> Dict[str, Any]:
        health_results = await self.health_checker.run_all_checks()

        overall_status = "healthy"
        if any(result.status == "unhealthy" for result in health_results.values()):
            overall_status = "unhealthy"
        elif any(result.status == "degraded" for result in health_results.values()):
            overall_status = "degraded"

        return {
            "status": overall_status,
            "timestamp": datetime.now().isoformat(),
            "service": self.service_name,
            "checks": {name: {
                "status": result.status,
                "message": result.message,
                "response_time_ms": result.response_time_ms
            } for name, result in health_results.items()}
        }

    def start_background_monitoring(self):
        async def monitor_loop():
            while True:
                self.metrics.update_system_metrics()
                await asyncio.sleep(30)  # Update every 30 seconds

        asyncio.create_task(monitor_loop())

# Usage example
monitor = ApplicationMonitor("user-service")
monitor.start_background_monitoring()

# In your request handler
async def handle_user_request():
    # Your business logic here
    return {"user_id": 123}

# Wrap with monitoring
async def monitored_endpoint():
    return await monitor.monitor_request("GET", "/users", handle_user_request)
```

---

## Summary

These questions cover the essential architectural concepts for Python development:

1. **Microservices Architecture** - Service design and communication patterns
2. **Concurrency Models** - Threading, multiprocessing, and asyncio
3. **Caching Strategies** - Multi-level caching implementation
4. **Database Abstraction** - Repository pattern and connection management
5. **Event-Driven Architecture** - Asynchronous event processing
6. **Error Handling** - Structured logging and error management
7. **Authentication/Authorization** - JWT and RBAC implementation
8. **Data Pipeline Architecture** - Batch and stream processing
9. **Configuration Management** - Environment-specific configurations
10. **Monitoring & Observability** - Metrics, tracing, and health checks

Each answer demonstrates practical implementation patterns that are commonly used in production Python applications.
```
```
```
```
```