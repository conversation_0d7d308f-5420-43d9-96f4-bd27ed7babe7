
# project-summary

Please find project summary as below.

# Upwork:

* Technical Hiring Consultant for Overseas Group (Hong kong)

* Experienced Technical Manager for BigToe (USA)

    -   Tech Stack: PHP, Ruby on Rails, Laravel, ReactJS, NodeJS, MySQL, MongoDB, AWS, GIT, Jenkins, <PERSON>er, SonarQube, PHPUnit, RSpec
    -   Team Size: 30

* https://freemansperspective.com/ (WordPress, THESIS, Wishlist)

* http://aantonop.com (WordPress - Elementor)

* https://develop.cevavision.spherewms.com/ (Lara<PERSON> 7, Angular 10)

* http://v2.spherewms.com/ (Laravel 5.3, Angular 7)

* https://sourceii.com/ (AngularJS 1.6, Ruby on Rails)

* https://vendor.sourceii.com/ (AngularJS 1.6, Ruby on Rails)


# Full-time Employment:

* WoltersKluwer - Nursing Education - (PHP 7.4/8, Symfony, Angular 8, Python, MySQL, AWS Glue, SQS,
Redis, GIT, <PERSON>, <PERSON>, SonarQube, GoLang)

* RLDatix/DCIQ - Health Care - (P 7.4, Symfony, AngularJS, NodeJS, MySQL, MsSQL, MongoDB,
GIT, Jenkins, Docker, Kafka)

* Digital WayFinding / EverBridge - Health Care -  (PHP 7.4, CakePHP, Laravel, Ruby on Rails, VueJS, MySQL, AWS, GIT,
Docker, RabbitMQ)

* https://dmehub.com/ - Apria/DMEHub - Health Care - (PHP 7.3, Laravel 5.8, VueJS, Redis, MySQL, GIT, Jenkins,
AppDynamics, Veracode (for code scanning), Grafana) 

* https://www.aventri.com/ - (PHP 5.3, PHP 7.0, Laravel, Lumen, Angular v2, VueJS, NodeJS -
ExpressJS, Elastic Search, Redis, MySQL, GIT, RabbitMQ)

* https://trackx.com/ - (Ruby on Rails, AngularJS 1.4, DHTMLX, JavaScript, MSSQL, SVN)

* Yard Management (Ruby on Rails, AngularJS 1.4, DHTMLX, JavaScript, MSSQL, SVN)

* https://connect.trimble.com/ - (AngularJS 1.4, Dojo, Java)

* https://imeetcentral.com/ (BackboneJS, PHP 5.3, PostGreSQL)

* Glam Media (Ruby on Rails, PHP 5.3, Zend 1.10, YUI, R3, MySQL 5.0, Apache 2.0,
Git, PhpDocs, PhpUnit)

* https://www.usautoparts.com/ (PHP 5.3, Ruby on Rails, Python, Perl, MySQL, Zend)

* https://www.avalara.com/ (CakePHP, MySQL)

* Synechron Technologies Pvt Ltd (PHP 5.3, Facebook API, Googlemap API, MySQL, WordPress, Drupal)

* https://hurix.com/ - McGrawHill (PHP 5.3, MySQL, Smarty)

* https://www.shaadi.com/ (PHP 4, PHP 5+, MySQL)

* https://zeetv.zee5.com/ (Joomla)

* https://www.bigadda.in/ (PHP, MySQL)

LinkedIn: https://www.linkedin.com/in/manishlinkedin/

GitHub:
https://github.com/mpatel2280?tab=repositories

DockerHub:
https://hub.docker.com/repositories/patelm2280
