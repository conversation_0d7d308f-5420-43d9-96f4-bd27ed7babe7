- Can you describe a situation where you had to design a RESTful API? How did you ensure it was scalable and maintainable?
    - Planning the API Endpoints
        - Example:
            - GET /products (list products)
            - POST /orders (create an order)
    - Implemented API Versioning
    - Optimized for Performance
        - Database Optimization
            - Indexes, Eager loding, Query cache
        - Caching
            - Redis or other cache mechanism
        - Records limits with Pagination, Lazy Loading, Eager Loading
    - Apply Security best practices
        - Authentication and Authorization
    - Scalable Design Patterns
        - Background Job
        - Rate limit, Throtteling
    - Error Handling
    - Logging and Monitoring
    - Documented the API using Swagger via the rswag gem
    - Maintain backward compatibility with any version upgradation 
- Have you worked with versioning in RESTful APIs? Can you provide an example where versioning was necessary?
    - Versioning is crucial for maintaining backward compatibility when making changes to an API, such as modifying request or response formats, adding or removing fields, or deprecating end-points. It allows you to release new features without disrupting existing clients using the older API version.
    - Route-based Versioning
    - Controller Organization
        - Each API version has its own controllers, allowing you to separate logic
    - Header-based Versioning
        - Alternatively, you can version APIs based on custom headers
- What are the key differences between RESTful and GraphQL APIs? In what scenarios would you choose one over the other for a SaaS platform?
    - ![alt text](<images/restful-vs-graphql.png>)
- What are the key features of REST?
    - Statelessness: Each request is independent, and the server does not store client context.
    - Cacheability: Responses can be marked as cacheable to improve performance.
    - Client-Server Architecture: Separation of concerns between client and server.
    - Uniform Interface: A standard way of interaction (resources accessed via URLs and standard HTTP methods).
- What is the difference between PUT and PATCH?
    - PUT: Updates the entire resource. For example, when updating user data, all fields must be provided.
    - PATCH: Updates partial fields of the resource. Ideal for small changes, like modifying a single property.
- security in a RESTful API?
    - Use HTTPS to encrypt data.
    - Implement authentication (e.g., OAuth, JWT).
    - Use rate limiting to prevent abuse.
    - Validate input to prevent injection attacks.
    - Use API keys or tokens to manage access.
- What is HATEOAS, and why is it important?
    - HATEOAS (Hypermedia as the Engine of Application State) ensures REST APIs include links in their responses for further actions. 
    - Example:
    ```php
    {
        "user": {
            "id": 1,
            "name": "John Doe",
            "links": [
            { "rel": "self", "href": "/users/1" },
            { "rel": "orders", "href": "/users/1/orders" }
            ]
        }
    }
    ```
- How would you handle rate-limiting in RESTful APIs?
    - Token bucket algorithms.
    - HTTP headers (e.g., X-RateLimit-Limit, X-RateLimit-Remaining) to inform clients about limits.

- Types of APIs Based on Design Style
    - REST APIs
        - Purpose: Built on the principles of RESTful architecture using HTTP.
        - Example: JSON-based APIs for web services.
        - Key Features: Stateless, scalable, and cacheable.
    - SOAP APIs
        - Purpose: Built on Simple Object Access Protocol (SOAP) using XML.
        - Example: Legacy financial systems.      
        - Key Features: High security (WS-Security), more rigid than REST.
    - GraphQL APIs
        - Purpose: A query language for APIs allowing clients to specify the data they need.
        - Example: APIs for modern web/mobile applications.
        - Key Features: Flexible and efficient, but requires more complex setup.
    - gRPC APIs
        - Purpose: High-performance, RPC-based APIs often used in microservices.
        - Example: Communication between microservices in a distributed system.
        - Key Features: Binary protocol (Protocol Buffers), low latency, and high speed.
    - WebSocket APIs
        - Purpose: For real-time, full-duplex communication.
        - Example: Chat applications, live notifications.
        - Key Features: Persistent connections, low latency.


- https://www.restapitutorial.com/
