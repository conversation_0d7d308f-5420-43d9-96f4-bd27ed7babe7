---------------------------------------------------------------------------------------------------------------------
### General
---------------------------------------------------------------------------------------------------------------------
- debugging 
- analytical skill
- session management
- security
- performance
- middleware
- routing
- active records
- microservices
- SaaS
- rest api
- design patterns
- solid principles
- indexes
- slow query optimization
- oops
- ci/cd
- unit testing
- tdd/bdd
- graphql
- cache - Redis vs Memcached 
- docker
    - docker volume
    - docker image
    - docker containers
    - can we up container docker without dokcer-compose.yml file?
- template
- aws
    - serverless - A<PERSON> Lambda, Amazon EventBridge, AWS IoT Core, Amazon API Gateway, Amazon S3, AWS Step Functions, Amazon DynamoDB
    - serverbase - AWS EC2, Amazon ECS, AWS Batch, Amazon VPC, Amazon RDS, Amazon EKS
- Deployment Strategy
- Agile scrum ceremonies
    - Sprint planning, Daily scrum, Sprint review, Sprint retrospective, Product backlog grooming 

---------------------------------------------------------------------------------------------------------------------
### PHP
---------------------------------------------------------------------------------------------------------------------
- Routes 
 - API versionning
    - What is Subdomain-based Versioning and how we can do that (v1.api.mysite.com)
- OOPs - https://github.com/mpatel2280/interview-prep/blob/master/php.md#oops
    - By Default instance methods are private or public?
        - public by default, then only class instance will be able to access it.
    - Can we create private constructor? - No
    - Does PHP support static class? - No
    - static method
        - static methods are implemented as class methods. These are methods that belong to the class itself, not instances of the class.
        - In which scenario we should use static method?
        - Can static methods be overridden in PHP?
        - Can we use $this in static method
    - What type of polymorphism supported by php?
    - Does PHP supports multiple inheritance?     
    - How many ways we can achieve multiple inheritance? 
    - Traits
    - Problem of Multiple inheritance
    - 
    - Interface
    - Interface & Abstract class (These 2 lead tightly coupled code)
        - Can we create private method in interface? - No, all method should be public only
        - Can we create Abstract class instance?  - No
        - Difference between interface and abstract class
        - What is the use of interface if it does not have method implementation logic
    - Can an interface extend another interface in PHP?
    - Can you implement multiple interfaces with methods of the same name in PHP?
    - Can an interface contain properties in PHP?
    - Can an interface have static methods in PHP?
    - What happens if two interfaces with conflicting method signatures are implemented in a class?
    - Can a class implement an interface and extend a class simultaneously?
    - Interfaces Vs Concrete Class
    - 
    - Abstract Class
    - Can we create an instance of an abstract class in PHP? - No
    - Can an abstract class have both abstract and concrete methods in PHP?
    - Can an abstract class implement an interface in PHP?
    - Can an abstract class extend another abstract class in PHP?
    - Can an abstract class be final in PHP?
    - Can you have private abstract methods in PHP?
    - 
    - method overloading and method overriding
    - Static polymorphism (compile-time polymorphism - method overloading) - Not supported by Ruby, but there is a way to achieve this
    - Dynamic polymorphism (run-time polymorphism - method overriding) - Supported by Ruby
    - What is the use of final class?
        - Can we extends the final class in child class? No
        - Can we create instance of the final class? Yes
    - How to extend the behavior of a final class in php?
    - What is Dependency Injection in PHP? 
    - How to access private properties in php?

- Security techniques - https://github.com/mpatel2280/interview-prep/blob/master/php.md#security-techniques
    - directory traversal attacks in file uploads
    - What is CSRF token, how to enable it and in which scenario we should disable it.
    - Security related settings

- Performance Improvement techniques - https://github.com/mpatel2280/interview-prep/blob/master/php.md#performance-improvement-techniques

- Error handling Qs - https://github.com/mpatel2280/interview-prep/blob/master/php.md#error-handling-qs 

- Sessions - https://github.com/mpatel2280/interview-prep/blob/master/php.md#sessions--jwt
    - If we set session.use_only_cookies and then disable the cookies on client browser, will session still works in php?
    - If session.use_only_cookies set to 1 and session.use_trans_sid set to 1 then which one get precedance ?
        - session.use_only_cookies get the precedance 
    - How to set session expiration in php
    - How to handle session ideal timeout in php
      
- PHPUnit
    - What is the usage of setUp() and tearDown() methods?
    - How we can test private or protected methods?
    - What is a Data Provider?
    - 
    - Difference between assertEquals and assertSame?
    - What are few Common Assertions?
    -
        | Method                                | Purpose                           |
        |---------------------------------------|-----------------------------------|
        | `assertTrue($condition)`              | Asserts that condition is true    |
        | `assertFalse($condition)`             | Asserts that condition is false   |
        | `assertEquals($expected, $actual)`    | Value equality (ignores type)     |
        | `assertSame($expected, $actual)`      | Value + type equality             |
        | `assertNotEquals($expected, $actual)` | Value inequality                  |
        | `assertNotSame($expected, $actual)`   | Value or type inequality          |
        | `assertNull($value)`                  | Asserts value is null             |
        | `assertNotNull($value)`               | Asserts value is not null         |
        | `assertCount($count, $array)`          | Asserts array count               |
        | `assertInstanceOf(ClassName::class, $object)` | Asserts object type         |
      - 
     
- MicroService
    - Microservice design patterns (system-architect.md)
    - Synchronous vs. Asynchronous Communication
    - Microservice design patterns to aggregate and process data
        - https://github.com/mpatel2280/interview-prep/blob/master/system-architect.md#20-microservice-design-patterns-to-aggregate-and-process-data
    - 
- SaaS - https://github.com/mpatel2280/interview-prep/blob/master/system-architect.md#19-saas
    - Database design for a multi-tenant application

- Distributed System Architecture: 
    - https://github.com/mpatel2280/interview-prep/blob/master/system-architect.md#q-distributed-systems-architecture

- Design Patterns - https://github.com/mpatel2280/interview-prep/blob/master/ror.md#design-patterns
- https://www.onlineinterviewquestions.com/core-php-interview-questions/

  
