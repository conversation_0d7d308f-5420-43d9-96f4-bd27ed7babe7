- git config --global credential.helper 'cache --timeout=86400' --save cred for a day

- git fetch vs git pull
	- git fetch:
		- Downloads objects and refs from a remote repository without merging or modifying the working directory.
		- Example: git fetch origin main		
	- git pull:
		- Combines git fetch and git merge. It fetches updates and then merges them into the current branch.
		- Example: git pull origin main		
	- Key Difference:
		- git fetch updates the remote tracking branches but leaves your working branch unchanged.
		- git pull automatically updates and merges changes into your working branch.
		
- git merge vs git rebase
    - The difference between Git rebase and Git merge lies in the approach to integrating changes. Git rebase rewrites the commit history to produce a linear sequence of commits. Git merge combines two branches without altering existing history, often resulting in a divergent project history.
    - Ref: 
        - https://www.atlassian.com/git/tutorials/merging-vs-rebasing 
        - https://www.atlassian.com/git/tutorials/merging-vs-rebasing#the-golden-rule-of-rebasing
    - Git Merge:
        - Merge is a command that integrates changes from one branch into another.
        When you merge branches, Git creates a new commit that combines the changes of the merged branches, preserving the commit history of both branches.
        This creates a merge commit, which is a snapshot of the final state of the merged branches.
    - Git Rebase:
        - Rebase is a command that allows you to reapply commits from one branch onto another.
        When you rebase a branch onto another branch, Git moves the entire branch to begin on the tip of the other branch.
        It effectively rewrites the commit history, creating a linear sequence of commits.
        Rebase can be used to maintain a cleaner and more linear commit history, as it eliminates unnecessary merge commits.
    - When to Use Each:
        - Merge: Use merge when you want to integrate changes from one branch into another and preserve the commit history of both branches.
        - Rebase: Use rebase when you want to maintain a clean and linear commit history, especially for feature branches before merging them into a main branch.

- How to get commit history
    - git log -p

- git hooks
    - Ref:
        - https://www.atlassian.com/git/tutorials/git-hooks
    - Git hooks are scripts that run automatically every time a particular event occurs in a Git repository. They let you customize Git’s internal behavior and trigger customizable actions at key points in the development life cycle.

- how to rewrite git commit history - git commit --amend

- git stash
    - https://www.atlassian.com/git/tutorials/saving-changes/git-stash
    - git stash temporarily shelves (or stashes) changes you've made to your working copy so you can work on something else, and then come back and re-apply them later on.
    - git stash list
    - git stash apply

- git submodule

- git revert 
    - git revert commit-hash
    - git checkout commit-hash -- file

- git revert vs reset
    - ![alt text](<images/revert-vs-reset.png>)

- git lfs - https://git-lfs.com/
    - Git LFS (Large File Storage) enhances GitHub projects by handling large files separately from the repository, optimizing storage and bandwidth while ensuring efficient version control of large assets.

- git cherry-pick
    - git cherry-pick commit-hash

- git reflog
    - Tracks changes to HEAD (branch movements, resets, checkouts).
    - Useful for recovering lost commits after a git reset or other destructive operations.
    - Example: git reflog shows recent HEAD movements, and you can recover a commit using git checkout commit-hash

- How to recover deleted files
    - git checkout -- file-path
    - git reset HEAD file-path
    - git checkout commit-hash -- file-path

- How to recover deleted file
	- git checkout --filepath (unstaged deleted file)
	- git reset HEAD --filepath && git checkout --filepath (staged file)
	- git checkout commit-hash --filepath
	- git reflog && git checkout --reflog-hash --filepath

- GitHub vs GitLab
    - GitHub
        - Focuses on repository hosting with a strong emphasis on open-source collaboration
        - It’s widely used for public repositories and community-driven projects.
    - GitLab
        - Offers an integrated DevOps platform with features that cover the entire software development lifecycle.
        - Focuses on providing a self-hosted solution and built-in CI/CD pipelines.