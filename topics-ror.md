---------------------------------------------------------------------------------------------------------------------
### General
---------------------------------------------------------------------------------------------------------------------
- debugging 
- analytical skill
- session management
- security
- performance
- middleware
- routing
- active records
- microservices
- SaaS
- rest api
- design patterns
- solid principles
- indexes
- slow query optimization
- oops
- ci/cd
- unit testing
- tdd/bdd
- graphql
- cache - Redis vs Memcached 
- docker
    - docker volume
    - docker image
    - docker containers
    - can we up container docker without dokcer-compose.yml file?
- template
    - Haml (HTML abstraction markup language)
    - erb (Embedded Ruby)
- aws
    - serverless - AWS Lambda, Amazon EventBridge, AWS IoT Core, Amazon API Gateway, Amazon S3, AWS Step Functions, Amazon DynamoDB
    - serverbase - AWS EC2, Amazon ECS, AWS Batch, Amazon VPC, Amazon RDS, Amazon EKS
- Deployment Strategy
- Agile scrum ceremonies
    - Sprint planning, Daily scrum, Sprint review, Sprint retrospective, Product backlog grooming 

---------------------------------------------------------------------------------------------------------------------
### RoR
---------------------------------------------------------------------------------------------------------------------
- Routes 
    - https://github.com/mpatel2280/interview-prep/blob/master/ror.md#routes-qs
    - In Routing -> Singular Resource
    - Difference between member and collection routes
    - namespace and scope routing diff
    - How to API versioning - like api/v1/orders, api/v2/orders etc (namespace, member, collection)
- API versionning
    - What is Subdomain-based Versioning and how we can do that (v1.api.mysite.com)
- OOPs
    - https://github.com/mpatel2280/interview-prep/blob/master/ror.md#oops
    - constructor (initialize method)
    - destructor (using ObjectSpace.define_finalizer)
    - Singleton class
    - By Default instance methods are private or public?
        - public by default, then only class instance will be able to access it.
    - How to access private method from class object?
        - using send method => classObject.send(:private_method_name) Or for Ruby3+ classObject.__send__(:private_method_name)
    - static method
        - static methods are implemented as class methods. These are methods that belong to the class itself, not instances of the class.
        - In which scenario we should use static method?
    - Does Ruby supports multiple inheritance? 
    - How many ways we can achieve multiple inheritance? 
    - Problem of Multiple inheritance
    - If Same Method in Multiple Included Modules, which one get precedence?
    - Interface & Abstract class (These 2 lead tightly coupled code)
    - Static polymorphism (compile-time polymorphism - method overloading) - Not supported by Ruby, but there is a way to achieve this
    - Dynamic polymorphism (run-time polymorphism - method overriding) - Supported by Ruby
    - Duck Typing
    - metaprogramming - https://github.com/mpatel2280/interview-prep/blob/master/ror.md#metaprogamming
        -  Explain the use of class_eval and module_eval
        - Difference between class << self and self.method
        - What are the advantages and disadvantages of metaprogramming?
        - When should you avoid using metaprogramming?
- debugging tools and gems

- Security
    - https://github.com/mpatel2280/interview-prep/blob/master/ror.md#security-technique
    - gems (brakeman, bullet)
    - brakeman for SQL injection 
    - Security related gems
    - gems for Static code check
    - directory traversal attacks in file uploads
    - What is CSRF token, how to enable it and in which scenario we should disable it.

- Performance
    - https://guides.rubyonrails.org/tuning_performance_for_deployment.html
    - https://github.com/mpatel2280/interview-prep/blob/master/ror.md#performance-technique
    - Performance improvement gems
    - size vs count vs length
    - turbolink
    - Database scalling

- MicroService
    - Microservice design patterns (system-architect.md)
    - Synchronous vs. Asynchronous Communication
    - Microservice design patterns to aggregate and process data
        - https://github.com/mpatel2280/interview-prep/blob/master/system-architect.md#20-microservice-design-patterns-to-aggregate-and-process-data
    - 
- SaaS - https://github.com/mpatel2280/interview-prep/blob/master/system-architect.md#19-saas
    - Database design for a multi-tenant application


- Active Records
    - https://github.com/mpatel2280/interview-prep/blob/master/ror.md#active-record-qs
    - https://github.com/mpatel2280/interview-prep/blob/master/ror.md#active-record-associations
    - Diff Types of Associations
    - Self Join - category sub-category example
    - Single Table Inheritance
    - Diff save and save!
    - lazy loading vs eager loading
    - <b>What is scope and default_scope, how to override default_scope
    - scope vs class method
    - maintenance_tasks - To perform data migrations separately (gem 'maintenance_tasks')
    - disable migration tasks - database_tasks : false in config/application.rb or in specific environment files
    - What is optional: true in belongs_to association</b>
    - destroy Vs delete
    - joins (inner join) Vs includes (left outer join)
    - preload
    - custom query
    - union and subquery 
    - What is dynamic finders
    - belongs_to vs has_one
    - Explain <b>has_many :through</b> associations
    - diff between has_many :through has_and_belongs_to_many associations
    - Polymorphic associations - https://guides.rubyonrails.org/association_basics.html#polymorphic-associations
    - circular dependencies in associations
    - conditional associations
    - What is inverse_of, and why is it used?
    - students , assignments associations with example
- Gems Qs?
    - For Internal Authentication which gems is useful? - Devise
    - For External Authentication (like social logins, google oauth2) which gems is useful? - OmniAuth
    - RBAC - CanCanCan Vs Pundit
    - RBAC Vs ABAC
    - What are some popular gems

- Design Patterns - https://github.com/mpatel2280/interview-prep/blob/master/ror.md#design-patterns

    
- <b>Why eager_load set to false in development env?</b>

- <b>Metaprogamming</b>
- What is Callbacks
    - Relational and Conditional Callbacks
- <b>Duck Typing</b>
- Mixins and Concerns  
    - Can we execute callbacks with mixins without using ActiveSupport::Concern?
- Diff class vs instance variable
- <b>Diff extend vs include vs prepend</b>
- Thread vs Background Job
- super vs super()
- throw/catch Vs raise/rescue
- What is clousers
    - Lambda Vs Proc Vs block
- instance vs class vs global
- Scaffolding
- What is the difference between require and require_relative?
- <b>splat (*) operator</b> – used for dynamic no. of arguments
- getter and setter
- What is Rake
    - Rake Middleware    
- Ractor
- GIL
- concept of open classes in Ruby    
- extract_options! 
- <b>refinements</b>
- Object Comparison in Rails
- 

- References:
    - https://guides.rubyonrails.org
    - https://guides.rubyonrails.org/routing.html
    - https://guides.rubyonrails.org/command_line.html
    - https://edgeguides.rubyonrails.org/api_documentation_guidelines.html
    - https://edgeapi.rubyonrails.org/
    - https://api.rubyonrails.org/
    - https://rubyonrails.org/
    - https://guides.rubyonrails.org/association_basics.html ***
    - https://www.fullstack.cafe/blog/ruby-on-rails-interview-questions
    - https://flexiple.com/ruby-on-rails/interview-questions *****
    - https://www.interviewbit.com/oops-interview-questions/ ***
    - https://techgyan360.in/2024/05/ruby-interview-questions-and-answers-for-freshers-part-1/ ***
    - https://www.restapitutorial.com/ | https://www.restapitutorial.com/introduction
    - https://learn.microsoft.com/en-us/azure/architecture/best-practices/api-design 
    - https://interviewkickstart.com/blogs/interview-questions/complex-sql-interview-questions
    - https://refactoring.guru/design-patterns/php ***
    - https://refactoring.guru/design-patterns/ruby *** 
    - https://www.turing.com/interview-questions/ruby#advanced-ruby-interview-questions-and-answers
    - https://www.rubyguides.com/ruby-post-index/ *****
    - https://www.rubyguides.com/ruby-tutorial/object-oriented-programming/