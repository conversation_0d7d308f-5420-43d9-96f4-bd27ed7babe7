----------------------------------------------
### Mo<PERSON>a Software Testing Pvt Ltd
----------------------------------------------
- Q1. Can you explain how Rails' Model-View-Controller (MVC) architecture works and how data flows through an application when a user makes a request?

- Q2. What are some key principles for designing a RESTful API, and can you give an example of a good and bad URL design for a resource?

- Q3. Can you explain the difference between TDD (Test-Driven Development) and BDD (Behavior-Driven Development), and when would you use one over the other in a backend project?

- Q4. When working with a relational database like MySQL, how do you ensure efficient querying and what are some ways to optimize performance when dealing with large datasets?

- Q5. How do you resolve conflicts in Git, and what steps would you take if you and a teammate both modify the same line of code in a file?

----------------------------------------------
### Money Forward India Q2
----------------------------------------------
- Q1. Can you describe a situation where you had to design a RESTful API for a Ruby on Rails application? How did you ensure it was scalable and maintainable?

- Q2. Tell us about a time you optimized a slow-running Ruby on Rails application. What strategies did you use to improve performance?

- Q3. Describe an instance where you had to handle complex data relationships in Ruby on Rails. How did you design the models and manage database interactions?

- Q4. Have you worked with versioning in RESTful APIs? Can you provide an example where versioning was necessary, and how you implemented it in a Ruby on Rails application?

- Q5. Can you share a scenario where you encountered a difficult bug in a Ruby on Rails API? How did you approach debugging and resolving the issue?
----------------------------------------------
### Money Forward India Q1
----------------------------------------------
- Q1. What are the key differences between RESTful and GraphQL APIs? In what scenarios would you choose one over the other for a SaaS platform?

- Q2. Imagine a scenario where the backend services experience a performance bottleneck during peak traffic. How would you identify and resolve the issue? Provide a step-by-step plan for optimization.

- Q3. Write a Ruby method that takes an array of integers and returns the maximum sum of any contiguous subarray (Kadane's Algorithm). Include tests to validate your solution.

- Q4. You are tasked with migrating a legacy monolithic Ruby on Rails application to a microservices architecture. What steps would you take to ensure a smooth transition? Highlight potential challenges and how you would address them.

- Q5. As a team leader, how would you handle a situation where one of your team members is consistently missing deadlines, impacting the overall delivery? Provide an example of how you have successfully managed a similar scenario in the past.

----------------------------------------------
### Synechron
----------------------------------------------
- Q1. Write a Ruby on Rails method to handle a common REST API endpoint. Suppose you are given a GET /api/orders/:id endpoint that retrieves order details for a specific order. Implement the Rails controller method to handle this endpoint. The order should include details about items, pricing, and the user associated with the order. Consider using eager loading to optimize for performance.

- Q2. Explain how you manage state between React components in a large-scale application. How do you handle state when passing data from a parent component to deeply nested child components?

- Q3. Describe a time when you collaborated with developers to bring a design to life. How did you handle any discrepancies between the initial design and the final implementation? What tools or methods did you use to ensure accurate design implementation?

- Q4. The tech landscape is constantly evolving, with new design trends and technologies emerging frequently. How do you stay updated on emerging design trends, and can you provide an example of a recent design technology or trend that you have successfully implemented in a project?

- Q5. You are tasked with conducting a usability evaluation for a newly implemented feature. Describe the process you would follow, the types of data you would collect, and how you would use this information to iterate on the design.

----------------------------------------------
### Nous Infosystems
----------------------------------------------
- Q1. Describe your recent experience with similar projects

- Q2. In a high-traffic application using SQL Server (or similar RDBMS), what specific strategies would you use to improve database performance? Describe your approach to indexing, caching, and optimizing complex queries. How would you handle database scaling to ensure continued performance as data grows?

- Q3. Imagine you are leading the architecture for a customer-facing e-commerce application that experiences high traffic during peak sale events. Midway through a major sale, you notice the system is slowing down due to high database load and increased API response times. How would you identify and address the root cause of these performance issues? What changes would you consider in the architecture to handle such high traffic events more smoothly in the future?

- Q4. Imagine you’re working on a Ruby on Rails application that is experiencing rapid user growth, and you’re beginning to encounter performance bottlenecks, particularly with response times and database queries. How would you approach identifying and optimizing performance issues within a Rails application? What specific Rails tools, techniques, or design patterns would you apply to handle high traffic, optimize database queries, and manage caching?

- Q5. You're tasked with creating a Rails API endpoint that accepts a list of order IDs and marks each order as "processed." Each order should only be processed if it hasn’t already been marked as such. Write a Rails controller action in Ruby that: Accepts a JSON array of order_ids. For each order_id, checks if the order is already marked as "processed" in the database. If not, marks it as "processed." Ensure that your code is efficient, handling cases where there might be a large number of order_ids submitted at once.
