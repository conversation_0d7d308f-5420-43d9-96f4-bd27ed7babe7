- https://laravel.com/docs/11.x/lifecycle
- https://symfony.com/projects/laravel
- https://laravel.com/docs/11.x/structure
- https://stitcher.io/blog/new-in-php-8
- Latest Version: https://laravel.com/docs/12.x/releases
- 
- composer create-project --prefer-dist laravel/laravel project-name
- laravel new project-name
- php artisan serve

- Laravel Scaffolding
    - <PERSON><PERSON> provides scaffolding commands to quickly set up basic application features like authentication, APIs, or other pre-built functionality. These commands are often implemented using packages like Laravel Breeze, Laravel Jetstream, or Laravel UI, depending on the desired scaffolding approach.
        - Laravel Breeze: https://laravel.com/docs/10.x/starter-kits#laravel-breeze
        - Laravel Jetstream: https://jetstream.laravel.com/
        - Laravel UI: https://github.com/laravel/ui
- Laravel API only 
    - To create a Laravel project intended for API-only functionality, you can use the --api option when creating a new Laravel application. This sets up a streamlined structure tailored for API development by omitting unnecessary features like session management, cookies, and frontend scaffolding.
    - composer create-project laravel/laravel my-api-project --api
    - Features of API-Only Mode
        - Excludes Middleware: Middleware like VerifyCsrfToken and web are omitted since they are primarily used for web applications.
        - Stateless: The application is configured to handle stateless requests, making it suitable for RESTful APIs.
        - No Frontend: The project doesn't include frontend views or assets.
        - Session-Free: Sessions and cookies are disabled by default.
- Laravel code structure 
    - https://laravel.com/docs/11.x/structure
    - https://www.devopsschool.com/blog/directory-structure-of-laravel-application/
- Laravel Lifecycle - https://laravel.com/docs/11.x/lifecycle 
    - Entry Point: public/index.php
        - All requests to a Laravel application are routed through the public/index.php file.
        - This file acts as the front controller, handling every HTTP request.
        - It loads the Composer autoloader and initializes the Laravel framework.
    - HTTP Kernel (App\Http\Kernel)
        - The Kernel is responsible for bootstrapping the application and handling incoming requests.
        - It loads: Global Middleware: Middleware applied to all requests (e.g., handling maintenance mode, verifying CSRF tokens).
        - It loads: Route Middleware Groups: Middleware specific to web or API routes.
    - Service Providers
        - The application's configured service providers are bootstrapped. These are defined in config/app.php under the providers array.
        - Which, Bind services to the Service Container.
        - Register event listeners.
        - Perform application setup logic.
    - Request Handling
        - The HTTP Kernel processes the request:
            - Middleware Pipeline: The request passes through all middleware layers in the stack.
            - Routing: The Router matches the incoming request to a route in the routes/web.php or routes/api.php file.
    - Route Execution
        - Once a route is matched:
            - The Controller Method or Closure associated with the route is executed.
            - If Route Model Binding is used, Laravel automatically resolves the models defined in the route.
    - Controller/Action Logic
        - The request is passed to the corresponding controller or route handler where the main application logic executes.
        - You can interact with models, services, or perform other application-specific tasks here.
    - View Rendering
        - If the controller/action returns a view, the Blade Templating Engine is used to compile and render the HTML.
        - The compiled view is sent back as part of the HTTP response.
    - Response Sent
        - The generated response is wrapped in an HTTP response object and sent back to the client.
        - Middleware that runs after the response can modify the response (e.g., add headers, log requests).
    -  Event and Logging
        - During the lifecycle, Laravel triggers several events (e.g., RequestHandled).
        - Logs are written using the Monolog library as configured in config/logging.php.
    - Flow
        - Client Request -> index.php -> HTTP Kernel -> Middleware -> Routing -> Controller -> View/Response -> Client
    - Key Components in the Lifecycle
        - Middleware: Filters requests globally or for specific routes.
            - Examples: Authenticate, ThrottleRequests, EncryptCookies.
        - Service Providers: Bootstraps essential services (e.g., database, mail, events).
        - Routing: Determines which controller or closure handles the request.
        - Service Container: Manages dependency injection for controllers and other components.

- Artisan in Laravel
    - In Laravel, an artisan is a command-line interface (CLI) tool that comes bundled with the framework. It provides a wide range of helpful commands to assist in building and managing Laravel applications.
    - Some common Artisan commands include:
        - php artisan make:model: Creates a new Eloquent model class.
        - php artisan make:controller: Generates a new controller class.
        - php artisan make:controller UserController --resource: Generates a controller with CRUD methods for a resourceful route.
        - php artisan make:controller BookController --api
        - php artisan make:migration create_table_name_table - to create a new migration
        - php artisan migrate: Runs any outstanding database migrations.
        - php artisan db:seed: Seeds the database with test data.
        - php artisan tinker: Opens an interactive REPL (Read-Eval-Print Loop) for interacting with the application's models and database.
        - php artisan route:list: Lists all registered routes in the application.
        - php artisan serve: Starts a development server for the Laravel application.
        - php artisan down: Puts the application into maintenance mode.
        - php artisan up: Takes the application out of maintenance mode.
        - php artisan help: Displays a list of available Artisan commands and their descriptions.
        - php artisan optimize: Optimizes the application's performance by combining and minifying assets.
        - php artisan env: Displays the current application environment.
        - php artisan key:generate: Generates a new application encryption key.
        - php artisan make:command YourCommand - to schedule a custom command
        - php artisan schedule:run - run scheduled tasks manually
        - php artisan serve --host=************ --port=8080
        - <i>Laravel does not have built-in create view command. </i>, manually create view file under resources/views/filename.blade.php
- Eloquent in Laravel
    - In Laravel, Eloquent is the default Object-Relational Mapping (ORM) implementation that comes bundled with the framework. It provides an intuitive ActiveRecord implementation for working with your database. Eloquent allows you to interact with your database tables using PHP objects and provides a fluent, expressive syntax for querying and manipulating data.
    - Key features of Eloquent include:
        - Model-View-Controller (MVC) Integration: Eloquent seamlessly integrates with Laravel's MVC architecture, allowing you to define models that represent database tables and interact with them from controllers and views.
        - Database Relationships: Eloquent makes it easy to define relationships between your models, such as one-to-one, one-to-many, and many-to-many relationships. These relationships can be defined using simple methods within your model classes.
        - CRUD Operations: Eloquent provides methods for performing CRUD (Create, Read, Update, Delete) operations on your database records. These operations can be performed using expressive syntax, making it easy to work with your data.
        - Query Builder: Eloquent includes a query builder that allows you to construct complex database queries using a fluent, chainable syntax. This makes it easy to retrieve and manipulate data from your database.
        - Model Observers and Events: Eloquent allows you to define observers and events on your model classes, which can be triggered when certain actions occur, such as creating, updating, or deleting records. This provides a convenient way to perform additional logic or side effects in response to database changes.
- Active Record Patterns Vs Data Mapper Pattern
    - ActiveRecord Pattern: In the ActiveRecord pattern, an object (the "ActiveRecord") represents a row in a database table, and typically encapsulates the database access and manipulation logic within itself. This means that the object is both responsible for representing the data and for handling the database operations (such as querying, updating, deleting). Eloquent in Laravel follows this pattern, where each Eloquent model represents a database table, and instances of the model are used to interact with the corresponding rows in that table.
    - Data Mapper Pattern: In contrast, the Data Mapper pattern separates the domain object (i.e., the object representing data in the application) from the persistence mechanism (i.e., the database). In this pattern, there's typically a separate mapper or repository responsible for mapping database records to domain objects and vice versa. Laravel's Eloquent does not strictly adhere to the Data Mapper pattern, as it combines both data representation and persistence logic within the model itself.
- Routes in Laravel
    - Web Routes and API Routes:
        - Web routes are used to handle HTTP requests that are typically associated with web applications. These routes are defined in the routes/web.php file by default.
        - API routes, on the other hand, are used to define routes specifically for APIs. These routes are typically stateless and are often used to interact with the application programmatically. API routes are defined in the routes/api.php file by default.
    - Named routes allow you to give a unique name to a route, making it easier to reference in your application. You can generate URLs or redirects to named routes instead of hardcoding the route URLs in your application, which makes your code more maintainable.
        - Route::get('/profile', 'ProfileController@show')->name('profile');
    - Route groups allow you to group several routes under a common prefix or namespace, as well as apply middleware or other attributes to multiple routes at once.
        - 
        ```php
        Route::prefix('admin')->middleware('auth')->group(function () {
            Route::get('/dashboard', 'AdminController@dashboard');
            Route::get('/users', 'AdminController@listUsers');
        });
        ```
    - In summary, routing in Laravel allows you to define how incoming HTTP requests should be handled by your application. You can define routes for both web and API requests, use named routes for easy referencing, and group routes together to apply common attributes. This provides a flexible and organized way to manage your application's routing logic.
-------------------------------------------------------------------------------
-  Laravel Eloquent

    - What is Eloquent 
        - Eloquent is Laravel's built-in ORM (Object-Relational Mapping) that provides an elegant, active record implementation for working with databases. Each database table has a corresponding Eloquent model, and these models allow you to interact with the database using methods and properties instead of raw SQL queries.
    - How do you define a one-to-one relationship in Eloquent? 
        - A one-to-one relationship is defined using the hasOne and belongsTo methods in Eloquent models.
        - 
        ```php
        // User model
        public function profile()
        {
            return $this->hasOne(Profile::class);
        }

        // Profile model
        public function user()
        {
            return $this->belongsTo(User::class);
        }
        ```
    - What is the difference between hasMany and belongsTo?
        - hasMany: Defines a one-to-many relationship where a model has multiple related models. Example: A user has many posts.
        - belongsTo: Defines an inverse relationship where a model belongs to another model. Example: A post belongs to a user.
    - How do you implement a polymorphic relationship in Eloquent?
        - A polymorphic relationship allows a model to belong to more than one type of model.
        - Example: A Comment model can belong to both Post and Video.
        - 
        ```php
        // Comment model
        public function commentable()
        {
            return $this->morphTo();
        }

        // Post model
        public function comments()
        {
            return $this->morphMany(Comment::class, 'commentable');
        }

        // Video model
        public function comments()
        {
            return $this->morphMany(Comment::class, 'commentable');
        }
        ```
    - What is the difference between first() and find() in Eloquent?
        - first(): Fetches the first record from the query result.
        - find($id): Fetches a record by its primary key.
    - How can you perform soft deletes in Laravel?
        - Soft deletes allow you to "delete" records without actually removing them from the database. Add the SoftDeletes trait to your model. 
        - Add a deleted_at column to your table using a migration. 
        - To retrieve only non-deleted records, use the withTrashed() or onlyTrashed() methods if needed. 
    - How do you use query scopes in Eloquent?
        - https://laravel.com/docs/11.x/eloquent#query-scopes
        - Query scopes allow you to encapsulate commonly used query constraints within your model
        - 
        ```php
        // Model
        public function scopeActive($query)
        {
            return $query->where('status', 'active');
        }

        // Usage
        $activeUsers = User::active()->get();
        ```
    - How can you eager load relationships in Eloquent?
        - Eager loading is used to load related models along with the main model to avoid N+1 query issues
        - $users = User::with('posts')->get();
    - What is the difference between has() and with() in Eloquent?
        - has(): Filters results based on the existence of a relationship. Example: User::has('posts')->get();
        - with(): Eager loads relationships without filtering. Example: User::with('posts')->get();
        - 
        ![alt text](<images/eloquent-has-vs-with.png>)
    - How do you define many-to-many relationships in Eloquent?
        - Many-to-many relationships are defined using the belongsToMany method.
    - How do you use raw expressions in Eloquent?
        - You can use DB::raw() for raw SQL expressions
        - 
        ```php
        $users = User::select('name', DB::raw('COUNT(posts.id) as post_count'))
             ->join('posts', 'users.id', '=', 'posts.user_id')
             ->groupBy('users.id')
             ->get();
        ```


    - Types of Associations In Laravel Eloquent
        - https://laravel.com/docs/11.x/eloquent-relationships
        - One-to-One Relationship: In a one-to-one relationship, each record in one database table is associated with exactly one record in another table. This is typically established using the hasOne and belongsTo methods.
        - One-to-Many Relationship: In a one-to-many relationship, a single record in one database table is associated with multiple records in another table. This is established using the hasMany method on the parent model and the belongsTo method on the child model.
        - Many-to-One Relationship: This is essentially the inverse of a one-to-many relationship, where multiple records in one table are associated with a single record in another table. This is established using the belongsTo method on the child model and the hasMany method on the parent model.
        - Many-to-Many Relationship: In a many-to-many relationship, multiple records in one table are associated with multiple records in another table. This is established using the belongsToMany method on both models.
        - Has-One-Through Relationship: This relationship allows you to define a one-to-one relationship through another model. It's useful when you need to access a related model through an intermediate model.
        - Has-Many-Through Relationship: Similar to the has-one-through relationship, but allows you to define a one-to-many relationship through an intermediate model.
-------------------------------------------------------------------------------    
- Laravel Service Providers

    - In Laravel, service providers are a fundamental aspect of the framework's architecture. They play a crucial role in bootstrapping various components and services of the application. Service providers are responsible for binding services into the Laravel service container, registering event listeners, defining middleware, and performing other bootstrapping tasks.
    - Service providers are located in the app/Providers directory.
    - Service providers are registered in the config/app.php configuration file under the providers array. When your Laravel application boots up, Laravel iterates through the list of registered service providers and calls their register() method. After all service providers have been registered, Laravel calls the boot() method on each service provider.
    - Service providers extend the Illuminate\Support\ServiceProvider class, The most important methods you'll typically use are register() and boot().
    - The register() method is where you can register bindings in the service container. This method is called when the service provider is registered with the application.
    - The boot() method is where you can perform any additional setup tasks after all service providers have been registered. This method is called after all service providers have been registered, and the application has been fully bootstrapped.
    - Here's what you need to know about service providers in Laravel:
        - Registration of Services: Service providers are responsible for registering services (such as classes, components, or dependencies) into the Laravel service container. This allows these services to be easily accessed throughout the application via dependency injection or the service container.

        - Bootstrapping: Service providers can perform bootstrapping tasks during the application's initialization process. This includes tasks such as registering routes, publishing configuration files, registering middleware, and registering event listeners.

        - Deferred Loading: Laravel supports deferred loading of service providers, which can help improve the performance of your application by deferring the registration of certain services until they are actually needed. This is particularly useful for service providers that provide services only in certain situations or environments.

        - Configuration and Binding: Service providers often define bindings and configurations for the services they register. This allows developers to easily configure and customize the behavior of these services by modifying configuration files or binding alternative implementations.

        - Package Integration: Service providers are commonly used when integrating third-party packages or libraries into Laravel applications. Package developers can define their own service providers to register and configure the services provided by their packages within the Laravel application.

        - Application Events: Service providers can also listen for application events and register event listeners accordingly. This allows developers to respond to various events that occur during the application's lifecycle, such as when the application is booted or when a specific event is dispatched.

    - Overall, service providers are a key aspect of Laravel's architecture, providing a centralized mechanism for bootstrapping and registering services within the application. They help promote modularity, extensibility, and maintainability by encapsulating the initialization logic for various components of the application.

    - What is a service provider in Laravel?
        - A service provider in Laravel is the central place where the framework bootstraps and binds services into the service container. Service providers are responsible for binding classes, configuring application settings, or loading additional files and libraries
    - How do you create a custom service provider in Laravel?
        - php artisan make:provider CustomServiceProvider
        - This will generate a service provider class in the app/Providers directory. You need to register this provider in the config/app.php file under the providers array.
    - What are the two main methods in a service provider?
        - register: Used to bind services or classes into the service container. Only bindings should go here, no logic that depends on the application being fully bootstrapped.
        - boot: Executes after all service providers have been registered. This is where you configure services, load routes, or set up event listeners.
    - How do you register a service provider in Laravel?
        - You register a service provider by adding its fully qualified class name to the providers array in the config/app.php file
        - 
        ```php
        'providers' => [
            // Other Service Providers

            App\Providers\CustomServiceProvider::class,
        ],
        ```
    - Can you give an example of binding an interface to a class in a service provider?
        - You can bind an interface to a concrete implementation in the register method
        - 
        ```php
        public function register()
        {
            $this->app->bind(App\Contracts\MyInterface::class, App\Services\MyService::class);
        }
        ```
    - What is deferred loading in Laravel service providers?
        - Deferred loading allows a service provider to load only when the services it provides are actually needed. To make a service provider deferred, set the $defer property to true and specify which bindings it provides using the provides method.
        - <i>Note: As of Laravel 5.5, deferred providers are no longer necessary due to the service container's optimized lazy loading.</i>
        - 
        ```php
        protected $defer = true;

        public function provides()
        {
            return [MyService::class];
        }
        ```
    - What is the purpose of the AppServiceProvider in Laravel?
        - The AppServiceProvider is one of the default service providers provided by Laravel. It is used to configure or register services specific to the application. Developers often use it for tasks like global query scopes, application-wide bindings, or registering other services.
    - How can you optimize service providers for better performance?
        - Use lazy loading: Only bind services when required.
        - Optimize the classmap: Use the composer dump-autoload -o command to optimize the autoloader.
        - Load only necessary providers: Register only those service providers that are required in the application.
    - Can you disable a service provider in Laravel?
        - Yes, you can disable a service provider by removing it from the providers array in config/app.php.
    - What is service provider auto-discovery?
        - Laravel automatically discovers service providers for packages that support auto-discovery. This is done via the extra section in the package's composer.json file.
        - 
        ```php
        "extra": {
            "laravel": {
                "providers": [
                    "Vendor\\Package\\ServiceProvider"
                ]
            }
        }
        ```

-------------------------------------------------------------------------------

- Service Container
    - The Laravel service container, also known as the IoC (Inversion of Control) container, is a powerful tool for managing class dependencies and performing dependency injection within your Laravel application. It serves as a central registry for storing and resolving instances of classes and their dependencies, allowing you to decouple components and promote modularity, testability, and flexibility in your application.
    - Here's an overview of how the service container works in Laravel:
        - Registration:
            - You can register bindings in the service container using the bind() method or its variants (singleton(), instance(), etc.).
            - Bindings define how instances of a class should be created and resolved by the service container.
            - Bindings can be registered as singletons (i.e., the same instance is returned on subsequent calls) or as transient instances (i.e., a new instance is created on each call).
        - Resolution:
            - Once a class has been bound to the service container, you can resolve instances of that class from the container using the make() method.
            - The service container automatically resolves class dependencies by inspecting the type hints of a class's constructor or method parameters.
            - It recursively resolves dependencies as needed, allowing you to easily inject dependencies into your classes without having to manually instantiate them.
        - Contextual Binding:
            - Laravel's service container supports contextual binding, allowing you to bind different implementations of a class based on the context in which the class is resolved.
            - This is useful for injecting different implementations of an interface depending on the current request or application state.
        - Service Providers:
            - Service providers in Laravel provide a convenient way to register bindings and configure the service container.
            - They allow you to encapsulate the setup logic for your application's services and dependencies, keeping your application organized and modular.
        - Container Aliases:
            - Laravel's service container allows you to define aliases for class bindings using the alias() method.
            - Aliases provide a convenient way to reference bindings by a shorter, more descriptive name.
        - Automatic Injection:
            - Laravel's service container can automatically inject dependencies into controllers, middleware, route closures, and other classes.
            - This allows you to leverage dependency injection throughout your application without having to manually resolve dependencies from the service container.
        - How to Use the Service Container:
            - Binding:
            ```php
            // Binding an interface to a concrete implementation
            $this->app->bind(MyInterface::class, MyImplementation::class);

            // Binding a class as a singleton
            $this->app->singleton(MyClass::class, function ($app) {
                return new MyClass($app->make('SomeDependency'));
            });
            ```
            - Resolution:
            ```php
            // Resolving a class instance from the container
            $instance = app(MyClass::class);
            ```
            - Dependency Injection:
            ```php
            // Constructor injection
            class MyClass {
                protected $dependency;

                public function __construct(MyDependency $dependency) {
                    $this->dependency = $dependency;
                }
            }

            // Method injection
            class AnotherClass {
                public function doSomething(MyDependency $dependency) {
                    // Method logic
                }
            }
            ```
            - Contextual Binding:
            ```php
            $this->app->when(AnotherClass::class)
                ->needs(MyInterface::class)
                ->give(AnotherImplementation::class);

            ```
            - The Service Container in Laravel plays a central role in facilitating loose coupling, promoting reusability, and enabling efficient dependency management in your application. It's a fundamental aspect of Laravel's robust and elegant architecture, making it easier to build and maintain complex applications with clean and maintainable code.

    - The service container is responsible for managing three main types of objects:
        - Bindings: Bindings are mappings between abstract contracts (e.g., interfaces or abstract classes) and concrete implementations. They define how instances of classes should be created and resolved by the service container.
        - Instances: Instances are concrete objects that have been resolved from the service container. Once a class has been bound to the service container, you can resolve instances of that class from the container using the make() method.
        - Dependencies: Dependencies are objects that a class depends on to perform its functionality. When a class is resolved from the service container, the container automatically resolves and injects any dependencies that the class requires, allowing you to perform dependency injection without having to manage dependencies manually.
    - What is a service container in Laravel?
        - The service container is a dependency injection system and a central place where Laravel manages class bindings and their dependencies. It resolves class instances, services, and dependencies automatically, promoting flexibility and clean code.
    - What is the role of a service container in service providers?
        - A service provider interacts with the Laravel service container to bind, resolve, and manage class dependencies. The service container is at the core of dependency injection in Laravel.
    - How does dependency injection work in Laravel's service container?
        - Laravel uses the service container to resolve dependencies. When a class is requested, Laravel resolves and injects its dependencies automatically.
        - public function __construct(ReportService $reportService)
        - Here, the ReportService instance is automatically injected into the controller by the service container.
    - How do you bind a class or interface in the service container?
        - Bindings can be defined in a service provider's register method or directly in the container.
        - $this->app->bind('App\Contracts\ServiceInterface', 'App\Services\ConcreteService');
    - What is the difference between bind and singleton?
        - bind: Creates a new instance every time the service is resolved.
            - $this->app->bind(ServiceInterface::class, ConcreteService::class);
        - singleton: Resolves the service as a single instance (shared across the application).
            - $this->app->singleton(ServiceInterface::class, ConcreteService::class);
    - What is the difference between make() and resolve() methods?
        - make(): A simpler way to resolve a class from the service container
            - $instance = app()->make(ServiceInterface::class);
        - resolve(): An alias of make(), with similar functionality. It's useful when manually resolving dependencies.
    - How can you use contextual binding in Laravel?
        - Contextual binding allows different implementations of a service to be injected based on the context.
        - 
        ```php
        $this->app->when(ReportController::class)
            ->needs(ServiceInterface::class)
            ->give(ReportService::class);
        ```
    - What are service container aliases in Laravel?
        - Aliases allow you to resolve services using short or user-friendly names
        - $this->app->alias('App\Services\ConcreteService', 'ServiceAlias');
    - Can you explain tag-based binding in the service container?
        - Tagging allows you to group multiple bindings under a tag, which can be resolved together.
        - 
        ```php
        $this->app->bind(ReportService::class);
        $this->app->bind(LogService::class);

        $this->app->tag([ReportService::class, LogService::class], 'services');

        $services = $this->app->tagged('services');
        ```
    - What is the difference between extend() and bind()?
        - bind(): Binds a new class or service to the container.
        - extend(): Modifies or decorates an existing binding.
        - 
        ```php
        $this->app->extend('App\Services\ServiceInterface', function ($service, $app) {
            // Modify or decorate the service
            return $service;
        });
        ```
    - What is auto-wiring in Laravel?
        - Auto-wiring refers to the service container's ability to automatically resolve dependencies by type-hinting them in class constructors or methods. The container identifies and resolves these dependencies without additional configuration.
    - How does the ServiceProvider relate to the service container?
        - Service providers are used to register bindings in the service container. They serve as the central place for configuring and registering application services.
    - Can you provide a real-world example of a service container in Laravel?
        - Consider an email service where different email providers (like Mailgun or SendGrid) are used:

-------------------------------------------------------------------------------

- Dependency Injection
    - Dependency injection in Laravel is a powerful feature that allows you to easily manage class dependencies and promote modular, testable, and reusable code. Laravel's service container, which serves as a central registry for managing class instances and their dependencies, facilitates dependency injection throughout the framework.
    - Here's how you can perform dependency injection in Laravel:
        - Constructor Injection:
            - Constructor injection involves injecting dependencies through a class's constructor.
            - Define the dependencies in the constructor of the class that requires them.
            - Laravel's service container automatically resolves and injects the dependencies when an instance of the class is requested.
            - 
            ```php
            namespace App\Http\Controllers;

            use App\Services\UserService;

            class UserController extends Controller
            {
                protected $userService;

                public function __construct(UserService $userService)
                {
                    $this->userService = $userService;
                }

                public function index()
                {
                    // Use the injected UserService instance
                    $users = $this->userService->getAllUsers();

                    // ...
                }
            }
            ```
        - Method Injection:
            - Method injection involves injecting dependencies directly into method parameters.
            - Define the method's parameters with type-hinted dependencies.
            - Laravel's service container automatically resolves and injects the dependencies when the method is called.
            - 
            ```php
            namespace App\Http\Controllers;

            use App\Services\UserService;

            class UserController extends Controller
            {
                public function index(UserService $userService)
                {
                    // Use the injected UserService instance
                    $users = $userService->getAllUsers();

                    // ...
                }
            }
            ```
        - Automatic Injection in Controllers:
            - Laravel provides automatic injection of dependencies into controller methods.
            - Type-hint dependencies in controller method parameters, and Laravel's service container will automatically resolve and inject them.
            - 
            ```php
            namespace App\Http\Controllers;

            use App\Services\UserService;

            class UserController extends Controller
            {
                public function index(UserService $userService)
                {
                    // Use the injected UserService instance
                    $users = $userService->getAllUsers();

                    // ...
                }
            }
            ```
        - Dependency Injection in Service Providers
            - Laravel service providers allow you to bind interfaces to their implementations in the service container, enabling dependency injection throughout your application.
            - Define service bindings in the register() method of your service provider classes.
            - 
            ```php
            namespace App\Providers;

            use App\Repositories\UserRepository;
            use App\Services\UserService;
            use Illuminate\Support\ServiceProvider;

            class AppServiceProvider extends ServiceProvider
            {
                public function register()
                {
                    $this->app->bind(UserService::class, function ($app) {
                        return new UserService($app->make(UserRepository::class));
                    });
                }
            }
            ```

- Middleware
    - Middleware in Laravel is a mechanism for filtering HTTP requests entering the application. It provides a convenient way to add custom logic to the request-response cycle, allowing developers to perform tasks such as authentication, authorization, logging, and request preprocessing. Middleware sits between the client's request and the application's route handling, intercepting the request and potentially modifying its behavior before passing it along to the next middleware or route handler.
    - Middleware in Laravel is defined as PHP classes that implement the handle() method, which receives an incoming request and returns either a response or passes the request to the next middleware in the chain. Middleware can be assigned globally to all routes, grouped routes, or individual routes.
    - Here's how middleware is used in Laravel:
        - Global Middleware: Global middleware is applied to all HTTP requests entering the application. These middleware are registered in the $middleware property of the App\Http\Kernel class.
        - 
        ```php
        namespace App\Http;

        use Illuminate\Foundation\Http\Kernel as HttpKernel;

        class Kernel extends HttpKernel
        {
            /**
            * The application's global HTTP middleware stack.
            *
            * These middleware are run during every request to your application.
            *
            * @var array
            */
            protected $middleware = [
                \App\Http\Middleware\TrustProxies::class,
                \App\Http\Middleware\CheckForMaintenanceMode::class,
                \Illuminate\Foundation\Http\Middleware\ValidatePostSize::class,
                \App\Http\Middleware\TrimStrings::class,
                \Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull::class,
                \App\Http\Middleware\VerifyCsrfToken::class,
                \Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode::class,
            ];
        }
        ```
        - Route Middleware: Route middleware is applied to specific routes or groups of routes. These middleware are registered in the $routeMiddleware property of the App\Http\Kernel class and can be assigned to routes using the middleware() method in route definitions.
        - 
        ```php
        // app/Http/Kernel.php

        protected $routeMiddleware = [
            'auth' => \App\Http\Middleware\Authenticate::class,
            'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
            'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
            // Other route-specific middleware...
        ];
        ```
        - Group Middleware: Middleware can be grouped together and applied to a group of routes using the middleware() method in route definitions.
            - Middleware groups are typically defined in the App\Http\Kernel class within the $middlewareGroups property. Each middleware group is an array containing one or more middleware classes.
            - Once a middleware group is defined, you can apply it to routes by using the middleware() method in your route definitions.
            - Here's an example of applying the web middleware group to a group of routes in a route file:
            - 
            ```php
            Route::middleware('web')->group(function () {
                // Routes that are part of the 'web' middleware group
                Route::get('/', [HomeController::class, 'index']);
                Route::get('/profile', [UserController::class, 'profile']);
            });

            Route::middleware(['web', 'api'])->group(function () {
                // Routes that apply both 'web' and 'api' middleware groups
            });
            ```
        - Custom Middleware Groups:
            - define custom middleware groups in the App\Http\Kernel class and apply them to routes in the same way as built-in middleware groups.
            - define a custom middleware group named api for API routes
            
            - 
            ```php
            protected $middlewareGroups = [
                'api' => [
                    \App\Http\Middleware\ThrottleRequests::class,
                    \Illuminate\Routing\Middleware\SubstituteBindings::class,
                ],
            ];

            // - Then, you can apply the api middleware group to your API routes:
            Route::prefix('api')->middleware('api')->group(function () {
                // API routes
            });
            ```
        - In Laravel, when applying middleware to a group of routes using group middleware, the middleware listed first in the $middlewareGroups array within the App\Http\Kernel class gets executed first
            
        - Controller Middleware: Middleware can also be applied directly to controller methods using the middleware() method in controller constructors or method definitions.
        - Priority Middleware: 
            - Laravel allows you to specify the priority or order in which middleware are executed by defining their position in the $middlewarePriority property of the App\Http\Kernel class.
            - Priority middleware is useful for controlling the order in which middleware are executed, ensuring that certain middleware are applied before or after others.
        - Terminable Middleware:
            - Terminable middleware is executed after the response has been sent to the client, allowing you to perform additional tasks after the request-response cycle has been completed.
            - Terminable middleware implements the TerminableMiddleware interface and defines a terminate method that is called automatically by Laravel's HTTP kernel.
            - Terminable middleware is useful for tasks that need to be performed after the response has been sent, such as cleaning up resources, sending analytics data, or logging response information.
    - Examples of built-in middleware in Laravel include:
        - Authenticate Middleware: The auth middleware verifies that the user making the request is authenticated. It redirects unauthenticated users to the login page.
        - Authorize: This middleware is used to authorize users to perform certain actions. It checks if the authenticated user has the necessary permissions to access a resource or perform a specific action.
        - RedirectIfAuthenticated Middleware: The guest middleware redirects authenticated users away from routes that should only be accessible to guests (unauthenticated users), such as the login and registration pages.
        - ThrottleRequests Middleware: The throttle middleware limits the number of requests that a client can make to certain routes within a specified time interval, helping to prevent abuse and spam.
        - 
        ```php
        Route::middleware('throttle:rate_limit,1')->get('/example', function () {
            return 'You can only make one request per minute.';
        });
        ```
        - VerifyCsrfToken Middleware: The csrf middleware verifies that the CSRF token attached to the request matches the token stored in the session, protecting against cross-site request forgery attacks.
        - EncryptCookies Middleware: The encrypt middleware encrypts cookies attached to the response, providing an extra layer of security for sensitive data stored in cookies.
        - AddQueuedCookiesToResponse: This middleware is used to add any cookies that were queued for insertion to the response. It appends the queued cookies to the response headers before sending the response to the client's browser.
        - StartSession: This middleware is used to start the session for the current request. It initializes the session data and makes it available to the rest of your application.
        - ShareErrorsFromSession: This middleware is used to share session errors with views. It retrieves any errors stored in the session flash data and makes them available to views so that they can be displayed to the user.

    - Creating Middleware
        - php artisan make:middleware CheckAge
    - Defining Middleware Logic
    - 
    ```php
    <?php

    namespace App\Http\Middleware;

    use Closure;

    class CheckAge
    {
        public function handle($request, Closure $next)
        {
            if ($request->age < 18) {
                return redirect('home');
            }

            return $next($request);
        }
    }

    ```
    - Registering Middleware: Middleware can be registered in the App\Http\Kernel class, which contains $middleware and $routeMiddleware properties. Global middleware is added to the $middleware property, while route middleware is added to the $routeMiddleware property.
    - Applying Middleware to Routes: Middleware can be applied to routes or route groups using the middleware method in route definitions
    - Applying Middleware to Controller Methods: Middleware can also be applied directly to controller methods using the middleware method in controller constructors or method definitions    
    - Common Usecase:
        - Authentication: Middleware can be used to ensure that incoming requests are authenticated before they are processed by your application. Laravel includes built-in middleware for authentication (auth), which can be applied to routes or route groups to restrict access to authenticated users only.
        - Authorization: Middleware can be used to enforce access control rules and permissions on specific routes or resources. You can create custom authorization middleware to check if the current user has the necessary permissions to perform certain actions.
        - Logging: Middleware can be used to log information about incoming requests, such as request headers, IP addresses, user agents, and more. This can be useful for debugging purposes or for tracking and monitoring requests in production environments.
        - Request Preprocessing: Middleware can preprocess incoming requests by modifying request parameters, headers, or payloads before they are passed on to your application's routes or controllers. For example, you can use middleware to sanitize input data, normalize request parameters, or enforce request validation rules.
        - Response Manipulation: Middleware can intercept and modify responses generated by your application before they are sent back to the client. This can be useful for adding custom headers, modifying response content, or handling error responses in a consistent manner.
        - Session Management: Middleware can be used to handle session management tasks, such as starting and terminating sessions, persisting session data, or handling session expiration and regeneration.
        - Rate Limiting: Middleware can be used to enforce rate limiting policies on incoming requests, such as limiting the number of requests a client can make within a certain time period. This can help prevent abuse or excessive usage of your application's resources.
- Laravel implicit controller Vs explicit controller
    - Implicit Controllers:
        - Implicit controllers allow you to define resourceful routes without explicitly specifying each route individually. Laravel automatically generates route definitions for CRUD operations (create, read, update, delete) based on conventions.
        - For example, to define routes for a resource named Post, you can use the Route::resource() method:
        - 
        ```php
        Route::resource('posts', 'PostController');
        # This single line covered CRUD operations. E.g GET /posts, POST /posts (store), PUT/PATCH /posts/{id} (update) etc.
        ```
        - Suitable for CRUD operations where routes follow RESTful conventions and don't require customization. Provides a quick and convenient way to define resourceful routes.
    - Explicit Controllers:
        - Explicit controllers involve manually defining each route and mapping it to a specific controller method. This approach offers more control and flexibility, especially when you want to define custom routes or handle non-CRUD operations.
        - For example, to define routes for a specific controller method in an explicit manner, you would use the Route::get(), Route::post(), Route::put(), Route::patch(), and Route::delete() methods explicitly:
        - 
        ```php
        Route::get('posts', 'PostController@index');
        Route::get('posts/{id}', 'PostController@show');
        Route::post('posts', 'PostController@store');
        Route::put('posts/{id}', 'PostController@update');
        Route::delete('posts/{id}', 'PostController@destroy');
        ```
        - Offers more control and flexibility, allowing you to define custom routes and handle non-standard operations. Suitable for scenarios where routes need to be customized or don't follow RESTful conventions.

- Session Management
    - In Laravel, session management is handled transparently through the use of the session helper and the underlying session driver configured in your application. Laravel supports various session drivers out of the box, including file, cookie, database, memcached, redis, and array.
    - Session configuration settings can be found in the config/session.php file. Here you can specify the session driver, lifetime, encryption, and other options.
    - The default session driver is file, which stores session data in files on the server. Other drivers include cookie, database, memcached, redis, and array.
    - You can access the session data using the session helper or the Session facade.
    - To store data in the session, you can use the put method: session()->put('key', 'value').
    - To retrieve data from the session, you can use the get method: session()->get('key').
    - You can flash data to the session using the flash method: session()->flash('key', 'value').
    - You can retrieve all session data as an array using the all method: session()->all().
    - You can remove specific session data using the forget method: session()->forget('key').
    - You can also flush all session data using the flush method: session()->flush().
    - You can manually regenerate the session ID using the regenerate method: session()->regenerate().
- Deferred (Lazy) Loading Vs Eager Loading Vs Lazy Eager Loading
    - In Laravel, deferred loading refers to delaying the loading of related models until they are actually accessed, which is useful for optimizing performance. This is primarily achieved through lazy loading.
    - This approach is useful when you don't always need related data and want to optimize database performance by loading it only when necessary.
    - 
    ```php
    class User extends Model
    {
        public function posts()
        {
            return $this->hasMany(Post::class);
        }
    }
    // Retrieve a user
    $user = User::find(1);

    // At this point, only the user data has been loaded

    // Access posts
    $posts = $user->posts;

    // Now, a query is made to fetch the user's posts
    ```
    - Eager Loading - If you know you'll need the related data immediately, you can use eager loading to avoid multiple queries.
    - 
    ```php
    // Eager loading posts with the user
        $user = User::with('posts')->find(1);

        // Both the user and posts are loaded in one query
    ```
    - With eager loading, Laravel fetches all necessary data in a single query, avoiding additional queries when relationships are accessed. Basically thas avoids N+1 query.
    - Lazy Eager Loading - Lazy eager loading loads relationships after the initial query, but only when needed.
    - 
    ```php
    // Retrieve multiple users
    $users = User::all();

    // Lazy eager load posts for all users
    $users->load('posts');
    ```
    - This retrieves all users first and then retrieves all posts in a second query, which is more efficient when working with collections.
    - When to Use Deferred Loading
        - Use lazy loading when you are unsure if the relationship data will be used.
        - Use eager loading if you know you need the relationship data to avoid N+1 query issues.
        - Use lazy eager loading when you need relationship data but only for a subset of the collection.
- Popular Laravel Packages
    - Laravel Nova: A beautifully designed administration panel for Laravel applications, allowing developers to build custom admin interfaces quickly and easily.
    - Laravel Passport: A full OAuth2 server implementation for Laravel, providing a simple way to authenticate users via API tokens or OAuth2 flows.
    - Laravel Sanctum: A lightweight package for API authentication using Laravel's built-in session authentication or token-based authentication via API tokens.
    - Laravel Horizon: A beautiful dashboard and queue monitoring tool for Laravel applications, allowing developers to monitor and manage queues, jobs, and workers with ease.
    - Laravel Scout: A powerful package for adding full-text search capabilities to Laravel applications using popular search engines like Algolia or Elasticsearch.
    - Laravel Dusk: A browser automation and testing tool for Laravel applications, allowing developers to write and run browser tests using a fluent and expressive API.
    - Laravel Cashier: Cashier  is a package provided by Laravel to simplify subscription billing for SaaS applications using Stripe or Paddle. It abstracts away much of the boilerplate code required to handle subscription billing, such as managing payment methods, creating subscriptions, handling cancellations, and even offering prorated billing.
    - Laravel Mix: A simple and powerful asset compilation tool for Laravel applications, allowing developers to compile and bundle CSS, JavaScript, and other assets using webpack.
    - Laravel Telescope: A developer tool for debugging and monitoring Laravel applications in real-time, providing insights into database queries, exceptions, and more.
    - Laravel Echo: A JavaScript library for subscribing to and listening for events broadcast by Laravel applications using websockets or broadcasting drivers like Pusher.
    - Laravel Valet: A development environment for macOS that provides a lightweight and fast way to serve Laravel applications locally using Nginx.
    - Laravel Spark is a premium Laravel package that includes built-in support for multi-tenancy among its features.
    - Laravel Rollbar: Rollbar is a popular error monitoring and tracking service that helps developers identify, debug, and fix errors in their applications. Integrating Rollbar with Laravel allows you to automatically capture and track errors, exceptions, and logs in your Laravel applications, enabling you to proactively monitor and improve application stability.
    - Laravel Octane: Laravel Octane is an optional package introduced in Laravel to significantly boost application performance by leveraging high-performance application servers like Swoole and RoadRunner. These servers allow for concurrent processing, long-lived memory, and asynchronous task execution, enhancing response times and reducing overhead in high-traffic applications.
    - Laravel Reverb: brings blazing-fast and scalable real-time WebSocket communication directly to your Laravel application.
    - Laravel Breeze: Breeze is a minimal, simple implementation of all of Laravel's authentication features, including login, registration, password reset, email verification, and password confirmation. In addition, Breeze includes a simple "profile" page where the user may update their name, email address, and password.
    - Laravel Jetstream: Jetstream provides a beautifully designed application scaffolding for Laravel and includes login, registration, email verification, two-factor authentication, session management, API support via Laravel Sanctum, and optional team management. Jetstream is designed using Tailwind CSS and offers your choice of Livewire or Inertia driven frontend scaffolding.

- Laravel Jetstream Vs Laravel Breeze
    - 
    ![alt text](<images/laravel-jetstream-vs-breeze.png>)
- Laravel Vapor
    - https://vapor.laravel.com/
    - Laravel Vapor is a serverless deployment platform for Laravel, powered by AWS. Launch your Laravel infrastructure on Vapor and fall in love with the scalable simplicity of serverless.
- Laravel Forge Vs Laravel Vapor
    - Laravel Forge: Forge is a server management tool for provisioning and managing servers for Laravel applications.
    - Laravel Vapor: Vapor is a serverless deployment platform for Laravel applications, designed to run on AWS infrastructure.
    - Forge:
        - Key Features:
            - Server Management: Manages VPS providers like DigitalOcean, Linode, AWS, Vultr, and more.
            - Web Servers: Automatically configures Nginx, PHP, MySQL, and other services.
            - Deployment: Offers easy deployment with Git integration and hooks.
            - Customizable: Provides SSH access for advanced configurations.
            - Database Management: Allows you to manage MySQL, MariaDB, and PostgreSQL.
            - Cost-Effective: You only pay for Forge and your server costs. Forge starts at $12/month.
        - Use Cases:
            - Small to medium applications.
            - Developers who prefer managing their own servers.
            - Projects that need traditional server hosting (e.g., VPS or dedicated servers).
        - Advantages:
            - Affordable and straightforward for server management.
            - Complete control over server configurations.
            - Supports any Laravel application or PHP project.
        - Limitations:
            - You are responsible for server maintenance (e.g., scaling, backups, uptime).
            - No serverless support; it uses traditional server hosting.
    - Vapor:
        - Key Features:
            - Serverless Deployment: Deploys applications on AWS Lambda, eliminating the need for server management.
            - Auto-Scaling: Automatically scales based on traffic demand, handling millions of requests effortlessly.
            - Storage & Queue Management: Integrated with AWS S3, SQS, and RDS for seamless operation.
            - Zero-Downtime Deployments: Uses Blue/Green deployment strategies for updates.
            - Advanced Features: Built-in support for caching, queues, custom domains, and HTTPS.
            - Pricing: Starts at $39/month + AWS usage costs.
        - Use Cases:
            - Large-scale applications with fluctuating traffic.
            - Applications that require high availability and scalability.
            - Developers who prefer serverless architecture.
        - Advantages:
            - No server maintenance required.
            - Scales automatically to meet traffic demands.
            - Deep integration with AWS services.
        - Limitations:
            - Higher cost compared to Forge (monthly fee + AWS usage).
            - Limited to AWS infrastructure.
            - Requires familiarity with AWS services and serverless concepts.
    - When to Choose Laravel Forge?
        - You need cost-effective hosting and want full control of your server.
        - Your application has predictable or low traffic.
        - You're comfortable managing servers and scaling manually.
    - When to Choose Laravel Vapor?
        - You expect unpredictable or high traffic with demand for auto-scaling.
        - You prefer a serverless approach with minimal server maintenance.
        - You need the power of AWS services integrated into your application.
    ![alt text](<images/laravel-forge-vs-vapor.png>)

- Laravel JWT Token
    - https://documentation.softwareag.com/webmethods/compendiums/v10-5/C_API_Management/index.html#page/api-mgmt-comp/co-jwt_usecase_workflow.html
    - To implement JWT (JSON Web Token) authentication in a Laravel application, you can use the popular tymon/jwt-auth package. 
    - composer require tymon/jwt-auth
    - php artisan jwt:secret
    - Update the config/auth.php file to set jwt as a guard
    - The User model should implement Tymon\JWTAuth\Contracts\JWTSubject. 
    - In the controller, implement methods for login, logout, and token refresh
    - Add routes for authentication in routes/api.php
    - To secure your API routes, use the auth:api middleware

- Laravel Authentication
    - https://laravel.com/docs/11.x/authentication


- Laravel ORM vs Symfony ORM
    - Laravel's ORM - Eloquent:
        - Laravel uses Eloquent ORM as its default ORM solution.
        - Eloquent is tightly integrated with Laravel and provides a fluent and expressive syntax for interacting with databases.
        - Eloquent follows the ActiveRecord pattern, where database tables are represented as PHP classes (Models), and instances of these classes represent individual rows in the database table.
        - Eloquent provides methods for performing CRUD (Create, Read, Update, Delete) operations on database records, defining relationships between different models, and querying the database using a fluent query builder interface.
        - Eloquent is designed to be easy to use and understand, making it a popular choice for developers building applications with Laravel.
    - Symfony's ORM - Doctrine ORM:
        - Symfony uses Doctrine ORM as its default ORM solution.
        - Doctrine ORM is a standalone library that can be used independently of Symfony, although it is commonly used with Symfony applications.
        - Doctrine ORM follows the Data Mapper pattern, where database entities are separated from the persistence layer and mapped to PHP objects using metadata.
        - Doctrine ORM provides a powerful query builder, lazy loading, caching, and other advanced features for managing database interactions.
        - Doctrine ORM is highly configurable and extensible, allowing developers to customize its behavior and integrate it with various databases and frameworks.
    - Differences and Considerations:
        - ActiveRecord vs. Data Mapper: The main difference between Eloquent and Doctrine ORM is their underlying architectural patterns. Eloquent follows the ActiveRecord pattern, where database entities are tightly coupled with PHP objects, while Doctrine ORM follows the Data Mapper pattern, where database entities are separated from the persistence layer.
        - Integration with Framework: Eloquent is tightly integrated with Laravel and is the default ORM solution for Laravel applications. On the other hand, Doctrine ORM is a standalone library that can be used with Symfony or other frameworks.
        - Complexity and Flexibility: Doctrine ORM tends to be more complex and flexible compared to Eloquent. It offers advanced features like lazy loading, caching, and event listeners, making it suitable for complex enterprise applications with demanding requirements. Eloquent, on the other hand, is designed to be simple and easy to use, making it a good choice for small to medium-sized projects.
- Laravel Sail
    - Laravel Sail is a lightweight command-line interface for interacting with Laravel applications, specifically designed to simplify the process of running Laravel applications locally using Docker. Sail provides a Docker-powered local development environment for Laravel projects, enabling developers to get up and running quickly without needing to configure complex server environments manually.
    - Here's an overview of Laravel Sail's key features and functionality:
        - Docker-Based Development Environment: Laravel Sail uses Docker containers to provide a consistent and isolated development environment for Laravel applications. It includes Docker containers for services like PHP, Nginx, MySQL, Redis, and more, all pre-configured to work seamlessly with Laravel.
        - Easy Setup and Configuration: Sail simplifies the process of setting up a local development environment for Laravel projects. With Sail installed, developers can initialize a new Laravel project or add Sail support to an existing project with a single command (sail new or sail install).
        - Command-Line Interface: Sail provides a set of command-line tools for interacting with Docker containers and managing the development environment. Developers can use commands like sail up to start the Docker containers, sail down to stop them, sail artisan to run Artisan commands, and sail test to run PHPUnit tests within the Docker environment.
        - Configuration Options: Sail allows developers to customize the Docker environment and configuration options according to their specific requirements. Configuration files for Docker services like Nginx, MySQL, and Redis can be modified or extended as needed.
        - Seamless Integration with Laravel Projects: Sail is designed to integrate seamlessly with Laravel projects, providing out-of-the-box support for common Laravel features and workflows. Developers can run Laravel commands, execute migrations, and access the application through a web browser as they would in a traditional local development environment.
-------------------------------------------------------------------------------
- Laravel Performance
    - How can you improve the performance of a Laravel application?
        - Use caching:
            - Store database query results using Laravel's cache drivers.
            - Use php artisan config:cache and route:cache for configuration and route caching.
        - Optimize database queries:
            - Avoid N+1 queries using with() and lazy loading.
            - Use indexing in the database.
        - Queue time-consuming tasks:
            - Use Laravel's queue system for tasks like email sending and report generation.
        - Minimize asset size:
            - Use Laravel Mix for CSS and JS minification.
        - Minimize the HTTP calls
            - Use webpack tools to merge css/js into single one to reduce the http request.
        - Optimize image loading:
            - Use tools like Spatie's laravel-medialibrary for efficient image handling.
        - Enable OPcache:
            - Improve PHP script performance by caching precompiled bytecode.
        - Database optimization:
            - Use proper relationships and efficient joins.
            - Limit fetched data using select().
        - USE CDN, Apply Horizontal Scalling techniques, Use Load balancing etc.
        - https://laravel.com/docs/11.x/cache

    - What is route caching, and how does it improve performance?
        - Route caching compiles all routes into a single PHP file for faster route registration. It is beneficial in applications with a large number of routes.
        - php artisan route:cache
        - php artisan route:clear
    - What is eager loading, and how does it help performance?
        - Eager loading retrieves related models with the main query to avoid multiple database calls.
        - $users = User::with('posts')->get();
    - How does the queue system improve Laravel application performance?
        - The queue system offloads long-running tasks (e.g., email sending, report generation) to background jobs.
        - Mail::to($user)->queue(new WelcomeEmail($user));
        - php artisan queue:work
    - What is the benefit of using Laravel's config:cache command?
        - Caching configuration files combines all configuration options into a single file. This reduces the time spent loading configurations.
        - php artisan config:cache
    - How does Laravel handle asset optimization?
        - Laravel Mix provides a fluent API for asset optimization.
        - mix.minify('public/js/app.js');
    - How do you monitor and debug performance issues in Laravel?
        - Use Laravel Telescope to debug queries, jobs, and logs.
        - Use Debugbar to measure execution time and queries.
        - Use APM tools like New Relic or Blackfire for in-depth performance monitoring.
    - How can OPcache improve Laravel performance?
        - OPcache caches precompiled PHP scripts in memory, reducing compilation time.
        - Configuration in php.ini:
        ```php
        opcache.enable=1
        opcache.memory_consumption=128
        opcache.max_accelerated_files=10000
        ```
    - How does Laravel handle large data efficiently?
        - Lazy Collections: For processing large datasets without loading everything into memory.
        ```php
        User::lazy()->each(function ($user) {
            // Process user
        });
        ```
        - Chunking: Process data in chunks.
        ```php
        User::chunk(100, function ($users) {
            foreach ($users as $user) {
                // Process user
            }
        });
        ```
    - What is Redis, and how is it used in Laravel?
        - Redis is an in-memory data store used for caching and session management in Laravel
        - Install: composer require predis/predis
        - Config in .env
        ```php
        CACHE_DRIVER=redis
        QUEUE_CONNECTION=redis
        ```
    - How does Laravel handle API performance?
        - Use caching for API responses.
        - Implement rate limiting using throttle middleware.
        - Use eager loading to fetch related resources efficiently.
        - Compress JSON responses with middleware.
    - What is the role of view:cache in Laravel?
        - view:cache compiles all Blade templates into PHP files, reducing rendering time.
        - php artisan view:cache
        - php artisan view:clear
    - What is Horizon, and how does it help in performance optimization?
        - Horizon is a Laravel tool for managing Redis queues. It provides a dashboard for monitoring jobs, retrying failed jobs, and observing queue performance.
    - Optimization
        - https://laravel.com/docs/11.x/deployment#optimization
        - When deploying your application to production, there are a variety of files that should be cached, including your configuration, events, routes, and views. Laravel provides a single, convenient optimize Artisan command that will cache all of these files. This command should typically be invoked as part of your application's deployment process:
        - php artisan optimize
    - You’re designing an API with 1M daily requests. How would you ensure scalability?
        - Use Laravel Passport or Sanctum for token-based authentication.
        - Implement caching for frequently accessed data.
        - Distribute traffic using load balancers.
        - Optimize database queries and implement sharding if needed.
        - Queue background tasks with Redis.

-------------------------------------------------------------------------------
- Laravel Security
    - Security packages
        - Laravel Sanctum: Laravel Sanctum (formerly known as Laravel Airlock) provides a lightweight authentication system for SPAs (Single Page Applications), mobile apps, and simple APIs. It offers token-based authentication using API tokens, allowing you to authenticate users without the need for session cookies.
        - Laravel Passport: Laravel Passport is an OAuth2 server implementation for Laravel applications. It provides a full-featured authentication solution for APIs, allowing you to issue access tokens, manage clients, and authorize users using OAuth2 flows.
        - Laravel Breeze: Laravel Breeze is a minimalistic authentication starter kit for Laravel applications. It provides a simple and lightweight way to add authentication features like registration, login, and password reset to your applications using Laravel's built-in authentication system.
        - Laravel Fortify: Laravel Fortify is a feature-rich authentication library for Laravel applications. It provides a flexible and customizable authentication solution with support for features like two-factor authentication, email verification, password confirmation, and more.
        - Laravel Security Checker: Laravel Security Checker is a package that integrates Symfony's Security Advisories Checker into Laravel applications. It allows you to check your application's dependencies against the Symfony security advisories database to identify any known security vulnerabilities.
        - Laravel Telescope: Laravel Telescope is a debugging and profiling tool for Laravel applications. While not primarily a security package, Telescope can help you identify security vulnerabilities and performance issues in your applications by providing insights into database queries, requests, exceptions, and more.
        - Laravel Security Headers: Laravel Security Headers is a package that helps you manage and configure security headers in your Laravel applications. It allows you to easily add security headers like Content Security Policy (CSP), Strict Transport Security (HSTS), and X-XSS-Protection to your application's responses to enhance security.
    - Encryption
        - To encrypt data, you can use the encrypt() method provided by the Crypt facade. This method takes a string as input and returns the encrypted version of that string.
        - Decryption: To decrypt data, you can use the decrypt() method provided by the Crypt facade. This method takes the encrypted string as input and returns the decrypted version of that string.
        - 
        ```php
        use Illuminate\Support\Facades\Crypt;

        //encrypt
        $encrypted = Crypt::encrypt('sensitive_data');

        //descrypt
        $decrypted = Crypt::decrypt($encrypted);
        ```
    - Disabled CSRF token
        - Open the app/Http/Middleware/VerifyCsrfToken.php file in your Laravel project.
        - To disable CSRF protection for specific routes in Laravel, you can add those routes to the $except array in the VerifyCsrfToken middleware.
    - What are some common security features provided by Laravel?
        - Laravel includes built-in security features such as:
        - CSRF Protection: Protects against cross-site request forgery attacks using @csrf tokens in forms.
        - Password Hashing: Uses the bcrypt algorithm or Argon2 for secure password storage.
        - Encryption: Provides AES-256 and AES-128 encryption via the Crypt facade.
        - Validation and Sanitization: Validates input data using Request and removes potentially harmful scripts.
        - SQL Injection Prevention: Automatically escapes query parameters when using Eloquent or query builder.
    - How does Laravel protect against cross-site scripting (XSS)?
        - Laravel automatically escapes output in Blade templates using {{ $variable }}. For unescaped output, developers can use {!! $variable !!} but must sanitize the data manually to prevent XSS.
    - How does Laravel secure passwords?
        - Laravel uses the bcrypt algorithm or Argon2 for password hashing. Passwords are hashed using the Hash facade:
    - What is the purpose of the App\Http\Middleware\VerifyCsrfToken middleware?
        - This middleware ensures that all POST, PUT, PATCH, and DELETE requests contain a valid CSRF token. If the token is invalid, the request is rejected.
    - How can you secure your Laravel application in production?
        - Use HTTPS and configure SSL/TLS certificates.
        - Set the APP_DEBUG to false in the .env file.
        - Use config:cache and route:cache for optimized configurations.
        - Implement rate limiting using the ThrottleRequests middleware.
        - Regularly update Laravel and its dependencies to patch vulnerabilities.
    -  How can you validate input in Laravel to prevent malicious data?
        - Laravel provides built-in validation for user inputs:
        - 
        ```php
        $request->validate([
            'email' => 'required|email',
            'password' => 'required|min:8',
        ]);

        ```
    - Validation in Laravel
        - https://laravel.com/docs/11.x/validation
        - The validate() method is commonly used within controllers to validate incoming requests
        ```php
        public function store(Request $request)
        {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'required|email|unique:users,email',
                'password' => 'required|min:8|confirmed',
            ]);

            // Code to handle validated data
        }

        ```
        - Common Validation Rules
            - required: The field must be present and not empty.
            - email: Ensures the input is a valid email address.
            - unique:table,column: Checks that the value is unique in a specified database table/column.
            - confirmed: Requires an additional _confirmation field (e.g., password_confirmation) to match.
            - min:n / max:n: Sets minimum or maximum string/array length or numeric value.
            - regex:pattern: Ensures the value matches a specific regular expression.

    - Laravel Authorization
        - https://laravel.com/docs/11.x/authorization
        - Laravel provides tools like Policies, Gates, and Middleware to implement authorization rules.
            - Policies: Used to authorize actions on models.
            - Gates: Used for simple authorization logic.
            - Middleware: Used for route-based authorization (e.g., auth, can middleware).
        - What is the difference between Gates and Policies in Laravel?
            - Gates: Typically used for simple, one-off authorization checks. They are closure-based and can be defined in the AuthServiceProvider.
            - Policies: Designed for more complex and resource-driven authorization. Policies are associated with a model (e.g., PostPolicy for Post model). 
        - How do you create and register policies in Laravel?
            - php artisan make:policy PostPolicy
        - How do you use middleware for authorization in Laravel?
            - You can use middleware for route-based authorization checks. Laravel includes built-in authorization middleware like auth, can, and role. 
            - auth Middleware: This middleware ensures the user is authenticated.
            ```php
            Route::middleware('auth')->get('/dashboard', function () {
                // Only authenticated users can access this
            });
            ```
            - can Middleware: Used for gate-based authorization:
            ```php
            Route::middleware('can:update,post')->get('/posts/{post}/edit', function (Post $post) {
                // User must be authorized to update the post
            });
            ```
        - How can you restrict access to certain routes based on user roles in Laravel?
            - You can use middleware to restrict access based on roles. One common approach is to use the can middleware with custom policies or roles-based gates.
            - Example of restricting routes based on roles:
            ```php
            Route::get('/admin', function () {
                // Admin-only logic
            })->middleware('can:isAdmin');
            ```
            - In the AuthServiceProvider:
            ```php
            Gate::define('isAdmin', function ($user) {
                return $user->role === 'admin';
            });
            ```
        - What are some of the built-in authorization methods in Laravel?
            - authorize: Checks if the user is authorized to perform an action (typically used in controllers).
            - can: Checks if the user has a specific ability (can be used in routes, views, etc.).
            - hasRole: Can be implemented in user models to check the user’s role (typically with packages like spatie/laravel-permission).
            - Gate::allows(): Returns a boolean indicating if a user is authorized to perform an action.
            - Gate::denies(): Returns a boolean indicating if a user is not authorized.
        - How do you check if a user has a specific permission in Laravel?
            - Permissions can be checked with Gate or using packages like spatie/laravel-permission. With Gate:
            ```php
            if (Gate::allows('edit-post', $post)) {
                // User has permission to edit the post
            }
            ```
            - With spatie/laravel-permission:
            ```php
            if ($user->can('edit post')) {
                // User has the 'edit post' permission
            }
            ```
        - What is the difference between authorize and authorizeForUser?
            - authorize: Checks the current authenticated user for permission.
            - authorizeForUser: Checks for authorization based on a specific user (other than the currently authenticated one).
            - $this->authorizeForUser($user, 'update', $post);  // Checks for authorization based on the `$user`
        - How can you prevent unauthorized users from accessing a controller method?
            - You can use middleware like can or auth to prevent unauthorized users from accessing methods.
        - How can you implement role-based access control (RBAC) in Laravel?
            - Role-based access control can be implemented using middleware or using third-party packages like spatie/laravel-permission
            - composer require spatie/laravel-permission
            - Define roles and permissions
            ```php
            $role = Role::create(['name' => 'admin']);
            $permission = Permission::create(['name' => 'edit posts']);
            $role->givePermissionTo($permission);
            ```
            - Assign roles to users:
            ```php
            $user->assignRole('admin');
            ```
            - Check for roles/permissions
            ```php
            if ($user->hasRole('admin')) {
                // Grant access
            }

            if ($user->can('edit posts')) {
                // Grant access
            }
            ```


-------------------------------------------------------------------------------

- Laravel Routing 
    - https://laravel.com/docs/11.x/routing 
    - Types of Routing
        - Web Routes:
            - These routes are defined in the routes/web.php
            - 
            ```php
            Route::get('/', function () {
                return view('welcome');
            });
            ```
        - API Routes:
            - These routes are defined in the routes/api.php
            - Route::get('/users', 'UserController@index');
    - Reverse Routing
        - Reverse routing in Laravel refers to the ability to generate URLs or URIs for named routes using route names or route actions. Instead of hardcoding URLs directly into your views or controllers, reverse routing allows you to reference routes by their names, making your application more maintainable and flexible.
        - Defining Named Routes: When defining routes in your Laravel application, you can assign them names using the name() method. Route names should be unique and descriptive, making it easier to reference them later.
        - 
        ```php
        Route::get('/profile', 'ProfileController@show')->name('profile.show');
        ```
        - Generating URLs or URIs: Once you've defined named routes, you can generate URLs or URIs for these routes using the route() helper function or the url() method in your views or controllers. By passing the route name 'profile.show' to the route() helper function, Laravel will automatically generate the URL or URI for the named route.
        - 
        ```php
        // Using the route() helper function
        $url = route('profile.show');

        // Using the url() method
        $url = url('/profile');
        ```
        - Route Parameters: If your route requires parameters, you can pass them as the second argument to the route() helper function. Laravel will replace the route's placeholders with the provided parameters.
        - 
        ```php
        $url = route('user.show', ['id' => 1]);
        ```
        - Reverse routing simplifies the process of generating URLs or URIs for routes in your Laravel application and makes your code more readable and maintainable. It also reduces the risk of broken links when changing route definitions, as you only need to update the route name in one place instead of multiple occurrences of the URL throughout your application.
    
    - Reverse routing Vs named routing
        - In Laravel, reverse routing and named routing are related concepts but serve slightly different
        - Named Routing:
            - Named routing refers to the practice of assigning a unique name to a route definition. This allows you to refer to the route by its name elsewhere in your application code, rather than hardcoding the URL directly. Named routes are defined using the name() method when defining routes.
            - Route::get('/profile', 'ProfileController@show')->name('profile.show');
            - Then, you can generate URLs or links to the named route using the route() helper function:
            - $url = route('profile.show');
        - Reverse Routing:
            - Reverse routing is a technique used to generate URLs or URIs for named routes. It involves using the route() helper function or the url() method to generate a URL based on the name of a route. Reverse routing allows you to dynamically generate URLs based on route names rather than hardcoding them, which can make your code more flexible and easier to maintain.
            - $url = route('profile.show');
            - Reverse routing relies on named routes to generate URLs dynamically. By providing the route name to the route() helper function, Laravel can generate the corresponding URL based on the route definition.
        - In summary, named routing is the practice of assigning unique names to route definitions, while reverse routing is the process of dynamically generating URLs or URIs for named routes using their assigned names. Together, these concepts help improve code readability, maintainability, and flexibility in Laravel applications.
    - Route Model binding
        - Route model binding in Laravel is a feature that allows you to automatically inject model instances into route closures or controller methods based on the route's parameter names. Instead of manually retrieving the model from the database using the route parameters, Laravel automatically resolves the model instance and passes it to the route handler.
        - In this example, {user} is the route parameter representing the primary key of the User model.
        - 
        ```php
        Route::get('/users/{user}', function (App\Models\User $user) {
            return $user;
        });
        ```
    - What are routes in Laravel, and how are they defined?
        - Routes in Laravel map URL requests to specific controllers or closures. They are defined in the routes directory, with separate files for web, API, console, and channels.
        - 
        ```php
        //controller
        Route::get('/home', [HomeController::class, 'index']);

        //closure
        Route::get('/welcome', function () {
            return 'Welcome to Laravel!';
        });

        Route::match(['get', 'post'], '/submit', [FormController::class, 'submit']);
        ```
    - What is a named route in Laravel?
        - Named routes allow developers to assign names to routes, making it easier to reference them
        - 
        ```php
        Route::get('/user/profile', [UserController::class, 'profile'])->name('user.profile');

        // Usage
        $url = route('user.profile');
        return redirect()->route('user.profile');
        ```
    - What are route parameters in Laravel, and how are they used?
        - Route parameters allow dynamic segments in the URL. They can be:
        - 
        ```php
        //required
        Route::get('/user/{id}', [UserController::class, 'show']);

        //optional
        Route::get('/user/{id?}', [UserController::class, 'show']);
        ```
    - How can you apply constraints to route parameters?
        - 
        ```php
        //Use regular expressions:
        Route::get('/user/{id}', [UserController::class, 'show'])->where('id', '[0-9]+');

        //Or use the RouteServiceProvider
        Route::pattern('id', '[0-9]+');
        ```
    - How does Laravel handle route grouping?
        - Route groups allow applying shared attributes like middleware, prefix, or namespace to multiple routes.
        - 
        ```php
        Route::prefix('admin')->middleware('auth')->group(function () {
            Route::get('/dashboard', [AdminController::class, 'dashboard']);
            Route::get('/profile', [AdminController::class, 'profile']);
        });

        ```
    - What is route model binding in Laravel?
        - Route model binding automatically injects model instances into routes based on route parameters.
        - 
        ```php
        Route::get('/user/{user}', function (App\Models\User $user) {
            return $user->name;
        });
        ```
    - How can you define a fallback route in Laravel?
        - A fallback route handles unmatched URLs
        - 
        ```php
        Route::fallback(function () {
            return response()->view('errors.404', [], 404);
        });
        ```
    - How can you protect routes in Laravel?
        - Use middleware like auth
        - 
        ```php
        Route::middleware('auth')->group(function () {
            Route::get('/dashboard', [DashboardController::class, 'index']);
        });
        ```
    - What is the difference between Route::resource and Route::apiResource?
        - Route::resource: Creates routes for all CRUD operations, including web-based routes like create and edit.
        - Route::apiResource: Skips create and edit since they are not used in APIs.

    - How can you resolve naming conflicts in Laravel routes?
        - Use unique names for conflicting routes or use route groups with prefixes
        - 
        ```php
        Route::prefix('admin')->group(function () {
            Route::get('/dashboard', [AdminController::class, 'index'])->name('admin.dashboard');
        });
        ```
    - What are some best practices for Laravel routing?
        - Use meaningful route names.
        - Group related routes using prefixes and middleware.
        - Apply route model binding for cleaner code.
        - Cache routes in production for performance.
        - Avoid using closures for complex logic; use controllers instead.

-------------------------------------------------------------------------------
- Laravel Middleware
    - What is middleware in Laravel?
        - Middleware in Laravel acts as a filter for HTTP requests entering your application. It is used to inspect, modify, or reject requests before reaching the controller or after receiving a response.
    - How do you create middleware in Laravel?
        - php artisan make:middleware CheckUserRole
    - How do you register middleware in Laravel?
        - Middleware can be registered in the app/Http/Kernel.php file
            - Global Middleware: Applied to all routes in the application. It is defined in the $middleware array of Kernel.php.
            - Route Middleware: Applied to specific routes or groups of routes using aliases defined in $routeMiddleware.
            - 
            ```php
            protected $routeMiddleware = [
                'role' => \App\Http\Middleware\CheckUserRole::class,
            ];
            ```
    - How do you apply middleware to routes?        
        - 
        ```php
        //SingleRoute
        Route::get('/admin', [AdminController::class, 'index'])->middleware('role');

        //Group Route
        Route::middleware(['role'])->group(function () {
            Route::get('/admin', [AdminController::class, 'index']);
            Route::get('/settings', [SettingsController::class, 'index']);
        });
        ```
    - How do you pass parameters to middleware in Laravel?
        - Middleware can accept parameters through routes:
        ```php
        Route::get('/profile', [ProfileController::class, 'index'])->middleware('role:admin');
        ```
        - The middleware handles it using the $next parameter:
        ```php
        public function handle($request, Closure $next, $role)
        {
            if ($request->user()->role !== $role) {
                return redirect('home');
            }
            return $next($request);
        }
        ```
    - What is the priority of middleware execution in Laravel?
        - Laravel executes middleware in the order defined in the $middlewarePriority array in Kernel.php. Middleware with dependencies should be listed in the correct order to ensure proper execution.
    - Can middleware modify the request or response?
        - Yes, middleware can modify both the incoming request and the outgoing response. For example:
        ```php
        public function handle($request, Closure $next)
        {
            $response = $next($request);
            $response->headers->set('X-Custom-Header', 'CustomValue');
            return $response;
        }
        ```
    - What are some common uses of middleware in Laravel?
        - Authentication (auth middleware).
        - Role-based access control.
        - Logging request data.
        - Modifying response headers.
        - Enforcing API rate limits.
    - What is a terminating middleware in Laravel?
        - Terminating middleware performs tasks after the response is sent to the browser. Middleware can implement the terminate method. This limits requests to 60 per minute.
        ```php
        public function terminate($request, $response)
        {
            // Logic after response
        }
        ```
    - How do you implement API rate limiting with middleware in Laravel?
        - Laravel provides the throttle middleware for rate limiting.
        ```php
        Route::middleware('throttle:60,1')->group(function () {
            Route::get('/api', [ApiController::class, 'index']);
        });
        ```
    - What is the difference between middleware and controllers in Laravel?
        - Middleware: Filters requests and responses, often focusing on cross-cutting concerns (e.g., authentication, logging).
        - Controllers: Handle the core logic and serve as the entry point for processing requests.
    - How does middleware chaining work in Laravel?
        - You can chain multiple middleware on a single route, Middleware are executed in the order they are listed.
        - Route::get('/admin', [AdminController::class, 'index'])->middleware(['auth', 'role:admin']);
    - What are some best practices for using middleware in Laravel?
        - Avoid overloading middleware with complex logic; use controllers or service classes instead.
        - Use route-specific middleware when possible to reduce overhead.
        - Organize middleware into logical categories (e.g., authentication, logging).
        - Test middleware thoroughly to ensure it handles edge cases.


-------------------------------------------------------------------------------

- What design patterns does Laravel use?
    - Service Container: Dependency Injection.
    - Repository Pattern: Often used with Eloquent.
    - Factory Pattern: For creating model factories.
    - Observer Pattern: For event handling in models.
    - Singleton Pattern: For services resolved via the container.
    - Adapter Pattern: Facades act as adapters for underlying services.

- Explain the role of Service Providers and Service Containers in Laravel.
    - Service Provider: Registers services, bindings, or configurations into the application.
    - Service Container: Handles Dependency Injection and resolves services.


- Laravel Closure
    - In Laravel, closures are often used for defining callback functions or routes and offer a compact, flexible way to execute specific blocks of code. Laravel heavily utilizes closures in areas like routing, middleware, and custom query functions.
    - Routing: Closures are commonly used to define inline route actions without needing a separate controller. In routes/web.php or routes/api.php, you can define routes with closures: This route directly returns a response without referencing a controller, which can be handy for quick prototyping or simple routes.
        - 
        ```php
        Route::get('/welcome', function () {
            return 'Welcome to Laravel!';
        });
        ```
    - Middleware: Closures can be used in middleware to perform tasks before or after an HTTP request. For example: This provides an inline, one-time middleware function for a route
        - 
        ```php
        Route::get('/dashboard', function () {
            // Dashboard content
        })->middleware(function ($request, $next) {
            // Middleware logic
            if (auth()->guest()) {
                return redirect('/login');
            }
            return $next($request);
        });
        ```
    - Model Scopes and Query Callbacks: In Eloquent models, closures can be passed as callbacks to methods like where, orWhere, and even within relationships. Here, the closure helps create complex query logic without needing to write SQL directly
        - 
        ```php
        $users = User::where(function ($query) {
            $query->where('status', 'active')
                ->orWhere('role', 'admin');
        })->get();
        ```
    - Event Listeners and Queue Closures: Laravel allows you to define closures for event listeners. This can be useful when you want to keep the event logic minimal:
        - 
        ```php
        Event::listen('user.registered', function ($user) {
            // Handle the event, e.g., send a welcome email
        });
        ```
    - Custom Commands and Schedules: In Artisan commands, you can use closures to quickly define logic for commands or schedules. For example, in App\Console\Kernel.php, closures can be used in task scheduling:
        - 
        ```php
        $schedule->call(function () {
            // Task logic
        })->daily();
        ```
    - Benefits of Using Closures in Laravel
        - Simplicity: For small, one-off functions, closures are easier than creating full classes or methods.
        - Encapsulation: Closures encapsulate logic within a limited scope, avoiding side effects.
        - Readability: For inline operations, closures keep code short and readable, especially for defining routes and simple callbacks.

-------------------------------------------------------------------------------    
- Laravel Queues Vs Jobs
    - Queues:
        - Queues in Laravel provide a way to defer the processing of time-consuming tasks, such as sending emails, processing image uploads, or interacting with external APIs, to a background process. Instead of performing these tasks synchronously within the request cycle, where they can slow down the response time of your application, you can push them onto a queue for processing later.
        - Laravel queues are backed by different queue drivers, such as Redis, Beanstalkd, Amazon SQS, and more. Queues allow you to distribute the processing load across multiple workers, enabling your application to scale more efficiently.
        - Configure the queue connection and driver in Laravel's config/queue.php configuration file. Choose the appropriate queue driver based on your application's requirements and infrastructure setup.
        - Start the queue worker process using the artisan queue:work command. Laravel's queue worker continuously polls the queue for pending jobs and processes them in the background.
        - Monitor the status of queues, pending jobs, and failed jobs using Laravel's artisan commands (queue:listen, queue:failed, queue:restart, etc.) or third-party monitoring tools.
    - Jobs:
        - Jobs in Laravel represent individual tasks that are added to a queue for processing. Each job class encapsulates a specific task that needs to be performed asynchronously. Jobs typically implement the Illuminate\Contracts\Queue\ShouldQueue interface, indicating that they should be pushed onto a queue for processing rather than executed immediately.
        - Laravel jobs define the work that needs to be done and contain the logic necessary to perform the task. They can include data or parameters needed for processing and can interact with other parts of your application, such as database models or external services.
    - In summary, queues provide a mechanism for managing and processing asynchronous tasks, while jobs represent the individual tasks that are added to queues for processing. Jobs encapsulate the logic for performing specific tasks asynchronously, allowing your application to handle background processing efficiently.
    - How does Laravel handle Job Queues, and how do you scale them?
        - Jobs are dispatched using dispatch() and processed using workers (php artisan queue:work).
        - Scaling strategies:
            - Use Redis as the queue backend for better performance.
            - Horizontally scale workers.
            - Monitor queues using Laravel Horizon.
    -  What are Laravel Jobs and Queues?
        - Jobs: Units of work that can be deferred for processing later. Jobs are defined in classes that implement the Illuminate\Contracts\Queue\ShouldQueue interface.
        - Queues: Mechanism to defer job execution, enabling asynchronous processing. Jobs are pushed onto a queue and processed later by workers.
    - How do you create a job in Laravel?
        - php artisan make:job ProcessOrder
        - This creates a job class in the App\Jobs directory:
        ```php
        namespace App\Jobs;

        use Illuminate\Bus\Queueable;
        use Illuminate\Contracts\Queue\ShouldQueue;
        use Illuminate\Foundation\Bus\Dispatchable;
        use Illuminate\Queue\InteractsWithQueue;
        use Illuminate\Queue\SerializesModels;

        class ProcessOrder implements ShouldQueue
        {
            use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

            public function __construct()
            {
                // Initialize job-specific data
            }

            public function handle()
            {
                // Job processing logic
            }
        }
        ```
    - How do you dispatch a job in Laravel?
        - Dispatch using the dispatch() helper: ProcessOrder::dispatch($order);
    - What are the available queue drivers in Laravel?
        - sync: Executes jobs immediately (synchronous).
        - database: Stores jobs in the database table jobs.
        - redis: Uses Redis to store queued jobs.
        - beanstalkd: A fast, distributed, in-memory work queue.
        - sqs: Amazon Simple Queue Service (SQS).
        - null: Discards jobs (used for testing).
    - What is the purpose of php artisan queue:work?
        - This command starts a worker that listens to a queue and processes jobs.
    - How do you retry failed jobs in Laravel?
        - Retry all failed jobs: php artisan queue:retry all
        - Retry a specific job: php artisan queue:retry <job_id>
    - What is the difference between queue:work and queue:listen?
        - queue:work:
            - Starts a worker process for the queue.
            - More efficient as it keeps the worker running.
            - Use in production.
        - queue:listen:
            - Restarts the worker after each job.
            - Slower but useful during development for real-time changes.
    - What are delayed jobs in Laravel, and how do you implement them?
        - Delayed jobs postpone job execution for a specific duration. Use delay() method.
        - ProcessOrder::dispatch($order)->delay(now()->addMinutes(10));
    - What is the failed_jobs table in Laravel?
        - A table that stores failed job details for debugging and retrying.
    - How does Laravel handle job priorities?
        - Assign queues based on priority and specify the queue while dispatching
        ```php
        ProcessOrder::dispatch()->onQueue('high-priority');
        php artisan queue:work --queue=high-priority,default
        ```
    - How would you handle jobs that depend on each other?
        - Use job chains:
        ```php
        Bus::chain([
            new JobOne($data),
            new JobTwo($data),
        ])->dispatch();
        ```
    - How would you handle a high-volume queue system in production?
        - Use Redis for better performance.
        - Scale workers horizontally.
        - Monitor queues with Laravel Horizon.
        - Use Supervisor to manage queue workers.
    - How would you implement rate-limiting for queued jobs?
        - Use Redis to throttle job processing:
        ```php
        RateLimiter::for('job-rate-limit', function (Job $job) {
            return Limit::perMinute(10);
        });
        ```
    - How do you monitor queue performance in Laravel?
        - Use Laravel Horizon for real-time queue monitoring.
    - How do you handle large payloads in a queue?
        - Use Redis or SQS as a queue backend for better performance.
        - Avoid serializing large objects. Store large data in the database or S3 and pass references to the job.

-------------------------------------------------------------------------------

- Laravel mutator and accessor
    - Mutators allow you to modify attribute values before they are saved to the database
    - Accessor allow you to manipulate attribute values when retrieving them from the database.
    - Example
    ```php
    namespace App\Models;

    use Illuminate\Database\Eloquent\Model;

    class Product extends Model
    {
        // Mutator to set the price attribute in cents
        public function setPriceAttribute($value)
        {
            $this->attributes['price'] = $value * 100; // Store price in cents
        }

        // Accessor to retrieve the price attribute in dollars
        public function getPriceAttribute($value)
        {
            return $value / 100; // Retrieve price in dollars
        }
    }
    ```
- Laravel Log Package
    - composer require monolog/monolog
- Laravel RESTFul API
    - Calling PUT - PATCH Method
        - To call a PUT or PATCH method from a Laravel Blade view, you typically use a hidden form field within a regular HTML form. This hidden field is used to spoof the PUT or PATCH request method, as HTML forms by default only support GET and POST methods.
        - 
        ```php
        <form action="{{ route('resource.update', ['id' => $resource->id]) }}" method="POST">
            @csrf
            @method('PUT') <!-- Or @method('PATCH') -->
            <!-- Other form fields -->
            <button type="submit">Update Resource</button>
        </form>

        use App\Http\Controllers\ResourceController;
        use Illuminate\Http\Request;

        Route::put('/resource/{id}', [ResourceController::class, 'update'])->name('resource.update');
        ```
- How would you integrate a third-party payment gateway in Laravel?
    - Use packages like Omnipay or direct SDKs provided by the payment gateway.
    ```php
    Stripe::charges()->create([
        'amount' => 1000,
        'currency' => 'usd',
        'source' => $request->stripeToken,
    ]);
    ```

- 
- 
- 
- New in Laravel 11
    - Minimal Application Structure: Laravel 11's default structure is streamlined by removing the Console Kernel and simplifying the app folder, reducing clutter while keeping essential features like routing and middleware configurable in bootstrap/app.php as needed.
    - SQLite by Default: New applications now default to SQLite for database storage, facilitating quicker setup with minimal dependencies - ideal for rapid prototyping.
    - Laravel Reverb: This new WebSocket-based tool provides real-time communication, leveraging Redis for horizontal scaling, making it suitable for handling large traffic with multiple clients and channels. This update integrates smoothly with Laravel's existing event broadcasting capabilities.
    - Improved Testing and Job Handling: Laravel 11 introduces the withFakeQueueInteractions method for more precise queue interaction testing. Additionally, job retries and delays are now easier to manage, allowing developers to specify tries and backoff values directly in job classes.
    - Blade Component Tags and Tailwind CSS Pagination: Enhanced Blade syntax and pre-styled pagination views using Tailwind CSS simplify UI consistency across applications, making it easier to implement elegant and responsive designs.
    - Authorization and Encryption Improvements: Laravel 11 provides more granular authorization responses, letting developers add custom messages when access is denied. Additionally, encryption key rotation is now smoother, preventing user logouts during key updates.
    - Eager Load Limit and New Artisan Commands: Enhanced ORM functionality allows setting limits on eager-loaded relationships, improving data efficiency. New Artisan commands (make:class, make:enum, etc.) speed up class generation for a variety of structures.
- Symfony component used in laravel
    - HttpFoundation Component
        - Purpose: Handles the HTTP request and response objects.
        - Usage in Laravel: Laravel's Illuminate\Http\Request and Illuminate\Http\Response classes extend Symfony's Symfony\Component\HttpFoundation\Request and Symfony\Component\HttpFoundation\Response respectively. This component manages HTTP requests, responses, sessions, and cookies in Laravel.
    - Routing Component
        - Purpose: Handles URL routing and generates URLs based on route definitions.
        - Usage in Laravel: Laravel's routing system is built on top of Symfony's Routing component. This component matches incoming URLs to the defined routes and dispatches them to the appropriate controllers.
    -  EventDispatcher Component
        - Purpose: Manages event listeners and dispatches events to them.
        - Usage in Laravel: Laravel's event system is heavily inspired by Symfony's EventDispatcher component. Laravel uses this component to allow the application to listen to and dispatch events.
     Console Component
        - Purpose: Provides the tools to build command-line applications.
        - Usage in Laravel: Laravel's Artisan command-line interface is powered by Symfony's Console component. This component handles input, output, command definitions, and command execution.
    - VarDumper Component
        - Purpose: Provides tools for dumping variables.
        - Usage in Laravel: Laravel's dd() and dump() functions use Symfony's VarDumper component to output debug information.
    - Process Component
        - Purpose: Manages system processes in PHP.
        - Usage in Laravel: The Process component is used in Laravel for running system commands, such as when running tasks in Artisan commands.
    - Translation Component
        - Purpose: Handles translation and localization.
        - Usage in Laravel: Laravel uses Symfony's Translation component to manage translation files and translation strings.
    - Yaml Component
        - Purpose: Parses and dumps YAML files.
        - Usage in Laravel: While not as commonly used directly in Laravel, the Yaml component can be employed in Laravel applications to manage configuration files or other YAML-based data.
    - Mime Component
        - Purpose: Handles MIME types and file extensions.
        - Usage in Laravel: Laravel's file upload and response systems use the Mime component to determine the MIME type of files.

- Laravel Vs Lumen
    - ![alt text](<images/laravel-vs-lumen.png>)

- Symfony Vs Laravel -> https://chatgpt.com/c/7876303c-c68f-41d3-8017-0389fe9ae81e
- 
