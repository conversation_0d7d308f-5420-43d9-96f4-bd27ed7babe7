---------------------------------------------------------------------------------------------------------------------
### New in PHP8
---------------------------------------------------------------------------------------------------------------------
- https://stitcher.io/blog/new-in-php-8
- https://stitcher.io/blog/new-in-php-81
- https://stitcher.io/blog/new-in-php-82
- https://stitcher.io/blog/new-in-php-83
- https://stitcher.io/blog/new-in-php-84
- https://php.watch/versions/8.3/typed-constants
- https://www.php.net/manual/en/language.oop5.changelog.php
- 
- Union Types - public function foo(Foo|Bar $input): int|float;
- Nullsafe operator - $dateAsString = $booking->getStartDate()?->asDateTimeString();
- Named arguments 
- Attributes Annotations
- Match Expression
- Constructor property promotion
- New static return type
- New mixed return type
- New str_contains(), str_starts_with(), str_ends_with() functions
- readonly class - readonly class Post (8.2)
- constants in traits (8.2)
- Negative indices in arrays (8.3) - next index start with +1 negative index, earlier next index start with 0
- Property hooks (8.4) - class property itself define getter and setter,  eliminating the need for a lot of boilerplate code.
- Interfaces in PHP 8.0+
   - Can have private or protected constants.
   - Can define default methods using public methods with an implementation (similar to traits).
   - 
   ```php
   interface Animal {
      public function makeSound();

      public function eat() {
         echo "Eating...\n";
      }
   }
   ```
---------------------------------------------------------------------------------------------------------------------
### Sessions & JWT
---------------------------------------------------------------------------------------------------------------------
- If we set session.use_only_cookies and then disable the cookies on client browser, will session still works in php?
   - No, if session.use_only_cookies is set to 1 (enabled) in PHP and cookies are disabled on the client’s browser, the session will not work.
   - How to Handle This Situation
      - You can disable session.use_only_cookies and allow session IDs to be passed via the URL
         - session.use_only_cookies = 0
      - Use token-based authentication (e.g., JSON Web Tokens) for session management.
      - Store session data in localStorage or a similar client-side mechanism, though this requires more effort to secure.
   - 
   | Setting                | Value | Meaning                                           |
   |-------------------------|-------|---------------------------------------------------|
   | session.use_cookies     | 1     | Allow using cookies (default)                     |
   | session.use_only_cookies| 0     | Allow URL-based session ID if cookies not available |
   | session.use_trans_sid   | 1     | Automatically add session ID to URLs if cookies are disabled |

- If session.use_only_cookies set to 1 and session.use_trans_sid set to 1 then which one get precedance ?
   - session.use_only_cookies get the precedance 
- How to set session expiration in php
   - Set session.gc_maxlifetime and session.cookie_lifetime for expiration control.
   - Use session_set_cookie_params() for more secure and flexible cookie handling.
   - Implement timeout logic for finer control over session expiration based on activity.
   - Ensure garbage collection is correctly configured to clean expired sessions on the server.
- How to handle session ideal timeout in php
   - Use the session to store the timestamp of the user's last activity. Each time the user interacts with the application, update this timestamp.
   - 
   ```php
   session_start();

   // Define the timeout duration in seconds
   $timeout_duration = 900; // 15 minutes

   // Check if the user has been inactive for too long
   if (isset($_SESSION['LAST_ACTIVITY']) && 
      (time() - $_SESSION['LAST_ACTIVITY']) > $timeout_duration) {
      // Session has expired due to inactivity
      session_unset(); // Unset session variables
      session_destroy(); // Destroy the session
      header("Location: login.php"); // Redirect to the login page
      exit();
   }

   // Update last activity timestamp
   $_SESSION['LAST_ACTIVITY'] = time();
   ```
- Sessions Vs JWT Token:
   - 
   | Feature               | Sessions                                  | JWT (JSON Web Tokens)                |
   |------------------------|-------------------------------------------|--------------------------------------|
   | Storage                | Server-side (in memory, files, DB)        | Client-side (stored in browser)      |
   | Authentication State   | Server keeps session info                | Token contains user info itself      |
   | Scalability            | Harder to scale (server must share sessions) | Easy to scale (stateless)           |
   | Security               | Safer if server is secure                | Requires careful token management (no exposure) |
   | Speed                  | Requires server lookup per request       | Faster (token is verified locally)   |
   | Session Sharing        | Needs centralized session store (e.g., Redis) | Easy, as token travels with user    |
   | Invalidation           | Easy (destroy session on server)          | Harder (need token blacklisting)     |
   | Best For               | Traditional web apps (login sessions)    | APIs, Microservices, Mobile apps     |
   | Size                   | Minimal (session ID only)                 | Can be large (token carries data)    |
   | Expiration             | Server-controlled                        | Token-controlled (expiry field)      |


---------------------------------------------------------------------------------------------------------------------
### OOPs
---------------------------------------------------------------------------------------------------------------------
- 
- https://www.php.net/manual/en/language.oop5.php
- https://www.php.net/manual/en/language.oop5.changelog.php
- 
- Can we create private constructor?
   - No, you cannot directly create an instance of a class with a private constructor in PHP. The purpose of a private constructor is to restrict direct instantiation of the class from outside, commonly used in singleton patterns or static factory methods.
   - 
   ```php
      class UtilClass {
         // Private constructor prevents instantiation
         private function __construct() {}
      }

      $objUtil = new UtilClass(); // Error: Cannot instantiate
   ```
   - 
   ```php
   class Singleton {
      private static $instance = null;

      // Private constructor to prevent external instantiation
      private function __construct() {
         echo "Private constructor called.\n";
      }

      // Public static method to access the single instance
      public static function getInstance() {
         if (self::$instance === null) {
               self::$instance = new self();
         }
         return self::$instance;
      }
   }

   // Accessing the singleton instance
   $instance1 = Singleton::getInstance(); // Output: Private constructor called.
   $instance2 = Singleton::getInstance(); // No new instance is created.

   // Verify the instances are the same
   var_dump($instance1 === $instance2); // Output: bool(true)

   // Attempting direct instantiation will cause an error
   // $obj = new Singleton(); // Error: Call to private Singleton::__construct()
   ```

- method overloading and method overriding
   - Method overloading: PHP does not support method overloading in the same way as some other languages like Java. However, you can achieve similar functionality by using optional parameters or func_num_args() and func_get_args() functions.
   - Method overriding: Method overriding occurs when a subclass provides a specific implementation of a method that is already defined in its superclass. This allows for customization of behavior in the subclass.

- In which scenario we should use static method?
   - Utility functions that are not tied to any specific instance of a class.
   - Factory methods for creating objects.
   - Helper methods for performing common tasks.
   - Methods for accessing or manipulating shared resources.

- Can static methods be overridden in PHP?
   - No, static methods cannot be overridden in PHP. Inheritance does not apply to static methods. If a subclass defines a static method with the same name as a static method in the parent class, it will not override the parent class's static method; instead, it will hide it.

- Can we use $this in static method
   - It's important to note that $this can only be used within non-static methods of a class to refer to properties and methods of the current object instance. Static methods do not have access to $this because they are not associated with a specific object instance.

- What type of polymorphism supported by php?
   - PHP supports method overriding and interface implementation for polymorphism, which aligns primarily with runtime polymorphism. 
   - Runtime Polymorphism / Dynamic polymorphism (Method Overriding)
      - Definition: Occurs when a child class provides a specific implementation of a method already defined in its parent class.
      - Supported in PHP: Yes, through inheritance.
   - Compile Polymorphism / Static polymorphism (Method Overloading)
      - Definition: Allows multiple methods with the same name but different parameters in a class.
      - Supported in PHP: PHP does not natively support method overloading like Java or C++. However, you can simulate it using optional parameters, func_num_args(), or __call() (magic method).

- How many ways we can achieve multiple inheritance?
   - Using Traits (Preferred Approach)
   - Using Interfaces
   - Combining Traits and Interfaces
   - Using Composition (Multiple class in constructor method)
      - By dependency injection
         - public function __construct(Logger $logger, Emailer $emailer) {
      - By Decorator Pattern 
   - 
   ![alt text](<images/multiple-inheritance-php.png>)

- Traits
   - Method Conflicts: 
      - If a class uses multiple traits that define methods with the same name, a fatal error will occur due to method name conflicts. To resolve conflicts, you can use the insteadof and as keywords to explicitly specify which method implementation should be used.
   - Method Precedence: 
      - Methods defined in the class itself take precedence over methods from traits. If a method with the same name is defined in both the class and a trait, the class method will override the trait method.

- Difference between interface and abstract class
   - Interface: An interface in PHP defines a contract by declaring method signatures without providing their implementation. It only specifies what methods a class implementing the interface must contain. Interfaces cannot contain any method implementations or properties.
   - Abstract Class: An abstract class is a class that cannot be instantiated on its own and may contain abstract methods (methods without a body) along with concrete methods (methods with implementation). Abstract classes can also contain properties.
   - Use Case:
      - Interface: Interfaces are suitable for defining a contract that multiple unrelated classes can adhere to. They are used to specify what methods a class must implement, without specifying how they should be implemented. Interfaces are commonly used to define APIs and ensure interoperability between different classes.
      - Abstract Class: Abstract classes are useful when you want to define a common structure or behavior that can be shared among related classes. They provide a way to partially implement a class and leave specific details to its subclasses. Abstract classes are often used when you have a base class with common functionality that needs to be extended by subclasses.
   - Visibility:
      - Interface: All methods declared in an interface must be public.
      - Abstract Class: Abstract classes can have methods with different visibility (public, protected, or private).
   - Summary:
      - In summary, interfaces are used to define contracts without any implementation details, allowing for multiple inheritance and interoperability between unrelated classes. Abstract classes, on the other hand, provide a way to define a common structure with both abstract and concrete methods, primarily used for inheritance and sharing common functionality among related classes.
   - When to Use What?
      - Use abstract methods when:
         - You need to provide common functionality to subclasses.
         - The base class needs to share properties and non-abstract methods.
      - Use interface methods when:
         - You want to define a contract for classes to follow.
         - You need multiple inheritance or a "blueprint" for unrelated classes.
   - 
   ![alt text](<images/abstract-vs-interface-1.png>)
   ![alt text](<images/abstract-vs-interface-2.png>)
   ![alt text](<images/abstract-vs-interface-3.png>)
   ![alt text](<images/abstract-vs-interface-4.png>)
   ![alt text](<images/abstract-vs-interface-5.png>)
   ![alt text](<images/abstract-vs-interface-6.png>)

- Can an interface extend another interface in PHP?
   - Yes, an interface can extend another interface in PHP. When an interface extends another interface, it inherits all the methods of the parent interface. The implementing class must define methods from both the child and parent interfaces.

- Can you implement multiple interfaces with methods of the same name in PHP?
   - Yes, a class can implement multiple interfaces with methods of the same name. However, the implementing class must provide a single implementation of the method, and it will fulfill the contract for both interfaces.

- Can an interface contain properties in PHP?
   - No, interfaces in PHP cannot contain properties. They can only declare method signatures. If you need properties, use an abstract class instead.
   - We can define constants in interface

- Can an interface have static methods in PHP?
   - Yes, starting with PHP 8.0, interfaces can define static methods. However, these static methods do not enforce implementation the same way as regular methods because static methods are tied to the class and not to the instance.

- What happens if two interfaces with conflicting method signatures are implemented in a class?
   - PHP will throw a fatal error because the class cannot resolve the conflicting method signatures.

- Can a class implement an interface and extend a class simultaneously?
   - Yes, a class can implement an interface and extend a class simultaneously. However, the class must define the interface methods in addition to inheriting or overriding methods from the parent class.
   - class FileLogger extends BaseLogger implements Logger {

- Interfaces Vs Concrete Class
   - Use an Interface:
      - When you need to define a contract for multiple unrelated classes.
      - When you want to decouple high-level components from low-level implementations.
      - When you expect the behavior to vary across implementations.
   - Use a Concrete Class:
      - When there's a single, definitive implementation.
      - When you're modeling a real-world entity or data structure.
      - When you need to reuse base functionality through inheritance.
   - 
   ![alt text](<images/interface-vs-concrete-class.png>)

- Can we create an instance of an abstract class in PHP?
   - No
- Can we create constructor in abstract class?
   - Yes, we can create a constructor in an abstract class in PHP. The constructor in the abstract class can be used to initialize properties that are common to all subclasses. Subclasses can call the parent constructor to ensure that the initialization code in the abstract class's constructor is executed.
   - 
   ```php
   abstract class Animal {
      protected $name;

      // Constructor in the abstract class
      public function __construct($name) {
         $this->name = $name;
      }

      // Abstract method (must be implemented by subclasses)
      abstract public function makeSound();

      public function getName() {
         return $this->name;
      }
   }

   class Dog extends Animal {
      public function __construct($name) {
         // Call the parent constructor
         parent::__construct($name);
      }

      public function makeSound() {
         return "Woof!";
      }
   }

   class Cat extends Animal {
      public function __construct($name) {
         // Call the parent constructor
         parent::__construct($name);
      }

      public function makeSound() {
         return "Meow!";
      }
   }

   // Create objects of subclasses
   $dog = new Dog("Buddy");
   $cat = new Cat("Whiskers");

   echo $dog->getName() . " says " . $dog->makeSound() . "\n"; // Output: Buddy says Woof!
   echo $cat->getName() . " says " . $cat->makeSound() . "\n"; // Output: Whiskers says Meow!
   ```
- Can an abstract class have both abstract and concrete methods in PHP?
   - Yes, an abstract class can contain both abstract methods (without implementation) and concrete methods (with implementation).
- Can an abstract class implement an interface in PHP?
   - Yes, an abstract class can implement an interface. However, the abstract class must either provide implementations for the interface's methods or declare itself abstract (leaving the implementation to its subclasses).
- Can an abstract class extend another abstract class in PHP?
   - Yes, an abstract class can extend another abstract class. The child abstract class inherits the abstract methods from the parent but doesn’t need to implement them. Subclasses of the child abstract class must implement all inherited and declared abstract methods.
- Can an abstract class be final in PHP?
   - No, an abstract class cannot be declared final in PHP. The purpose of an abstract class is to be extended, while the purpose of a final class is to prevent inheritance. These two concepts contradict each other.
- Can you have private abstract methods in PHP?
   - No, abstract methods cannot be private. They must be either protected or public. This is because abstract methods are meant to be overridden in subclasses, and private methods are not visible to child classes.

- What is the use of interface if it does not have method implementation logic
   - Defining Contracts: Interfaces establish contracts that classes must adhere to. They specify a set of methods that a class implementing the interface must provide. This ensures that any class implementing the interface will have certain functionality, regardless of its specific implementation details.
   - Promoting Code Consistency: By defining interfaces, you ensure that classes that implement them provide specific behaviors. This promotes consistency across different parts of your codebase, making it easier to understand and maintain.
   - Enabling Polymorphism: Interfaces allow for polymorphic behavior, where different objects can be treated interchangeably based on the interfaces they implement. This facilitates code flexibility and reusability, as you can write code that operates on interfaces rather than specific classes.
   - Facilitating Loose Coupling: Interfaces promote loose coupling between components of your system. By depending on interfaces rather than concrete implementations, you decouple different parts of your codebase, making it easier to replace or extend implementations without affecting other parts of the system.
   - Enforcing API Contracts: In libraries or frameworks, interfaces define the API contract that users of the library must follow. This ensures that users interact with the library in a consistent and expected manner.
   - Mocking and Testing: Interfaces are invaluable in unit testing and mocking frameworks. They allow developers to create mock objects that simulate the behavior of real objects by implementing the same interface. This is particularly useful for isolating and testing components in isolation.
   - Encouraging Composition over Inheritance: Interfaces promote composition-based design, where classes are composed of multiple objects that fulfill specific roles, rather than relying solely on inheritance. This approach leads to more flexible and maintainable code.
   - In summary, while interfaces do not contain method implementation logic, they play a crucial role in defining contracts, promoting consistency, enabling polymorphism, facilitating loose coupling, enforcing API contracts, aiding in testing, and encouraging good design practices in object-oriented programming.

- What is the use of final class?
   - Prevent Inheritance
   - Security and Integrity
      - The class handles sensitive operations (e.g., encryption, authentication, logging).
      - Modifications might introduce security vulnerabilities.
   - Optimization
      - In some cases, marking a class as final can allow PHP to optimize the performance of the class because the engine knows it will not be overridden.
   - Can we extends the final class in child class? No
   - Can we create instance of the final class? Yes

- How to extend the behavior of a final class in php?
   - Since a final class in PHP cannot be extended through inheritance, you must use other techniques to extend its behavior. Here are some effective strategies:
   - Composition (Preferred Approach)
      - Instead of inheriting from the final class, you can use composition by creating a new class that includes an instance of the final class as a property. This approach delegates calls to the methods of the final class and allows you to add new functionality.
      - 
      ```php
      final class FinalClass {
         public function doSomething() {
            return "FinalClass: Doing something.";
         }
      }

      class ExtendedBehavior {
         private $finalClass;

         public function __construct(FinalClass $finalClass) {
            $this->finalClass = $finalClass;
         }

         public function doSomethingElse() {
            return "ExtendedBehavior: Adding new functionality.";
         }

         public function doSomething() {
            // Call the original method and extend its behavior
            return $this->finalClass->doSomething() . $this->doSomethingElse();
         }
      }

      // Usage
      $final = new FinalClass();
      $extended = new ExtendedBehavior($final);
      echo $extended->doSomething();        // "FinalClass: Doing something. ExtendedBehavior: Enhancing it."
      echo $extended->doSomethingElse();    // "ExtendedBehavior: Adding new functionality."
      ```
   - Static Methods (Partial Behavior Extension)
      - If the final class contains static methods, you can create a new class with static methods that call and extend the behavior of the final class's static methods.
      - 
      ```php
      final class FinalClass {
         public static function staticMethod() {
            return "FinalClass: Static method executed.";
         }
      }

      class StaticBehaviorExtender {
         public static function staticMethod() {
            return FinalClass::staticMethod() . " Extended static behavior.";
         }
      }

      // Usage
      echo StaticBehaviorExtender::staticMethod(); // "FinalClass: Static method executed. Extended static behavior."
      ```
- What is Dependency Injection in PHP?
   - Dependency Injection (DI) is a design pattern where an object receives its dependencies (other objects or services) from an external source rather than creating them internally. This promotes loose coupling, improves testability, and simplifies the management of dependencies.
   - 
   ```php
   class DatabaseConnection {
      private $dsn;

      public function __construct($dsn) {
         $this->dsn = $dsn;
      }

      public function connect() {
         return "Connected to {$this->dsn}";
      }
   }

   class UserRepository {
      private $dbConnection;

      public function __construct(DatabaseConnection $dbConnection) {
         $this->dbConnection = $dbConnection;
      }

      public function getUser() {
         return $this->dbConnection->connect() . " - Fetching user data.";
      }
   }

   // Usage
   $dbConnection = new DatabaseConnection("mysql:host=localhost;dbname=test");
   $userRepo = new UserRepository($dbConnection);
   echo $userRepo->getUser();
   ```
- How to access private properties in php?
   - Access via Getter Method (Recommended Approach)
   - Access via Reflection (Not Recommended for Regular Use)
      - 
      ```php
      class MyClass {
         private $property = "Private Value";
      }

      $instance = new MyClass();
      $reflection = new ReflectionClass($instance);

      // Access private property
      $property = $reflection->getProperty('property');
      $property->setAccessible(true); // Make it accessible
      echo $property->getValue($instance); // Output: Private Value
      ```
   - Access via Magic Methods (__get and __set)
   - Access via Serialization (Hacky and Not Recommended)
   - 

---------------------------------------------------------------------------------------------------------------------
### Security techniques
---------------------------------------------------------------------------------------------------------------------
- https://docs.php.earth/security/intro/
- Security related settings
   - disable_functions: This directive disables certain PHP functions that could pose security risks, such as exec, system, passthru, shell_exec, and eval. By disabling these functions, you can prevent attackers from executing arbitrary shell commands or evaluating arbitrary PHP code on the server.
      - disable_functions = exec, system, passthru, shell_exec, eval
   - open_basedir: This directive restricts PHP scripts to accessing only files within specified directory trees. It helps prevent PHP scripts from accessing sensitive system files outside of the specified directory boundaries.
      - open_basedir = /var/www/html:/tmp
   - expose_php: This directive controls whether PHP exposes its version information in HTTP response headers. It's a good security practice to disable PHP version information to prevent potential attackers from identifying vulnerabilities specific to a particular PHP version.
      - expose_php = Off
   - display_errors: This directive controls whether PHP errors are displayed to the user. In a production environment, it's recommended to turn off error display to prevent sensitive information from being exposed to potential attackers.
      - display_errors = Off
   - allow_url_fopen and allow_url_include: These directives control whether PHP can open remote URLs using functions like file_get_contents() and include(). Disabling these functions can help prevent remote file inclusion (RFI) attacks.
      - allow_url_fopen = Off
      - allow_url_include = Off
      
- Use authentication and role-based access control (RBAC).
- Sanitize all user submitted data (user addslashes, remove stripslashes etc)
- Use uuid (universally unique identifier) instead of id whenever possible, so attacker can’t change it to perform any operations for other records
- While performing any operation, double check authenticated users for that particular record. For e.g in case of delete, check whether the user has right access to delete such records or not (only the creator of the records or admin can perform such [delete][update] operations).
- Use parameterized query in database
- Apply strong rule for password, hash it etc. Apply rule like no directory world will be getting used in password. use password like P@ssW0rd!123
- Use one way hashing for password
- Apply max login fail rule and block user for particular time (In case of emergency or for real user - Admin / Super Admin can handle such situation then after)
- Apply captcha whenever possible (particularly in registration form)

- directory traversal attacks in file uploads
   - Sanitize File Names Ensure that the file names provided by users do not contain harmful characters such as ../, \, or / that would allow path traversal.
   - Use Absolute Paths for File Storage Always store uploaded files in a specific directory that is within a controlled location (e.g., /uploads/) and avoid directly using user-supplied file paths.
      - 
      ```php
      $uploadDir = '/path/to/your/uploads/';
      $filePath = $uploadDir . basename($_FILES['uploaded_file']['name']);
      move_uploaded_file($_FILES['uploaded_file']['tmp_name'], $filePath);
      ```
   - Limit the File Path to a Specific Directory Always validate that the destination file path is within the intended directory using functions like realpath() or dirname() to avoid any path traversal.
   - Limit File Uploads to Specific Types and Sizes Restrict the file types that can be uploaded to avoid dangerous files. Use mime_content_type() or finfo_file() to check the MIME type, and ensure that only valid files are allowed.
   - Check File Permissions After the file upload, ensure the uploaded file is only readable and not executable to mitigate the risk of executing uploaded scripts or malicious files.
   - Avoid Using User Input for File Paths Never use raw user input (e.g., file names, directory names) to directly access or manipulate file paths without proper validation and sanitization.
   - Regularly Monitor and Audit File Uploads Implement logging mechanisms to track all file uploads and check for suspicious behavior (e.g., files with multiple ../ in their names).

- What is CSRF token, how to enable it and in which scenario we should disable it.
   - CSRF (Cross-Site Request Forgery) is a type of attack that tricks a user into performing actions on a website without their consent.
   - A CSRF token is a security measure used to prevent this type of attack. It is a unique, secret token that is sent along with the request, and it verifies that the request was made by the user who initiated the action. This token ensures that the user is submitting the request from the correct form, and not from an external site that could be controlled by an attacker.
   - How CSRF Tokens Work
      - Generate CSRF Token:
         - When the user loads a page (e.g., a form submission page), the server generates a unique CSRF token and embeds it in the form (as a hidden field).
         - 
         ```php
         session_start(); // Start the session to store the token

         // Generate a random token (e.g., using bin2hex(random_bytes()))
         $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
         ```
      - User Submits the Form:
         - When the user submits the form, the CSRF token is sent along with the request to the server.
         - 
         ```php
         echo '<form method="POST" action="submit.php">';
         echo '<input type="hidden" name="csrf_token" value="' . $_SESSION['csrf_token'] . '">';
         echo '<input type="text" name="username">';
         echo '<input type="submit" value="Submit">';
         echo '</form>';
         ```
      - Server Validation:
         - The server checks if the CSRF token in the request matches the token stored in the user's session or a secure cookie.
         - If the token is valid, the server processes the request.
         - If the token is invalid or missing, the server denies the request as a potential CSRF attack. 
         - 
         ```php
         session_start(); // Start the session to access the token

         if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
               // Invalid token, possible CSRF attack
               die('CSRF validation failed');
            }

            // Proceed with processing the form data
            echo 'Form submitted successfully!';
         }
         ```
   - When to Disable CSRF Protection
      - If the action is purely read-only (e.g., viewing a page) and does not modify any data or perform an action (e.g., GET requests), CSRF protection is not needed.
      - CSRF protection is not necessary for API requests if you are using token-based authentication (e.g., JWT (JSON Web Token)) because:
         - The token is sent explicitly in the request header (usually as Authorization: Bearer <token>), which cannot be automatically submitted by malicious sites.
         - API requests are typically stateless, and the CSRF vulnerability is mostly for stateful sessions managed by cookies.
         - If you are using cross-domain POST requests (e.g., through CORS (Cross-Origin Resource Sharing)), CSRF protection may not be necessary. However, ensure that CORS headers are correctly configured to restrict requests from untrusted origins.
         - For SPAs, you may disable CSRF protection if you use JWT or other methods like OAuth2 for authentication, as these do not rely on cookies to maintain sessions.

- What is Cross-Site Scripting (XSS), and how can you prevent it?
   - Use htmlspecialchars() or htmlentities() to escape output:
   - Validate and sanitize input data.
   - Implement Content Security Policy (CSP) headers
      - header("Content-Security-Policy: default-src 'self'; script-src 'self' https://cdn.example.com; style-src 'self'");
      - <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' https://cdn.example.com; style-src 'self';">

- How do you secure API endpoints in PHP?
   - Use authentication mechanisms (e.g., API keys, OAuth tokens).
   - Validate and sanitize input.
   - Use HTTPS to encrypt data in transit.
   - Implement rate limiting to prevent abuse.

- How can you prevent session hijacking in PHP?
   - Use session_regenerate_id() to regenerate session IDs regularly.
   - Enable session.use_strict_mode to accept only server-generated session IDs.
   - Use the HttpOnly and Secure flags for cookies:
      - 
      ```php
      session_set_cookie_params(['secure' => true, 'httponly' => true, 'samesite' => 'Strict']);
      ```
- 

---------------------------------------------------------------------------------------------------------------------
### Performance Improvement techniques
---------------------------------------------------------------------------------------------------------------------
- Performance Improvement techniques

   - Use Opcode Caching: Enable an opcode cache like APCu, OPcache, or XCache to store precompiled PHP code in memory, reducing the need to recompile scripts on each request.

   - Minimize File Includes: Reduce the number of file includes and require statements in your code, as file I/O operations can be costly. Consider using autoloading mechanisms like PSR-4 to autoload classes efficiently.

   - Optimize Database Queries: Use indexes, limit the number of returned rows, and optimize complex queries to minimize database load. Consider using tools like EXPLAIN in MySQL to analyze query performance.

   - Reduce HTTP Requests: Minimize the number of HTTP requests by combining CSS and JavaScript files, using image sprites, and employing techniques like lazy loading for images.

   - Cache Data: Implement caching mechanisms like Memcached or Redis to store frequently accessed data in memory, reducing the need to regenerate content on each request.

   - Optimize Images: Compress and resize images to reduce file size and improve page load times. Consider using image optimization tools or CDNs to serve images efficiently.

   - Enable Gzip Compression: Enable Gzip compression on your web server to compress HTML, CSS, and JavaScript files before sending them to the client, reducing bandwidth usage and improving page load times.

   - Use Efficient PHP Functions: Use built-in PHP functions and language constructs where possible, as they are usually more optimized than custom implementations. Avoid using slow functions like file_get_contents() when more efficient alternatives like fopen() and fread() are available.

   - Reduce Session Usage: Minimize the use of PHP sessions or consider using alternative session storage mechanisms like database-backed sessions or session-less authentication methods where appropriate.

   - Optimize CSS and JavaScript: Minify CSS and JavaScript files to reduce file size and improve loading times. Remove unnecessary whitespace, comments, and redundant code to optimize performance.

   - Enable HTTP/2: If your server and client support it, enable HTTP/2 to take advantage of features like multiplexing, header compression, and server push, which can significantly improve page load times.

   - Use Content Delivery Networks (CDNs): Offload static assets like images, CSS, and JavaScript files to CDNs to distribute content closer to users, reducing latency and improving load times.

   - Profile and Benchmark: Use profiling tools like Xdebug or Blackfire.io to identify performance bottlenecks in your code. Benchmark different parts of your application to measure improvements accurately.

   - Optimize Server Configuration: Fine-tune your web server configuration (e.g., Apache, Nginx) and PHP configuration settings (e.g., memory_limit, max_execution_time) to match the requirements of your application and server hardware.

- What is OPcache, and how does it improve performance?
   - OPcache is a PHP extension that improves performance by caching precompiled script bytecode in memory, reducing the need for repeated parsing and compilation.
   - To enable OPcache:
   - 
   ```php
   opcache.enable=1
   opcache.memory_consumption=128
   opcache.max_accelerated_files=10000
   ```
- What is the difference between file-based caching and memory-based caching?
   - File-Based Caching: Stores cache data on disk. Slower than memory-based caching due to disk I/O but persistent across application restarts.
   - Memory-Based Caching: Stores cache in memory (e.g., Memcached, Redis). Faster but non-persistent unless explicitly saved.
- What is the purpose of a CDN, and how does it improve performance?
   - A Content Delivery Network (CDN) delivers static content (e.g., images, JavaScript, CSS) from servers geographically closer to the user.
   - It reduces server load, latency, and bandwidth usage, speeding up content delivery.
- How do lazy loading and eager loading affect performance in PHP?
   - Lazy Loading: Loads resources (e.g., related database data) only when needed. Reduces initial load time but may cause additional queries later.
   - Eager Loading: Preloads related data upfront, reducing the number of queries.

---------------------------------------------------------------------------------------------------------------------
### Error handling Qs
---------------------------------------------------------------------------------------------------------------------
- Header already sent error?
   - 
   ```php
   // Incorrect
   // Error: Headers already sent
   echo "Hello";
   header("Location: index.php"); 
   ```
   - How to Fix "Headers Already Sent" Error
      - Remove any whitespace or blank lines before <?php or after ?>.
      - Best Practice: Omit closing PHP tags (?>) in pure PHP files to avoid trailing whitespace.
      - Use output buffering to capture and control output before sending headers.
         - 
         ```php
         ob_start();
         echo "This output won't cause an error";
         header("Location: index.php");
         ob_end_flush();
         ```
      - Debug Output Locations by using debug_backtrace();
      - Enable Error Reporting
         - ini_set('display_errors', 1);
         - error_reporting(E_ALL);   

- What are the different types of errors in PHP?
   - Parse Errors: Occur when PHP cannot interpret the code syntax. Example: Missing semicolon.
   - Fatal Errors: Caused by operations that cannot be completed. Example: Calling a non-existent function.
   - Warning Errors: Non-fatal errors; the script continues execution. Example: Including a non-existent file.
   - Notice Errors: Minor issues; the script runs as expected. Example: Accessing undefined variables.
   - Deprecated Errors: Warning that a feature or function will be removed in future PHP versions.
- How can you handle errors in PHP?
   - Error Reporting: Configure which errors PHP should report using error_reporting() or php.ini. error_reporting(E_ALL);
   - Error Logging: Log errors to a file using error_log("Error message", 3, "/path/to/log/file.log");
- What is the difference between an exception and an error in PHP?
   - Errors:
      - Represent system-level issues (e.g., syntax errors, memory limits).
      - Cannot always be caught or handled by the application.
   - Exceptions: 
      - Represent application-level issues (e.g., invalid input, failed database queries).
      - Can be caught and handled using try-catch.
- How can you enable and disable error reporting in PHP?
   - ini_set('display_errors', 1);
   - ini_set('display_startup_errors', 1);
   - error_reporting(E_ALL);
   - disable error reporting..
   - ini_set('display_errors', 0);
   - error_reporting(0);
- How do you log errors in PHP?
   - php.ini Settings
      - log_errors = On
      - error_log = "/path/to/error.log"
- How to get the last error?
   - error_get_last() retrieves the last error that occurred in the script.
- How can you suppress errors in PHP?
   - Use the @ operator to suppress errors

- Disadvantages of Persistent Connections 
   - Persistent connections remain open and can lock server-side resources (e.g., database connections), even when no active request is using them. This can lead to resource exhaustion, especially on servers with limited connection pools.
   - If too many persistent connections are opened but not actively used, they consume server memory and other resources unnecessarily.
   - If the application is highly concurrent, multiple requests may compete for the same persistent connections, leading to contention or failures when the connection pool is exhausted.
   - Persistent connections can make debugging connection-related issues more complex, as the same connection may be shared across multiple requests, making it difficult to isolate problems.
   - When to Avoid Persistent Connections
      - High-concurrency applications with limited database connections.
      - Applications where transactions, temporary tables, or session-based data are used heavily.
      - Short-lived scripts where connection overhead is negligible.
   - Best Practices
      - Use Persistent Connections Judiciously: Enable them only if the application has a high number of requests needing database access and is properly designed to handle shared connection states.
      - Close Resources Explicitly: Ensure all open transactions or temporary data are cleared after use.
      - Monitor Connection Pooling: Use tools to monitor the database for connection pool exhaustion and resource usage.
      - Combine with Connection Managers: Tools like Doctrine or frameworks with ORM support can handle connection pooling better.

---------------------------------------------------------------------------------------------------------------------
### Other Qs
---------------------------------------------------------------------------------------------------------------------
- Why it is best practice to omit closing PHP tags (?>) in pure PHP files?
   - to avoid trailing whitespace at the end so it prevent "Header Already Sent" Error.

- What is the use of PHP-FPM
   - PHP-FPM (PHP FastCGI Process Manager) is an alternative PHP implementation designed to improve the performance, scalability, and manageability of PHP applications in high-traffic environments. It is widely used in modern web server setups to efficiently handle PHP requests.
   - The main configuration file for PHP-FPM is typically located at /etc/php/8.x/fpm/php-fpm.conf or /etc/php/8.x/fpm/pool.d/.
   ```php
   [www]
   user = www-data
   group = www-data

   listen = /run/php/php8.1-fpm.sock
   listen.owner = www-data
   listen.group = www-data
   # PHP-FPM Settings
   pm = dynamic
   pm.max_children = 50
   pm.start_servers = 5
   pm.min_spare_servers = 5
   pm.max_spare_servers = 10

   request_terminate_timeout = 30
   slowlog = /var/log/php-fpm/slow.log
   ```
   - How PHP-FPM Works
      - PHP-FPM operates as a FastCGI server, listening for incoming requests from a web server (like Nginx or Apache). When a request is received:
      - The web server forwards the request to PHP-FPM.
      - PHP-FPM assigns the request to one of its worker processes.
      - The worker processes execute the PHP code and return the response to the web server.
      - The web server delivers the response to the client.
      - Key Features of PHP-FPM
      - Process Management:
         - Efficiently handles multiple PHP processes.
         - Includes advanced process management capabilities like spawning, killing, and recycling processes.
      - Improved Performance:
         - Optimized to handle a large number of concurrent PHP requests.
         - Reduces the overhead of starting and stopping PHP processes for each request.
      - Separate Pools:
         - Allows configuration of multiple process pools, each with its own settings.
         - Useful for hosting multiple applications with varying resource requirements.
      - Security:
         - Supports chroot and process isolation for better security in multi-tenant environments.
         - Allows running each pool under a different user to isolate applications.
      - Adaptive Process Management:
         - Dynamically adjusts the number of processes based on server load and demand.
      - Error Logging:
         - Provides enhanced logging capabilities, including slow log analysis to debug performance bottlenecks.
      - Customizable Settings:
         - Highly configurable with settings for request timeouts, memory limits, and more.
   - Benefits of PHP-FPM
      - Performance Optimization:
         - Reduces latency and CPU usage compared to traditional CGI or mod_php setups.
      - Scalability:
         - Handles a large number of simultaneous requests efficiently.
         - Suitable for high-traffic applications and complex architectures.
      - Resource Management:
         - Configurable to allocate server resources based on application needs.
      - Isolation:
         - Isolates different applications running on the same server, improving security and stability.
      - Integration:
         - Works seamlessly with popular web servers like Nginx, Apache, and Lighttpd.
   - Common Use Cases
      - High-Traffic Websites:
      - Handles large numbers of concurrent users efficiently.
      - Load Balancing:
      - Works well in clustered environments with multiple PHP-FPM instances.
      - Multi-Tenant Applications:
      - Provides process isolation and user-based permissions for shared hosting.
   - Summary
      - PHP-FPM is a powerful, efficient, and scalable tool for managing PHP execution in modern web environments. It is ideal for high-performance applications, offering significant advantages in performance, process management, and resource utilization over traditional CGI or mod_php setups. Its flexibility and integration capabilities make it a popular choice for developers and system administrators alike.

