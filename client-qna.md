------------------------------------------------------------------------
### Money Forward India SSE 
------------------------------------------------------------------------
- Q1. Can you describe a situation where you had to design a RESTful API for a Ruby on Rails application? How did you ensure it was scalable and maintainable?
    - Planning the API Endpoints
        - Example:
            - GET /products (list products)
            - POST /orders (create an order)
    - Implemented API Versioning (gem versionist)
    - Optimized for Performance
        - Database Optimization
            - Indexes, Eager loding, Query cache
        - Caching
            - Redis or other cache mechanism
        - Records limits with Pagination, Lazy Loading, Eager Loading
    - Apply Security best practices
        - Authentication and Authorization
    - Scalable Design Patterns
        - Background Job
        - Rate limit, Throtteling
    - Error Handling
    - Logging and Monitoring
    - Documented the API using Swagger via the rswag gem
    - Maintain backward compatibility with any version upgradation 

- Q2. Tell us about a time you optimized a slow-running Ruby on Rails application. What strategies did you use to improve performance?
    - Database Query Optimization
        - Review slow query log, apply missing indexes, rewrite SQLs, Solve N+1 query 
    - Caching
    - Background Jobs
    - Static content management (Asset Management)
    - Database connection Pooling
    - Logging and Monitoring

- Q3. Describe an instance where you had to handle complex data relationships in Ruby on Rails. How did you design the models and manage database interactions?
    - Complex DB Design, Multiple Table associations
    - Use Indexes, Polymorphic Associations 
    - Use Cache for not so frequently updated data
    
- Q4. Have you worked with versioning in RESTful APIs? Can you provide an example where versioning was necessary, and how you implemented it in a Ruby on Rails application?
    - Versioning is crucial for maintaining backward compatibility when making changes to an API, such as modifying request or response formats, adding or removing fields, or deprecating end-points. It allows you to release new features without disrupting existing clients using the older API version.
    - Route-based Versioning
    - Controller Organization
        - Each API version has its own controllers, allowing you to separate logic
    - Header-based Versioning
        - Alternatively, you can version APIs based on custom headers

- Q5. Can you share a scenario where you encountered a difficult bug in a Ruby on Rails API? How did you approach debugging and resolving the issue?
    - Intermittent 500 Errors in a Ruby on Rails API
        - Log Analysis and Monitoring
        - Reproduced the issue
        - Validate Request / Response Payload
        - Perform Unit testing to reproduced the bug
        - Improve Error / Logs messages

------------------------------------------------------------------------
### Money Forward India TL
------------------------------------------------------------------------
- Q1. What are the key differences between RESTful and GraphQL APIs? In what scenarios would you choose one over the other for a SaaS platform?

![alt text](<images/restful-vs-graphql.png>)

- Q2. Imagine a scenario where the backend services experience a performance bottleneck during peak traffic. How would you identify and resolve the issue? Provide a step-by-step plan for optimization.
    - Log analysis
    - Monitor Metrics
    - Perform Unit testing
    - Load testing
    - Debugging and Identify root cause 
        - Application / code level of analysis
        - SQL analysis - slow query check, missing indexes etc.
        - Cache
        - Http calls 
        - Scaling

- Q3. Write a Ruby method that takes an array of integers and returns the maximum sum of any contiguous subarray (Kadane's Algorithm). Include tests to validate your solution.

- Q4. You are tasked with migrating a legacy monolithic Ruby on Rails application to a microservices architecture. What steps would you take to ensure a smooth transition? Highlight potential challenges and how you would address them.
    - Analysis and Plannig for execution
        - Understand the existing application
        - Identify loosely coupled modular, independent services
        - Define the scope, identify challenges, Documentations, Create execution plan for the define services
    - Gradually replace parts of the monolith with microservices (Strangler-Fig Plan)
    - Database decoupling 
    - Interservice dependencies and communication channel analysis
    - Logs, Monitor and exception handling plan for define microservices
    - TDD/BDD
    - Keep old and new services for Time being and gradually resplace it with deployment strategy like canary release, feature flag release, Blue-Green etc

- Q5. As a team leader, how would you handle a situation where one of your team members is consistently missing deadlines, impacting the overall delivery? Provide an example of how you have successfully managed a similar scenario in the past.
    - Identify root cause
        - Personal issue, work load,  lack of clarity, skill gap
    - Provide support and set the clear expectations
    - Track the progress, review and monitor the tasks, 1-1 meeting and provide the feedback
    - If not working then PIP plan, raise the concern with HR and other higher authority and create a resourse backup plan
    
------------------------------------------------------------------------
### Moolya Software Testing Pvt Ltd SSE
------------------------------------------------------------------------
- Q1. Can you explain how Rails' Model-View-Controller (MVC) architecture works and how data flows through an application when a user makes a request?
    - a. A web server (e.g., Puma, Unicorn) listens for incoming HTTP requests and forwards them to the Rails application
    - b. The request passes through a stack of middleware. Middleware are lightweight components that can modify the request or response. Examples include session management, logging, and request parsing
    - 1. Request:
        - A user accesses http://localhost/articles.
    - 2. Route:
        - Rails maps GET /articles to ArticlesController#index.
    - 3. Controller:
        - ArticlesController calls Articles.all to fetch all articles.
    - 4. Model:
        - The article Model executes a database query to retrieve article records.
    - 5. View:
        - The index.html.erb template iterates through @articles and displays them in HTML.
    - 6. Response:
        - The HTML is sent back to the user and rendered in their browser.

- Q2. What are some key principles for designing a RESTful API, and can you give an example of a good and bad URL design for a resource?
    - Key Principles for Designing a RESTful API
        - Use Meaningful Resource Names
            - Resources should be nouns and pluralized when appropriate.
            - Avoid using verbs in endpoint paths.
        - Follow HTTP Method Semantics
            - Use HTTP methods like GET, POST, PUT, PATCH, and DELETE appropriately
        - Keep file, controllers, model, table naming conventions and keep consistency
        - API should be stateless
        - API should follow single responsibility principle, it should be loosely coupled and highly cohesive in nature
        - Maintain API versioning
        - Logging and Monitoring
        - Error Handling
        - Request / Response Payload format
        - Status Code
        - Provide HATEOAS (Hypermedia as the Engine of Application State)
        - Secure the API
        - Documentation - use tools like, Swagger/OpenAPI
        - Set the Rate Limit to prevent abusing usage
        - Apply Cache for API
        - Good URL Design
            ```ruby
            GET /api/v1/orders
            GET /api/v1/orders/123
            POST /api/v1/orders
            PATCH /api/v1/orders/123
            DELETE /api/v1/orders/123

            # config/routes.rb
            namespace :api do
                namespace :v1 do
                    resources :orders
                end
            end
            ```
        - Bad URL Design
            ```ruby
            GET /getOrders
            POST /createOrder
            DELETE /deleteOrder?id=123
            GET /api/orders/getActive
            ```
- Q3. Can you explain the difference between TDD (Test-Driven Development) and BDD (Behavior-Driven Development), and when would you use one over the other in a backend project?
    - TDD is a software development approach where tests are written before writing the actual code. The focus is on the correctness of functionality
        - Focus: Ensures individual units of code work correctly (unit testing).
        - Code-focused (assertions, methods).
        - Micro-level (unit testing)
        - Tools: RSpec (for unit tests), MiniTest in Ruby on Rails
    - BDD builds on TDD but focuses on the behavior of the application from the user perspec-tive. Tests are written in a natural language style
        - Focus: Validates that the application behaves as expected (integration and acceptance test-ing).
        - Natural language (Given-When-Then).
        - Macro-level (integration and acceptance testing).
        - Tools: RSpec (for writing specs), Cucumber, Capybara

- Q4. When working with a relational database like MySQL, how do you ensure efficient querying and what are some ways to optimize performance when dealing with large datasets?
    - Query Optimization
        - Indexes, Proper References, Cluster / NonCluster Indexes, Composite Indexes
        - Explain Query
        - Slow Query Log
        - Avoid N+1 Query
        - Cache Query
    - Schema Design
        - Normalization
        - DeNormalization
        - Partitioning
        - Proper Data Types
        - Maintain References
    - Query execution
        - Batch Processing
        - Avoid Complex Joins
        - Caching
    - DB Configuration
        - Connection Pool
        - Buffer Settings
        - Tuning Params
    - Monitor and Maintenance
        - Slow Query Log
        - Performance Analysis
        - Bench Marking

    - Indexing: Create indexes on frequently searched columns to speed up query times. Rails supports adding indexes directly through migrations.
    - Caching: Use Rails caching mechanisms, like page, action, or fragment caching, to reduce database load by storing and serving repeated requests.
    - Read replicas: Deploy read replicas of the database to distribute read queries, thus reducing the load on the primary database.
    - Database partitioning: Partition large tables into smaller, more manageable pieces to improve query performance and maintenance.
    - Background jobs: Move heavy computations or non-critical database operations to background jobs using Sidekiq or Delayed Job, to keep the web request cycle as fast as possible.
    - Connection pooling: Manage database connections efficiently to reduce the overhead of establishing connections frequently.
    - Monitoring and tuning: Regularly monitor database performance using tools like New Relic or pgBadger, and tune queries and indexes based on insights.
    - Selective loading: Use select, joins, and includes wisely to load only the necessary data, avoiding N+1 queries problem.

- Q5. How do you resolve conflicts in Git, and what steps would you take if you and a teammate both modify the same line of code in a file?
    - Check the Conflict
    - Analyze previous and current code logic
    - Identiy which we should keep, incase of any confusion, connect with dev and merge the changes
    - git add . and then apply proper conflict resolve message for future reference
    - Verify changes and communicate other devs to verify their features too
    - Avoid conflicts by
        - Use proper branching
        - Always Pull the latest code, before start working or push it into the remore repo and fix in case of local conflicts
-----------------------------------------------------------------------
### Synechron
------------------------------------------------------------------------
- Q1. Write a Ruby on Rails method to handle a common REST API endpoint. Suppose you are given a GET /api/orders/:id endpoint that retrieves order details for a specific order. Implement the Rails controller method to handle this endpoint. The order should include details about items, pricing, and the user associated with the order. Consider using eager loading to optimize for performance.
    ```ruby
    module Api
        class OrdersController < ApplicationController
            # GET /api/orders/:id
            def show
            # Find the order by its ID
            order = Order.find_by(id: params[:id])

            if order
                # Return the order details as JSON
                render json: { status: "success", data: order }, status: :ok
            else
                # Handle case where order is not found
                render json: { status: "error", message: "Order not found" }, status: :not_found
            end
            rescue StandardError => e
            # Handle any unexpected errors
            render json: { status: "error", message: e.message }, status: :internal_server_error
            end
        end
    end

    # config/routes.rb
    namespace :api do
        resources :orders, only: [:show]
    end

    # Testing
    curl -X GET http://localhost:3000/api/orders/1

    ```

- Q2. Explain how you manage state between React components in a large-scale application. How do you handle state when passing data from a parent component to deeply nested child components?
    - Passing State Through Props
    - React Context API
    - Consume Context in Child Components
    - Custom Hooks
    - Component Composition
    - For large-scale applications with complex or global state needs, libraries like Redux, Zustand, or MobX can help.

- Q3. Describe a time when you collaborated with developers to bring a design to life. How did you handle any discrepancies between the initial design and the final implementation? What tools or methods did you use to ensure accurate design implementation?

- Q4. The tech landscape is constantly evolving, with new design trends and technologies emerging frequently. How do you stay updated on emerging design trends, and can you provide an example of a recent design technology or trend that you have successfully implemented in a project?

- Q5. You are tasked with conducting a usability evaluation for a newly implemented feature. Describe the process you would follow, the types of data you would collect, and how you would use this information to iterate on the design.
------------------------------------------------------------------------
### Nous Infosystems
------------------------------------------------------------------------
- Q1. Describe your recent experience with similar projects

- Q2. In a high-traffic application using SQL Server (or similar RDBMS), what specific strategies would you use to improve database performance? Describe your approach to indexing, caching, and optimizing complex queries. How would you handle database scaling to ensure continued performance as data grows?

- Q3. Imagine you are leading the architecture for a customer-facing e-commerce application that experiences high traffic during peak sale events. Midway through a major sale, you notice the system is slowing down due to high database load and increased API response times. How would you identify and address the root cause of these performance issues? What changes would you consider in the architecture to handle such high traffic events more smoothly in the future?

- Q4. Imagine you’re working on a Ruby on Rails application that is experiencing rapid user growth, and you’re beginning to encounter performance bottlenecks, particularly with response times and database queries. How would you approach identifying and optimizing performance issues within a Rails application? What specific Rails tools, techniques, or design patterns would you apply to handle high traffic, optimize database queries, and manage caching?

- Q5. You're tasked with creating a Rails API endpoint that accepts a list of order IDs and marks each order as "processed." Each order should only be processed if it hasn’t already been marked as such. Write a Rails controller action in Ruby that: Accepts a JSON array of order_ids. For each order_id, checks if the order is already marked as "processed" in the database. If not, marks it as "processed." Ensure that your code is efficient, handling cases where there might be a large number of order_ids submitted at once.