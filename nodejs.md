- import Vs require
  - In Node.js, both import and require are used to load modules, but they come from different module systems and have distinct use cases. Here's a detailed comparison:
  - require:
    - Module System: CommonJS (CJS)
    - Purpose: Used in older versions of Node.js or when working with CommonJS modules.
    - Synchronous: require is synchronous; it blocks execution until the module is fully loaded.
    - Dynamic Loading: You can call require at runtime, allowing conditional imports.
    - File Types: Can import .js, .json, and .node files.
    - Caching: Modules are cached after the first load, improving performance on subsequent calls.
    - Backward Compatibility: Works in all versions of Node.js.
  - import
    - Module System: ECMAScript Modules (ESM)
    - Purpose: Used in modern JavaScript, especially when working with ES Modules.
    - Asynchronous: import is asynchronous and does not block execution.
    - Static Loading: Modules are loaded at the start of the program; dynamic imports require the import() function.
    - File Types: Can import .js and .mjs files.
    - Tree Shaking: Allows unused code to be removed during the build process, improving performance.
    - Modern Syntax: Compatible with tools like Babel or TypeScript.
    - Experimental in Older Node.js: Fully supported since Node.js 12+ with the type: "module" configuration in package.json.
  - ![alt text](<images/include-vs-require-nodejs.png>)

- If Node.js is single threaded then how does it handle concurrency?
  - Node.js is indeed single-threaded, meaning it operates within a single main thread in the event loop. However, it's important to understand that Node.js leverages non-blocking I/O operations to handle concurrency efficiently.
  - Here's how Node.js achieves concurrency despite being single-threaded:
    - Event Loop: Node.js uses an event-driven architecture with an event loop. The event loop continuously checks the event queue for any new events or tasks to execute. When an event occurs, such as an I/O operation completing or a timer expiring, Node.js processes the corresponding event handler.
    - Non-blocking I/O: Node.js utilizes non-blocking, asynchronous I/O operations. When Node.js performs an I/O operation, such as reading from a file or making an HTTP request, it doesn't wait for the operation to complete before moving on to the next task. Instead, it initiates the operation and continues executing other tasks. When the operation completes, Node.js processes the result through a callback or other asynchronous mechanism.
    - Libuv: Node.js relies on the Libuv library to handle asynchronous I/O operations and manage the event loop. Libuv provides a platform-independent abstraction layer for asynchronous I/O operations, including mechanisms for handling events, timers, networking, and file system operations. It employs a thread pool to handle certain blocking operations asynchronously, further improving concurrency.
    - Worker Threads: Node.js also offers the ability to create worker threads using the worker_threads module. These worker threads allow developers to execute JavaScript code in parallel, leveraging multiple CPU cores for CPU-intensive tasks. Although worker threads operate separately from the main event loop, they can communicate with the main thread using messaging.

- How does concurrency work in NodeJS? Please explain the use of Event Queue and Event Loop. 
  - Concurrency in Node.js is achieved through its event-driven, non-blocking I/O model. At the core of this model are the Event Queue and the Event Loop.
  - Event Queue:
    - The Event Queue is a data structure that holds events and tasks that are ready to be processed.
    - Whenever an asynchronous operation is initiated (such as I/O operations, timers, or network requests), Node.js does not wait for the operation to complete. Instead, it places the associated callback function in the Event Queue and continues executing other tasks.
    - Each event in the Event Queue has a callback function associated with it, which represents the action to be taken when the event is processed.
    - The Event Queue follows a first-in, first-out (FIFO) order, meaning the events are processed in the order they were added.
  - Event Loop:
    - The Event Loop is a mechanism that continuously checks the Event Queue for events or tasks that are ready to be processed.
    - It runs in a single-threaded manner, meaning there's only one main thread handling all incoming requests and executing code.
    - The Event Loop consists of several phases, each responsible for different types of tasks such as timers, I/O operations, or immediate callbacks.
    - When the Event Loop encounters an event in the Event Queue, it retrieves the associated callback function and executes it.
    - After executing a callback function, the Event Loop moves on to the next event in the queue. If the queue is empty, the Event Loop waits for new events to be added.
  - Here's a simplified overview of how concurrency works in Node.js:
    - Node.js receives incoming requests, such as HTTP requests or file system operations.
    - It initiates asynchronous operations, such as reading from a file or making a network request, without blocking the main thread.
    - Instead of waiting for these operations to complete, Node.js registers callback functions to be executed once the operations finish.
    - Meanwhile, Node.js continues to process other tasks or handle new incoming requests.
    - When an asynchronous operation completes, its associated callback function is placed in the Event Queue.
    - The Event Loop continuously checks the Event Queue for new events.
    - When an event is ready to be processed, the Event Loop retrieves its callback function and executes it.
    - This process repeats, allowing Node.js to handle multiple concurrent operations efficiently without blocking the main thread.
  
  - What are streams in Node.js, and how can they be used to handle large datasets efficiently? Provide an example of using readable and writable streams in Node.js.
    - Streams in Node.js are a powerful feature for handling data efficiently, especially when dealing with large datasets. Streams provide an interface for reading from or writing to a continuous flow of data, allowing developers to process data in chunks rather than loading it all into memory at once. This can significantly reduce memory usage and improve performance, especially when dealing with large files or network data.

    - There are four main types of streams in Node.js:
    - Readable Streams: Used for reading data from a source.
    - Writable Streams: Used for writing data to a destination.
    - Duplex Streams: Streams that can both read from and write to the underlying data source.
    - Transform Streams: Special type of duplex stream that allows modifying or transforming data as it is being read from or written to.