Candidate Name - 17-01-2025 at 4:30pm 

Total exp - 10+ yrs
PHP - 
Other BE stack - 
Front-End: 
RBDMS - 
NoSQL - 
Docker / Kubernetes - 
Kafka / RabbitMQ - 
AWS serverless / base services? 
any application design / architect level of exp?
microservices?
CI/CD - 
Deployment strategy - 

Cache - 




Current Company - 
Current Role -
Current Project - 
Team Size - 
hands-on with coding - 


Q1. Can you describe a situation where you had to design a RESTful API in your project? How did you ensure it was scalable and maintainable?
api/v1/orders/1
Subdomain-based Versioning - e.g., v1.api.mysite.com

Q2. Have you worked with versioning in RESTful APIs? Can you provide an example where versioning was necessary, and how you implemented it in a PHP application?


Q3. Can you share a scenario where you encountered a difficult bug in  your project? How did you approach debugging and resolving the issue?


Q4. Imagine a scenario where the backend services experience a performance bottleneck during peak traffic. How would you identify and resolve the issue? Provide a step-by-step plan for optimization.


Q5. You are tasked with migrating a legacy monolithic php application to a microservices architecture. What steps would you take to ensure a smooth transition? Highlight potential challenges and how you would address them.


Q6. Tell us about a time you optimized a slow-running PHP application. What strategies did you use to improve performance?


Q7. Describe an instance where you had to handle complex data relationships in PHP. How did you design the models and manage database interactions?
- diff between has_many :through has_and_belongs_to_many associations 


Q8. As a team leader, how would you handle a situation where one of your team members is consistently missing deadlines, impacting the overall delivery? Provide an example of how you have successfully managed a similar scenario in the past.

Q9. 


# Finds the maximum subarray sum in a array of numbers
	  # Input: [-2, 1, -3, 4, -1, 2, 1, -5, 4]
	  # Output: 6 

	  # Input: [-2, 1, -3, 4, -1, 2, 1, -5, 8]
	  # Output: 9
  
OR

# Find the three element from an array to get the sum
	# 2 Input params =>  1. array = [ 2, 5, 9, 8, 12, 4 ] , 2. sum = 18
	# Output = 2, 12, 4

	# Input array = [1, 2, 4, 5, 6, 9, 10, 12, 3],  sum = 25
	# Output = 4, 9, 12




# Open/Closed Principle for diff types of log storage method 
# e.g Log File, Log MongoDB etc.
# Save Log details in User.save action
	# log details in file OR 
	# log details in MongoDB  

E.g 
class User 
  def save
   // log details (File or MongoDB)  
  end
end
    

# Find the three element from an array to get the sum
	# Input array = [ 2, 5, 9, 8, 12, 4 ] , sum = 18
	# Output = 2, 12, 4

	# Input array = [1, 2, 4, 5, 6, 9, 10, 12, 3],  sum = 25
	# Output = 4, 9, 12



Strengths: 


Weaknesses: 


Areas of Improvement: 


Conclusion: 

Status:  


- new in php 8
- sessions
- debugging
- performance and security
- oops
- db
- javascript
- nodejs
- rest api
- microservices
- docker
- git
- solid principle
- design patterns 
- .env vs config which one is faster

- session management
- validation 
- security
- debugging 
- use of rate limit to restrict api call
- middleware
- migration - 
- manage diff envs - via .env file .env.local | .env.prod
- DB associations
- CSRF token bt not for all pages, how to exclude 
	

- Laravel
 - lifecycle
 - Artisan in Laravel
 - Which ORM - Object Relational Mapping used by Laravel - Eloquent
 - Which pattern used by Eloquent - Active Record
 - What is Active Record Pattern 
 - Types of Associations In Laravel Eloquent
 - Laravel Service Providers
 - Laravel Service Containers
 - Middleware - Types of Middleware, Global, Group, Route
 - The throttle middleware limits the number of requests that a client can make to certain routes within a specified time interval, helping to prevent abuse and spam.

- SOLID principles with example 


-- MySQL
- indexes adv and disadv

- Performance improvement techniques
- cluster vs non-cluster indexes
- SQL analysis
- Join vs subquery
- SQl Vs NoSQL in which scenario we should go with either one


--JavaScript
- let vs var
- Hoisting 
- Coercion 
- is oops or not
- create object, is mutable or immutable
- event bubbling - stopPropagation
- closures
- function hoisting
- diff let and var - variable scope
- IIFE - Immediately invoked function execution
- console.log(typeof(null));


- array program - remove duplicate and count elements 



var pets = ['dog', 'chicken', 'cat', 'dog', 'chicken', 'chicken', 'rabbit'];

var petCounts = pets.reduce(function(obj, pet){
	if (!obj[pet]) {
    	obj[pet] = 1;
	} else {
    	obj[pet]++;
	}
	return obj;
}, {});

console.log(petCounts);

/*
Output:
 {
	dog: 2,
	chicken: 3,
	cat: 1,
	rabbit: 1
 }
