### PHP8 New 
```php

# Without Named Argument & Constructor Property Promotion

class ClassA {
    private string $prop1;
    private int $prop2;

    public function __construct(string $prop1, int $prop2) {
        $this->prop1 = $prop1;
        $this->prop2 = $prop2;
    }

    public function getData(): string {
        return "$this->prop1, $this->prop2";
    }
}

$a = new ClassA ('test', 123);
echo $a->getData();

# With  Named Argument & Constructor Property Promotion
class ClassB {
   
    public function __construct(
        private string $prop1,
        private int $prop2,
    ) {}

    public function getData(): string {
        return "$this->prop1, $this->prop2";
    }
}

$namedArgObj = new ClassB(prop1: 123, prop2: "prop1"); //invalid
$namedArgObj = new ClassB(prop2: 123, prop1: "prop1"); //valid
echo $namedArgObj->getData();

# Named Argument
class ClassA {
    public function __construct(
        private string $prop1,
        private int $prop2,
    ) {}

    public function getData(): string {
        return "$this->prop1, $this->prop2";
    }
}

# Without Named Argument
$a = new ClassA ('test', 123);
echo $a->getData();

# With Named Argument
$namedArgObj = new ClassA(prop1: 123, prop2: "prop1"); //invalid
$namedArgObj = new ClassA(prop2: 123, prop1: "prop1"); //valid
echo $namedArgObj->getData();

# -----------------------------
# Constructor Property Promotion

# Before PHP8
class User {
    private string $name;
    private int $age;

    public function __construct(string $name, int $age) {
        $this->name = $name;
        $this->age = $age;
    }
}


# PHP 8 (Constructor Promotion):
class User {
    public function __construct(
        private string $name,
        private int $age
    ) {}
}


```

### Code improvements
```php
class Test {
  
  public function isValidLimit($limit) {
    $maxLimit = $this->getMaxLimit();
    if ($limit <= $maxLimit) {
      return true;
    } else {
      return false;
    }
  }
  
  public function getMaxLimit() {
    return 50;
  }
  
  
}


$o = new Test();
$limitCheck = $o->isValidLimit(100);

if ($limitCheck) {
  echo "Within limit";
} else {
  echo "Limit exceeded";
}



class Test1
{
    public function isWithinLimit(int $limit): bool
    {
        return $limit <= $this->getMaxLimit();
    }

    private function getMaxLimit(): int
    {
        return 50;
    }
}

// Usage
$checker = new Test1();
echo $checker->isWithinLimit(50) ? 'Within limit' : 'Limit exceeded';
```

### 1 
```php
trait TraitA {
    public function getTrait() {
        return "getTrait A";
    }
}


trait TraitB {
    public function getTrait() {
        return "getTrait B";
    }
}


class ClassA {
    use TraitA, TraitB;
}

$obj = new ClassA();


echo $obj->getTrait(); // This will result in a "trait collision" error


# use instead of

trait TraitA {
    public function getTrait() {
        return "getTrait A";
    }
    
    public function getTraitA2() {
    	return "getTraitA2";
    }
}


trait TraitB {
    public function getTrait() {
        return "getTrait B";
    }
}


class ClassA {
    use TraitA, TraitB {
        TraitB::getTrait insteadof TraitA;
	TraitA::getTrait as getTraitA;
    }
    
}

$obj = new ClassA();


echo $obj->getTraitA(); // working calling TraitA method
echo $obj->getTrait(); // working calling TraitB method

```

### 2 static method
```php

class ClassA {
    public static function StaticMethod() {
        return "SMethod";
    }
}

$obj = new ClassA();
$obj->StaticMethod();

echo ClassA::StaticMethod(); //working

#4 not working

class ClassA {
    
    public $name;
    public static $name1 = 'name1';
    
    
    public function __construct() {
        $this->name = 'abc';
    }

    public static function staticMethod() {
        return $this->name . "SMethod";
    }
    
    
    public function notStaticMethod() {
        return  $this->name . ' notStaticMethod ';
    }
}


$obj = new ClassA();


echo $obj->staticMethod(); 

# working

class ClassA {
    
    public $name;
    public static $name1 = 'name1';
    
    
    public function __construct() {
        $this->name = 'abc';
    }

    public static function staticMethod() {
        return self::$name1 . "SMethod";
    }
    
    
    public function notStaticMethod() {
        return  $this->name . ' notStaticMethod ';
	//return  self::$name1 . ' notStaticMethod ';
    }
}


$obj = new ClassA();


echo $obj->staticMethod(); 

```
### Private Constructor
```php
class UtilClass {
    private function __construct() {
        echo "constructor called.\n";
    }
    
    public function getData() {
      echo 'Get Data';
    }
}

$objUtil = new UtilClass();
$objUtil->getData();

# Fixed 1
class UtilClass {
    private function __construct() {
        echo "Private constructor called.\n";
    }

    public static function createInstance() {
        return new self();
    }
    
    public function getData() {
      echo 'Get Data';
    }
}

// Creating an instance using the static method
$objUtil = UtilClass::createInstance(); 
$objUtil->getData();

// Direct instantiation is not allowed
// $obj = new FactoryClass(); // Error: Call to private FactoryClass::__construct()


# Fixed 2
class Singleton {
    private static $instance = null;

    // Private constructor to prevent external instantiation
    private function __construct() {
        echo "Private constructor called.\n";
    }

    // Public static method to access the single instance
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
}

// Accessing the singleton instance
$instance1 = Singleton::getInstance(); // Output: Private constructor called.
$instance2 = Singleton::getInstance(); // No new instance is created.

// Verify the instances are the same
var_dump($instance1 === $instance2); // Output: bool(true)

// Attempting direct instantiation will cause an error
// $obj = new Singleton(); // Error: Call to private Singleton::__construct()

```
### abstract class

```php

abstract class AbsCls  implements iA, iB {
   abstract public function absMethod();
}
interface iA {
    public function methodA();
}

interface iB {
    public function methodA();
    public function methodB();
}

trait Ta {
    public function methodA(): String {
        return 'Trait methodA ';
    }
}

trait TB {
    public function methodB(): String {
        return 'Trait methodB ';
    }
}

class A extends AbsCls {
    use Ta, Tb {
        Ta::methodA as traitAmethodA;
        Tb::methodB as traitBmethodB;
    }
    public $a = '';
    public function __construct() {
        
    }
    
    public function methodA(): String {
        return 'interface MethodA ';
        
    }
    
     public function methodB(): String {
        return 'interface methodB ';
        
    }
    
    public function absMethod(): String {
        return 'abstract absMethod ';
        
    }
}

$objA = new A();
echo $objA->methodB();
echo $objA->traitBmethodB();
echo $objA->absMethod();

```
# Design Patterns

```php
# Adapter Pattern 
// solve multiple payment method problem by using adapter pattern

interface PaymentGatewayInterface {
    public function pay($amount);
}

// PayPal API
class PayPal {
    public function sendPayment($amount) {
        echo "Processing PayPal payment of $$amount\n";
    }
}

// Stripe API
class Stripe {
    public function charge($amount) {
        echo "Processing Stripe payment of $$amount\n";
    }
}


// PayPal Adapter
class PayPalAdapter implements PaymentGatewayInterface {
    private $payPal;

    public function __construct(PayPal $payPal) {
        $this->payPal = $payPal;
    }

    public function pay($amount) {
        $this->payPal->sendPayment($amount);
    }
}

// Stripe Adapter
class StripeAdapter implements PaymentGatewayInterface {
    private $stripe;

    public function __construct(Stripe $stripe) {
        $this->stripe = $stripe;
    }

    public function pay($amount) {
        $this->stripe->charge($amount);
    }
}

class BillingService {
    private $paymentGateway;

    public function __construct(PaymentGatewayInterface $paymentGateway) {
        $this->paymentGateway = $paymentGateway;
    }

    public function processPayment($amount) {
        $this->paymentGateway->pay($amount);
    }
}

// Create instances of third-party payment gateways
$payPal = new PayPal();
$stripe = new Stripe();

// Wrap them with their respective adapters
$payPalAdapter = new PayPalAdapter($payPal);
$stripeAdapter = new StripeAdapter($stripe);

// Use the billing service with any adapter
$billingService = new BillingService($payPalAdapter);
$billingService->processPayment(100); // PayPal

$billingService = new BillingService($stripeAdapter);
$billingService->processPayment(200); // Stripe

Benefits of Using Adapter Pattern
Flexibility: You can integrate new payment gateways without modifying the BillingService or the common interface.
Single Responsibility: Adapters handle the translation between the PaymentGatewayInterface and the third-party API.
Scalability: Adding a new payment gateway only requires creating a new adapter.
Reusability: Adapters can be reused in other parts of the system.

```

```php
# Factory Pattern 
// solve multiple payment method problem by using factory pattern

interface PaymentGatewayInterface {
    public function processPayment($amount);
}

class PayPal implements PaymentGatewayInterface {
    public function processPayment($amount) {
        echo "Processing PayPal payment of $$amount\n";
    }
}

class Stripe implements PaymentGatewayInterface {
    public function processPayment($amount) {
        echo "Processing Stripe payment of $$amount\n";
    }
}

class PaymentGatewayFactory {
    public static function createPaymentGateway($type) {
        switch ($type) {
            case 'paypal':
                return new PayPal();
            case 'stripe':
                return new Stripe();
            default:
                throw new Exception("Unsupported payment method: $type");
        }
    }
}

class BillingService {
    private $paymentGateway;

    public function __construct(PaymentGatewayInterface $paymentGateway) {
        $this->paymentGateway = $paymentGateway;
    }

    public function processPayment($amount) {
        $this->paymentGateway->processPayment($amount);
    }
}


try {
    // Create a payment gateway using the factory
    $paymentGateway = PaymentGatewayFactory::createPaymentGateway('paypal');
    $billingService = new BillingService($paymentGateway);
    $billingService->processPayment(100); // PayPal

    $paymentGateway = PaymentGatewayFactory::createPaymentGateway('stripe');
    $billingService = new BillingService($paymentGateway);
    $billingService->processPayment(200); // Stripe

    // Invalid payment method
    $paymentGateway = PaymentGatewayFactory::createPaymentGateway('cash');
    $billingService = new BillingService($paymentGateway);
    $billingService->processPayment(400);
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

Advantages of the Factory Pattern
Centralized Object Creation: The factory handles all object creation logic in one place, making the system more maintainable.
Scalability: Adding a new payment method requires only a new class and a small modification to the factory.
Decoupling: The BillingService class is decoupled from the concrete payment method classes, adhering to the Dependency Inversion Principle.
Flexibility: The factory can use different logic to create objects, such as configuration files or user input.

```

# SOLID Principle

```php
// Violating Single Responsibility Principle
class User {  

    public function Save() {
        // Save record to database
    }

    public function sendEmail() {
        // Send email 
    }
}

// Following SRP: Each class has a single responsibility
class User {
    public function save(User $user) {
        // Save record to database
    }
}
// OR 
class UserRepository {
    public function save(User $user) {
        // Save record to database
    }
}

class EmailService {
    public function sendEmail($email) {
        // Send email
    }
}
```

```php
# Open/Closed Principle for diff types of payment method in billing service. 
# e.g payment from card, upi, paypal, amazonpay etc.

// Violating OCP
class Billing {
    public function payment(string $type, float $amount, int $discount) {       
        $this->{$type}($amount, $discount);
    }

    public function upiPayment() {
        echo 'Calculate UpiPayment' . $amount . ' ' . $discount;
    }

    public function cardPayment() {
        echo 'Calculate CardPayment' . $amount . ' ' . $discount;
    }
}

class CardPayment extends Billing
{
}
	
class UpiPayment extends Billing
{
}

$objCard = new CardPayment();
print $objCard->payment('cardPayment', 110.10, 2);

$objUpi = new UpiPayment();
print $objUpi->payment('upiPayment', 110.10, 4);

# Fixed
interface iBilling {
    public function pay(float $amount, int $discount);
}
	
class CardPayment implements iBilling
{
    public function pay(float $amount, int $discount) {
        echo 'Calculate CardPayment' . $amount . ' ' . $discount;
    }
}
	
class UpiPayment implements iBilling
{
    public function pay(float $amount, int $discount) {
        echo 'Calculate UpiPayment' . $amount . ' ' . $discount;
    }
}
	

class Billing {
    private $paymentMethod;
    public function __construct(iBilling $iBilling) {
    $this->paymentMethod = $iBilling;
    
    }
    public function payment(float $amount, int $discount) {
        $this->paymentMethod->pay($amount, $discount);
    }
}
	
$objCard = new CardPayment();
$objBilling = new Billing($objCard);
print $objBilling->payment(110.10, 2);

$objUpi = new UpiPayment();
$objBilling = new Billing($objUpi);
print $objBilling->payment(110.10, 4);

```

```php
// Liskov Substitution Principle (LSP)
// Subtypes must be substitutable for their base types without altering the correctness of the program.
```
