- let Vs var
  - var: Variables declared with var are function-scoped or globally scoped. This means that a variable declared with var inside a function is accessible anywhere within that function, but not outside of it.
  - let: Variables declared with let are block-scoped. This means they are only accessible within the block they are declared in, whether that's a loop, a conditional statement, or a function.
- let Vs const
  - Both are block-scoped.
  - let allows reassignment of the variable value. const does not allow reassignment once the variable has been initialized.
  - 
  ```javascript
  let a = 10;
  a = 20;  // Valid, reassignment is allowed
  console.log(a);  // Output: 20

  const b = 30;
  b = 40;  // Error: Assignment to constant variable
  console.log(b);  // Error
  ```
  - However, const does not make the value itself immutable if it is an object or array. You can still modify the contents of the object or array, but you cannot reassign the entire object or array.
  - 
  ```javascript
  const arr = [1, 2, 3];
  arr.push(4);  // Valid, the array is mutable
  console.log(arr);  // Output: [1, 2, 3, 4]

  arr = [5, 6, 7];  // Error: Assignment to constant variable
  ```
  - Hoisting
  - Hoisting in JavaScript is a behavior where variable and function declarations are moved to the top of their containing scope during the compilation phase, regardless of where they are declared within the code. However, it's important to note that only the declarations are hoisted, not the initializations.
  - 
  ```javascript
  console.log(x);  // Error: Cannot access 'x' before initialization
  let x = 10;

  console.log(y);  // Error: Cannot access 'y' before initialization
  const y = 20;

  console.log(z);  // undefined
  var z = 30;
  ```
- What is coercion in JavaScript
  - Coercion in JavaScript refers to the process of automatically converting values from one data type to another when they are used in a context that expects a different data type.
  - Example:
  - let num = 5 + '10'; // '510', number 5 is coerced into a string and concatenated with '10'
- Hoisting
  - Hoisting in JavaScript is a behavior where variable and function declarations are moved to the top of their containing scope during the compilation phase, regardless of where they are declared within the code. However, it's important to note that only the declarations are hoisted, not the initializations.
  - Variable Hoisting: When variables are declared using var, they are hoisted to the top of their containing scope. However, only the declaration is hoisted, not the initialization.
  ```javascript
  console.log(x); // undefined
  var x = 5;
  ```
  - Function Hoisting: Function declarations are also hoisted to the top of their containing scope. Unlike variables, both the function name and its implementation are hoisted.
  - 
  ```javascript
  foo(); // "Hello, I'm a function!"

  function foo() {
      console.log("Hello, I'm a function!");
  }
  ```
- How can we create object in Javascript
  - Object Literal: You can define an object using object literal notation, which is a list of key-value pairs enclosed in curly braces {}.
  - 
  ```javascript
    let person = {
      name: 'John',
      age: 30,
      profession: 'Developer'
    };
  ```
  - Constructor Function: You can define a constructor function and create objects using the new keyword.
  - 
  ```javascript
  function Person(name, age, profession) {
      this.name = name;
      this.age = age;
      this.profession = profession;
  }

  let person = new Person('John', 30, 'Developer');
  ```
  - Object.create(): You can create an object using Object.create() method by specifying the prototype object.
  - 
  ```javascript
  let personPrototype = {
      greeting: function() {
          return 'Hello, my name is ' + this.name;
      }
  };

  let person = Object.create(personPrototype);
  person.name = 'John';
  person.age = 30;
  person.profession = 'Developer';
  ```
  - Class (ES6): With ES6, you can use class syntax to define objects.
  - 
  ```javascript
    class Person {
      constructor(name, age, profession) {
          this.name = name;
          this.age = age;
          this.profession = profession;
      }
  }

  let person = new Person('John', 30, 'Developer');
  ```
  - Factory Function: You can create objects using factory functions, which are functions that return objects.
  - 
  ```javascript
    function createPerson(name, age, profession) {
      return {
          name: name,
          age: age,
          profession: profession
      };
  }

  let person = createPerson('John', 30, 'Developer');
  ```
  - 
- What is Closure in Javascript
  - In JavaScript, a closure is a combination of a function and the lexical environment within which that function was declared. Closures allow functions to retain access to variables from their containing scope even after the parent function has finished executing.
  - Here's an example to illustrate the concept of closure:
  - 
  ```javascript
    function outerFunction() {
      let outerVariable = 'I am from the outer function';
      
      function innerFunction() {
          console.log(outerVariable); // Accessing outerVariable from the outer scope
      }

      return innerFunction;
  }

  let closureFunction = outerFunction(); // The inner function is returned and assigned to closureFunction
  closureFunction(); // When closureFunction is invoked, it still has access to outerVariable
  ```
  - In this example, innerFunction is a closure because it's declared inside outerFunction and has access to the outerVariable of outerFunction. Even though outerFunction has finished executing by the time innerFunction is called, innerFunction still has access to outerVariable due to closure.
  - Closures are commonly used for:
    - Encapsulation: Keeping variables private to a function.
    - Data Hiding: Exposing only necessary details while hiding the implementation details.
    - Callback functions: Preserving the state of variables between function calls in asynchronous operations.
    - Module pattern: Creating modules with private data and methods.

- What is IIFE
  - IFE stands for Immediately Invoked Function Expression. It is a JavaScript function that is executed immediately after it is defined. The primary purpose of an IIFE is to create a new scope for the enclosed code, preventing variable name conflicts and keeping the global scope clean.
  - 
  ```javascript
  (function() {
      // IIFE code goes here
      var x = 10;
      console.log(x); // Output: 10
  })();
  ```
- Difference between const and Object.freeze()

- What is callback hell
  - Callback hell, also known as "pyramid of doom" or "callback spaghetti", refers to the situation in asynchronous JavaScript programming where deeply nested callbacks make the code difficult to read, understand, and maintain. It occurs when multiple asynchronous operations are chained together using callback functions, resulting in deeply nested and indented code structures.
  - 
  ```javascript
    asyncFunction1(function(result1) {
      asyncFunction2(result1, function(result2) {
          asyncFunction3(result2, function(result3) {
              asyncFunction4(result3, function(result4) {
                  // More nested callbacks...
              });
          });
      });
  });
  ```
  - To mitigate callback hell, you can use techniques such as:
    - Named Functions: Define named functions for callbacks instead of using anonymous functions. This can help reduce nesting and make the code more readable.
    - Promises: Use Promises to handle asynchronous operations in a more sequential and organized manner. Promises allow you to chain asynchronous operations using then() methods, which can help avoid deep nesting.
    - Async/Await: Use async/await syntax, which provides a more synchronous-looking way to write asynchronous code. Async functions return Promises, allowing you to write asynchronous code in a more sequential and readable manner.

- Explain the event loop in JavaScript.
  - The event loop is a mechanism in JavaScript that allows asynchronous operations to be executed in a non-blocking manner. It continuously checks the call stack and the task queue. When the call stack is empty, it takes the first function from the task queue and pushes it onto the call stack for execution.

- promises vs async awaits differences
  - Promises and async/await are both features introduced in JavaScript to handle asynchronous code in a more readable and manageable way. Here are the main differences between them:
  - Promises: Promises use a chainable syntax with then() and catch() methods to handle asynchronous operations and errors.
  - Async/Await: Async/await uses the async keyword to define an asynchronous function, and the await keyword to pause the execution of the function until a Promise is resolved. This results in code that looks more synchronous and is easier to understand, especially for developers who are familiar with synchronous programming paradigms.
  - Promises: Errors in promises are handled using the catch() method or by chaining a second callback function to the then() method for error handling.
  - Async/Await: Errors in async/await functions can be handled using traditional try/catch blocks, making error handling more similar to synchronous code.
  - Promises: Nesting multiple asynchronous operations with promises can lead to deeply nested code structures, which can reduce readability.
  - Async/Await: Async/await allows developers to write sequential code for nested asynchronous operations using regular control flow statements like if, for, and while, resulting in code that is easier to understand and maintain.
  - Overall, async/await is considered a more modern and preferred approach for handling asynchronous operations in JavaScript due to its improved readability, error handling, and ability to write sequential code for asynchronous operations. However, promises still have their place and are widely used, especially in scenarios where compatibility with older JavaScript environments is required.

- What are arrow functions in JavaScript?
  - Arrow functions are a shorthand syntax for writing functions in JavaScript. They provide a more concise syntax compared to traditional function expressions and have lexical scoping of this, meaning they inherit this from the surrounding code where they are defined.

- When should one not use arrow functions
  - Function Hoisting, Named Functions:
    - As arrow functions are anonymous, we cannot use them when we want function hoisting or when we want to use named functions.
    ```javascript
    var a = {
    b: 7,
    func: () => {
      this.b--;
      }
    };

    console.log(a);
    ```
    - The value of b does not drop when you call a.func. It's because this isn't bound to anything and will inherit the value from its parent scope.
  - Callback functions with dynamic context
  - this/arguments:
    - Since arrow functions don’t have this/arguments of their own and they depend on their outer context, we cannot use them in cases where need to use this/arguments in a function.

- What is code obfuscation and minification in javascript
  - Code obfuscation in JavaScript (or any other programming language) refers to the process of transforming the source code into a version that is difficult for humans to understand, while still maintaining its functionality. This is often done to protect intellectual property, prevent reverse engineering, and make it harder for others to steal or tamper with the code.
  - 
  ```javascript
  function greetUser(name) {
      console.log("Hello, " + name + "!");
  }
  greetUser("Alice");

  //obfunction
  function a(b){console.log("Hello, "+b+"!");}a("Alice");
  ```
  - Code minification is a process that reduces the size of JavaScript (or other front-end files like CSS and HTML) by removing unnecessary characters from the code without changing its functionality. It aims to reduce file size and improve performance by speeding up the loading time of web applications.
  - 
  ```javascript
  function greetUser(name) {
      console.log("Hello, " + name + "!");
  }
  function greetUser(n){console.log("Hello, "+n+"!");}
  ```

- Debouncing in JavaScript
  - Debouncing is a programming pattern used in JavaScript to limit the rate at which a function is executed. Specifically, it ensures that a function is only invoked after a specified time has passed since the last time it was called. This is particularly useful when you want to prevent a function from being called too frequently, such as in response to rapid user actions like scrolling, resizing the window, or typing.

- Throttling in JavaScript
  - Throttling is a technique in JavaScript that controls how frequently a function is executed during continuous events. It ensures that a function is called at most once in a given interval, even if the event is triggered multiple times. This is particularly useful when dealing with performance-heavy operations like scrolling, window resizing, or mouse movement.
  ![alt text](<images/Debouncing-Vs-Throttling.png>)

- Where JWT Token getting stored?
  - Web Apps: Use cookies (preferably httpOnly and Secure) or localStorage/sessionStorage (with caution).
  - Mobile Apps: Use platform-specific secure storage like Keychain or Keystore.
  - Security Focus: Always prioritize preventing XSS and CSRF to protect JWTs.

- Which method we should prefer to pass JWT token as a request payload?
  - Authorization Header (Recommended) - Authorization: Bearer <your-jwt-token>
  - Request Body (For POST or PUT Requests)
  - Query Parameters (Least Recommended)
  - Best Practices for Passing JWT Tokens
    - Use Secure Transmission:
      - Always use HTTPS to encrypt communication and prevent token exposure.
    - Prefer Authorization Header:
      - The Authorization header is the most secure and widely used method.
    - Avoid Storing Tokens in URLs:
      - Do not use query parameters unless absolutely necessary, as URLs are more exposed.
    - Token Expiry:
      - Ensure JWTs have short expiration times and rotate tokens when needed.
    - Validate on Server:
      - Always validate the JWT on the server to ensure its authenticity and integrity.
    - Mitigate CSRF:
      - If using cookies for token storage, ensure they are HttpOnly, Secure, and have SameSite attributes.

ECMAScript 6 Feature
- let and const
- Arrow function
- Template Literals -> Allows embedded expressions and multi-line strings using backticks (`).
- default values for function parameters
- Spread (...): Expands elements of an array or object.
	const arr1 = [1, 2, 3];
	const arr2 = [...arr1, 4, 5];
	console.log(arr2); // [1, 2, 3, 4, 5]
- Rest (...): Collects arguments into an array
	function sum(...numbers) {
  		return numbers.reduce((acc, curr) => acc + curr, 0);
	}
	console.log(sum(1, 2, 3)); // 6

- Classes
- Modules - Allows importing and exporting code between files.
- Promises - Simplifies handling of asynchronous operations
- Iterators and For-Of Loop
- Map: Key-value pairs where keys can be any type
- Set: Collection of unique values
- Symbols - A new primitive data type for unique identifiers.
- Block-Scoped Functions
Functions declared with function inside a block are block-scoped.

Example:

```javascript
// javascript to insert array as [1,2,3,4,5] and get output as [5,1,2,3,4]
// without using new array or any inbuilt functions, only with array iteration

// Input [1, 2, 3, 4, 5]
// Output: [5, 1, 2, 3, 4]


let arr = [1, 2, 3, 4, 5];

// Store the last element
let lastElement = arr[arr.length - 1];

// Shift all elements one position to the right
for (let i = arr.length - 1; i > 0; i--) {
    arr[i] = arr[i - 1];
}

// Place the last element at the beginning
arr[0] = lastElement;

console.log(arr); 

```
```javascript

{
  function test() {
    console.log("Inside block");
  }
  test();
}
// test(); // ReferenceError: test is not defined

- Generators: Functions that can be paused and resumed.
- 



var i = 1;
let j = 11;
(function() {
    var i = 2;
    let j = 22;
    console.log(i); 
    console.log(j);
})();
console.log(i);
console.log(j);


var a = 1;
var b = 2;
var c = '3';
console.log(a+b+c);

var d = null;
console.log(typeof d);

var object1 = { name: 'abc' };
var object2 = { name: 'abc' };
console.log(object1 === object2);
console.log(JSON.stringify(object1) === JSON.stringify(object1));

```