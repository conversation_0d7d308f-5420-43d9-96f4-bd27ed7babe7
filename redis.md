- Redis
    - Redis, which stands for Remote Dictionary Server, is a fast, open-source, in-memory key-value data store for use as a database, cache, message broker, and queue.
    - Supports multiple datatypes (strings, hashes, lists, sets, sorted sets, bitmaps, and hyperloglogs)
    - Supports pub-sub model
    - Redis cache provides replication for high availability (master/slave)


- Ref:
    - https://www.fullstack.cafe/interview-questions/redis