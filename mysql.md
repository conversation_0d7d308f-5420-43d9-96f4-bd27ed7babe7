```mysql

https://onecompiler.com/mysql


# Example 1

CREATE TABLE projects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100),
    tech_stack VARCHAR(100)
);

INSERT INTO projects (name, tech_stack)
VALUES
    ('Project1', 'PHP'),
    ('Project2', 'Java'),
    ('Project1', 'PHP'),
    ('Project3', 'Python'),
    ('Project4', 'PHP'),
    ('Project1', 'Java'),
    ('Project1', 'PHP'),
    ('Project3', 'Python'),
    ('Project1', 'Java'),
    ('Project3', 'Java'),
    ('Project4', 'PHP');


select * from projects order by name, tech_stack asc;

+----+----------+------------+
| id | name     | tech_stack |
+----+----------+------------+
|  6 | Project1 | Java       |
|  9 | Project1 | Java       |
|  1 | Project1 | PHP        |
|  3 | Project1 | PHP        |
|  7 | Project1 | PHP        |
|  2 | Project2 | Java       |
| 10 | Project3 | Java       |
|  4 | Project3 | Python     |
|  8 | Project3 | Python     |
|  5 | Project4 | PHP        |
| 11 | Project4 | PHP        |
+----+----------+------------+



select count(*) as dup, name, tech_stack  from projects GROUP by name, tech_stack having dup > 1;
+-----+----------+------------+
| dup | name     | tech_stack |
+-----+----------+------------+
|   3 | Project1 | PHP        |
|   2 | Project3 | Python     |
|   2 | Project4 | PHP        |
|   2 | Project1 | Java       |
+-----+----------+------------+


SELECT distinct a.*
FROM projects a
JOIN projects b
  ON a.name = b.name and a.tech_stack = b.tech_stack
 AND a.id <> b.id order by a.name, a.tech_stack asc;

OR 

SELECT distinct a.*
FROM projects a
WHERE EXISTS (
    SELECT 1
    FROM projects b
    WHERE a.name = b.name and a.tech_stack = b.tech_stack
      AND a.id <> b.id
) order by a.name, a.tech_stack asc;


+----+----------+------------+
| id | name     | tech_stack |
+----+----------+------------+
|  6 | Project1 | Java       |
|  9 | Project1 | Java       |
|  1 | Project1 | PHP        |
|  3 | Project1 | PHP        |
|  7 | Project1 | PHP        |
|  4 | Project3 | Python     |
|  8 | Project3 | Python     |
|  5 | Project4 | PHP        |
| 11 | Project4 | PHP        |
+----+----------+------------+

#######################################################################################

# Example 2
# Get Duplicate records based on name and tech_stack
+-----+----------+------------+
| dup | name     | tech_stack |
+-----+----------+------------+
|   2 | Project1 | Ruby        |
|   2 | Project3 | Python     |
+-----+----------+------------+

# Remove Duplicate Entry (Remove first inserted duplicate record)
+----+----------+------------+
| id | name     | tech_stack |
+----+----------+------------+
|  3 | Project1 | Ruby       |
|  6 | Project1 | Java       |
|  2 | Project2 | Java       |
|  7 | Project3 | Python     |
|  5 | Project4 | Ruby       |
+----+----------+------------+

CREATE TABLE projects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100),
    tech_stack VARCHAR(100)
);

INSERT INTO projects (name, tech_stack)
VALUES
    ('Project1', 'Ruby'),
    ('Project2', 'Java'),
    ('Project1', 'Ruby'),
    ('Project3', 'Python'),
    ('Project4', 'Ruby'),
    ('Project1', 'Java'),
    ('Project3', 'Python');

select * from projects order by name asc;


select count(*) as dup, name, tech_stack  from projects GROUP by name, tech_stack having dup > 1;
select count(*) as dup, tech_stack  from projects GROUP by tech_stack  having dup > 1;


select p1.* 
from projects as p1 
inner join projects as p2 on p1.name = p2.name and p1.tech_stack = p2.tech_stack
where p1.id < p2.id;


delete p1 
from projects as p1 
inner join projects as p2 on p1.name = p2.name and p1.tech_stack = p2.tech_stack
where p1.id < p2.id;


DELETE FROM projects
WHERE id NOT IN (
SELECT MAX(id)
    FROM (
        SELECT id, name, tech_stack
        FROM projects
    ) AS temp
    GROUP BY name, tech_stack
);


select * from projects  order by id asc ;


#############################

# Remove Duplicate Entry
CREATE TABLE players (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100),
    team VARCHAR(100)
);

INSERT INTO players (name, team)
VALUES
    ('Player1', 'TeamA'),
    ('Player2', 'TeamB'),
    ('Player1', 'TeamA'),
    ('Player3', 'TeamC'),
    ('Player4', 'TeamA'),
    ('Player3', 'TeamC');

select * from players ;

-- 1. select max(t.id), name, team from ( SELECT id, name, team FROM players ) as t group by name, team;
-- 2. select max(p.id), p.name, p.team from players p 
inner join (
SELECT count(*) as dup, name FROM players group by name, team ) t on p.name = t.name
group by p.team, p.name;


SELECT p1.* 
FROM projects as p1 
inner join projects as p2 on p1.name = p2.name and p1.tech_stack = p2.tech_stack
where p1.id < p2.id;

DELETE p1 
from projects as p1 
inner join projects as p2 on p1.name = p2.name and p1.tech_stack = p2.tech_stack
where p1.id < p2.id;

DELETE FROM players
WHERE id NOT IN (
SELECT MAX(id)
    FROM (
        SELECT id, name, team
        FROM players
    ) AS temp
    GROUP BY name, team
);


select * from players ;


# Initial Records
+----+---------+-------+
| id | name    | team  |
+----+---------+-------+
|  1 | Player1 | TeamA |
|  2 | Player2 | TeamB |
|  3 | Player1 | TeamA |
|  4 | Player3 | TeamC |
|  5 | Player4 | TeamA |
|  6 | Player3 | TeamC |
+----+---------+-------+
# ^^ Player1 (1,3) and Player3(4,6) appear as a duplicate records
# Keep the lastest one Player1(3) and Player3(6) and Remove duplicte records 1 and 4

# After Removing Duplicate Records (Keep the latest record)
+----+---------+-------+
| id | name    | team  |
+----+---------+-------+
|  2 | Player2 | TeamB |
|  3 | Player1 | TeamA |
|  5 | Player4 | TeamA |
|  6 | Player3 | TeamC |
+----+---------+-------+

# Get Highest Marks from each class and Subject 
CREATE TABLE students (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100),
    class_id VARCHAR(10),
    subject_id VARCHAR(100),
    marks INT
);


CREATE TABLE class (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100)
);

CREATE TABLE subject (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100)
);


INSERT INTO class (name) VALUES
('Class1'),
('Class2');

INSERT INTO subject (name) VALUES
('Subject1'),
('Subject2');



INSERT INTO students (name, class_id, subject_id, marks) VALUES
('Student1', 1, 1, 80),
('Student2', 1, 1, 95),
('Student3', 1, 2, 83),
('Student4', 1, 2, 82),
('Student5', 2, 1, 97),
('Student6', 2, 2, 84),
('Student7', 2, 1, 74),
('Student8', 2, 2, 91);

Output:

+------------+--------------+--------+----------+-------+
| student_id | student_name | class  | subject  | marks |
+------------+--------------+--------+----------+-------+
|          2 | Student2     | Class1 | Subject1 |    95 |
|          3 | Student3     | Class1 | Subject2 |    83 |
|          5 | Student5     | Class2 | Subject1 |    97 |
|          8 | Student8     | Class2 | Subject2 |    91 |
+------------+--------------+--------+----------+-------+

# Query
SELECT 
    s.id as student_id, 
    s.name as student_name, 
    c.name as class, 
    sj.name as subject, 
    s.marks
FROM 
    students s
    inner join class c on c.id = s.class_id
    inner join subject sj on sj.id = s.subject_id
WHERE 
    s.marks = (
        SELECT MAX(marks) 
        FROM students 
        WHERE students.class_id = s.class_id AND students.subject_id = s.subject_id
    );    

# Other SQLs

CREATE TABLE students (
  studId INTEGER PRIMARY KEY,
  name TEXT NOT NULL,
  class TEXT NOT NULL
);

-- insert
INSERT INTO students VALUES (1, 'Stud1', 'Class1');
INSERT INTO students VALUES (2, 'Stud2', 'Class2');
INSERT INTO students VALUES (3, 'Stud3', 'Class1');
INSERT INTO students VALUES (4, 'Stud4', 'Class1');
INSERT INTO students VALUES (5, 'Stud5', 'Class2');
INSERT INTO students VALUES (6, 'Stud6', 'Class1');
INSERT INTO students VALUES (7, 'Stud7', 'Class2');
INSERT INTO students VALUES (8, 'Stud8', 'Class3');



# Duplicate records
+-------------+--------+
| total_class | class  |
+-------------+--------+
|           4 | Class1 |
|           3 | Class2 |
+-------------+--------+

SELECT COUNT(*) as total_class, class
from students
group by class
having total_class > 1;


-- 2

CREATE TABLE students (
  studId INTEGER PRIMARY KEY,
  name TEXT NOT NULL,
  marks TEXT NOT NULL
);

-- insert
INSERT INTO students VALUES (1, 'Stud1', '80');
INSERT INTO students VALUES (2, 'Stud2', '90');
INSERT INTO students VALUES (3, 'Stud3', '85');
INSERT INTO students VALUES (4, 'Stud4', '83');
INSERT INTO students VALUES (5, 'Stud5', '90');
INSERT INTO students VALUES (6, 'Stud6', '85');
INSERT INTO students VALUES (7, 'Stud7', '87');
INSERT INTO students VALUES (8, 'Stud8', '80');



# Second Highest Marks:
+--------+-------+-------+
| studId | name  | marks |
+--------+-------+-------+
|      7 | Stud7 | 87    |
+--------+-------+-------+


SELECT studId, name, marks
FROM students
WHERE marks = (
    SELECT MAX(marks)
    FROM students
    WHERE marks < (SELECT MAX(marks) FROM students)
);


# Third highest marks
+--------+-------+-------+
| studId | name  | marks |
+--------+-------+-------+
|      3 | Stud3 | 85    |
|      6 | Stud6 | 85    |
+--------+-------+-------+

SELECT studId, name, marks
FROM students
WHERE marks = (
select DISTINCT marks from students order by marks desc limit 2,1
);

# Odd Marks
+--------+-------+-------+
| studId | name  | marks |
+--------+-------+-------+
|      3 | Stud3 | 85    |
|      4 | Stud4 | 83    |
|      6 | Stud6 | 85    |
|      7 | Stud7 | 87    |
+--------+-------+-------+

SELECT * 
FROM students
WHERE marks % 2 <> 0;

CREATE TABLE students (
  studId INTEGER PRIMARY KEY,
  name TEXT NOT NULL,
  classId INTEGER NOT NULL
);

CREATE TABLE class (
  classId INTEGER PRIMARY KEY,
  name TEXT NOT NULL
);


-- insert
INSERT INTO class VALUES (1, 'Class1');
INSERT INTO class VALUES (2, 'Class2');
INSERT INTO class VALUES (3, 'Class3');
INSERT INTO class VALUES (4, 'Class4');

INSERT INTO students VALUES (1, 'Stud1', 1);
INSERT INTO students VALUES (2, 'Stud2', 1);
INSERT INTO students VALUES (3, 'Stud3', 2);
INSERT INTO students VALUES (4, 'Stud4', 3);
INSERT INTO students VALUES (5, 'Stud5', 2);
INSERT INTO students VALUES (6, 'Stud6', 1);
INSERT INTO students VALUES (7, 'Stud7', 5);

#Students does not have any classes

+--------+-------+---------+---------+------+
| studId | name  | classId | classId | name |
+--------+-------+---------+---------+------+
|      7 | Stud7 |       5 |    NULL | NULL |
+--------+-------+---------+---------+------+

#Class does not have any students
+---------+--------+--------+------+---------+
| classId | name   | studId | name | classId |
+---------+--------+--------+------+---------+
|       4 | Class4 |   NULL | NULL |    NULL |
+---------+--------+--------+------+---------+

select * from students s 
left join class c on s.classId = c.classId
WHERE c.classId is null;

select * from class c
left join students s on s.classId = c.classId
WHERE s.classId is null;


# Parent/Child Relationship

CREATE TABLE technologies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    parent_id INT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES technologies(id) ON DELETE CASCADE
);


INSERT INTO technologies (name, parent_id) VALUES 
('Backend', NULL),
('Frontend', NULL),
('Others', NULL);

-- Insert Subcategories (parent_id refers to parent category)
INSERT INTO technologies (name, parent_id) VALUES 
('PHP', 1),
('Python', 1),
('Java', 1),
('Ruby on Rails', 1),
('Angular', 2),
('ReactJs', 2),
('VueJs', 2),
('Docker', 3),
('Kubernetes', 3);

SELECT parent.name AS Category, child.name AS SubCategory
FROM technologies AS parent
LEFT JOIN technologies AS child ON parent.id = child.parent_id
WHERE child.parent_id IS NOT NULL;


DELETE FROM technologies WHERE id = 1; -- Deletes Parent and its Child


# Hotels and Rooms Example

CREATE TABLE hotels (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL
);

CREATE TABLE rooms (
    id INT AUTO_INCREMENT PRIMARY KEY,
    hotel_id INT NOT NULL,
    room_number VARCHAR(50) NOT NULL,
    room_type ENUM('Single', 'Double', 'Suite', 'Triple', 'Connecting') NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    is_available BOOLEAN DEFAULT TRUE    
);


INSERT INTO hotels (name) VALUES
('Hotel A'), ('Hotel B'), ('Hotel C');

INSERT INTO rooms (hotel_id, room_number, room_type, price, is_available) VALUES
(1, '101', 'Single', 1000, true),
(1, '202', 'Double', 1500, false),
(1, '302', 'Suite', 3000, true),
(2, '101', 'Single', 1200, true),
(2, '204', 'Double', 1600, false),
(2, '303', 'Triple', 3200, true),
(3, '111', 'Single', 1100, true),
(3, '222', 'Double', 2200, true),
(3, '333', 'Connecting', 4000, false);

-- get most expensive rooms in each hotels

+------------+-------------+------------+---------+
| hotel_name | room_number | room_type  | price   |
+------------+-------------+------------+---------+
| Hotel A    | 302         | Suite      | 3000.00 |
| Hotel B    | 303         | Triple     | 3200.00 |
| Hotel C    | 333         | Connecting | 4000.00 |
+------------+-------------+------------+---------+

SELECT h.name AS hotel_name, r.room_number, r.room_type, r.price
FROM rooms r
INNER JOIN hotels h ON r.hotel_id = h.id
WHERE r.price = (SELECT MAX(price) FROM rooms WHERE hotel_id = h.id);

-- get most expensive available rooms in each hotels

SELECT h.name AS hotel_name, r.room_number, r.room_type, r.price
FROM rooms r
INNER JOIN hotels h ON r.hotel_id = h.id
WHERE r.price = (SELECT MAX(price) FROM rooms WHERE hotel_id = h.id and is_available = true);

-- FOREIGN KEY (hotel_id) REFERENCES hotels(id) ON DELETE CASCADE;

```

### Types of Indxes

Indexes in databases are data structures that improve the speed of data retrieval operations on a database table at the cost of additional writes and storage space. Different types of indexes serve various purposes and are optimized for specific use cases. Here are the common types of indexes:

1. Primary Index
Description: Automatically created when a primary key is defined. The primary index enforces the primary key constraint, ensuring that the values in the indexed column(s) are unique and not NULL.
Example:
CREATE TABLE employees (
    emp_id INT PRIMARY KEY,
    emp_name VARCHAR(100)
);
2. Unique Index
Description: Ensures all values in the indexed column(s) are unique. Unlike a primary index, a table can have multiple unique indexes, and the column can include NULL values.
Example:
CREATE UNIQUE INDEX idx_email ON users (email);
3. Clustered Index
Description: Physically sorts the rows in the table based on the indexed column(s). A table can have only one clustered index since the table's rows are stored in this order.
Example: In MySQL's InnoDB, the primary key creates a clustered index by default.
CREATE CLUSTERED INDEX idx_order_date ON orders (order_date);
4. Non-Clustered Index
Description: Maintains a separate structure for the index and contains pointers to the physical data rows. Multiple non-clustered indexes can exist on a table.
Example:
CREATE INDEX idx_name ON employees (emp_name);
5. Composite Index (Multi-Column Index)
Description: An index on two or more columns. It's useful for queries that filter or sort by multiple columns together.
Example:
CREATE INDEX idx_name_department ON employees (emp_name, department_id);
6. Full-Text Index
Description: Used for full-text search operations, such as searching for words or phrases in large text fields.
Supported By: MySQL, PostgreSQL, and others.
Example:
CREATE FULLTEXT INDEX idx_content ON articles (content);
7. Spatial Index
Description: Specifically designed for spatial data types such as points, lines, and polygons. Used in GIS (Geographic Information Systems) queries.
Supported By: MySQL (with MyISAM) and others.
Example:
CREATE SPATIAL INDEX idx_location ON locations (geom);
8. Bitmap Index
Description: Stores index data as bitmaps and is highly efficient for columns with a low cardinality (i.e., a small number of distinct values).
Supported By: Oracle, PostgreSQL (extensions), and others.
Use Case: Ideal for gender, boolean flags, or small enumerated values.
9. Partial Index
Description: Indexes only a subset of rows in a table based on a condition. This reduces storage and improves performance for specific queries.
Supported By: PostgreSQL.
Example:
CREATE INDEX idx_active_users ON users (last_login) WHERE is_active = true;
10. Covering Index
Description: A type of index where the indexed columns satisfy the query without needing to access the table. This is not explicitly created but results from a well-designed composite index.
Use Case: Optimizing queries to avoid additional table lookups.
11. Hash Index
Description: Maps key values to locations using a hash function. It is useful for equality searches but not range queries.
Supported By: MySQL (Memory storage engine), Redis, etc.
Example:
CREATE INDEX idx_hash_example ON table_name (column_name) USING HASH;
12. Unique Clustered Index
Description: A combination of a unique constraint and a clustered index. Ensures uniqueness and sorts data physically.

Choose the appropriate index type based on your query patterns and data structure for optimal performance.

![alt text](<images/mysql-index-table.png>)

### Other Q?
- Diff between PK and UK - Can we have a null value allowed in PK/Uk?
- Composite index key
- Clustered Index Vs Non-Clustered Index
    - In MySQL, the term "non-clustered index" is not directly used as it is in some other database management systems like Microsoft SQL Server. Instead, MySQL uses the concept of "secondary indexes" to refer to indexes that are not part of the primary key or clustering key.
- Does in mandatory to set auto incremental value for primary key in mysql? - No
- Disadvantage of Indexes
- Performance Improvement Techniques in MySQL:
    - Create indexes on columns frequently used in WHERE, JOIN, ORDER BY, and GROUP BY clauses.
    - Use composite indexes when filtering by multiple columns.
    - Avoid over-indexing, as too many indexes slow down INSERT, UPDATE, and DELETE operations.
    - The EXPLAIN statement helps identify slow queries and inefficient table scans.
    - Avoid SELECT * (Fetch Only Required Columns)
    - Use JOIN Instead of Subqueries
    - Use unique indexes on columns where duplicate values should not exist
    - Use smaller data types where possible (TINYINT instead of INT if storing small numbers).
    - Avoid TEXT and BLOB columns unless necessary.
    - Use Partitioning for Large Tables
    - Use InnoDB Instead of MyISAM - InnoDB supports transactions, row-level locking, and better concurrency.
    - Set innodb_buffer_pool_size to 50-75% of available RAM for better performance.
    - Use Memcached or Redis for Caching
    - Index columns used in JOIN to improve performance.
    - Avoid Large ORDER BY and GROUP BY Without Indexing
    - Limit the Number of Rows Fetched
    - Analyze and Update Table Statistics


- SQL Vs NoSQL - When to use, CAP Theoram - Consistancy, Availability and Partition tolerance
    - https://github.com/mpatel2280/interview-prep/blob/master/system-architect.md#q-what-considerations-should-be-taken-into-account-when-designing-a-database-schema-how-do-you-choose-between-sql-and-nosql-databases-for-a-given-project
- How to get number of table rows without using count()
    - SELECT SUM(1) AS total_rows FROM --tablename
- Aggregate functions
    - Aggregate functions are functions which are used to get a single summary value like minimum, maximum or average of a group of values.
    - COUNT, SUM, AVG, MIN, MAX are examples of MySQL aggregate functions
- Diff char and varchar
- Diff BLOB and Text 
    - Blob: If you need to store and manipulate binary data (e.g., multimedia files, encrypted content).
    - Text: If you need to store and manipulate large textual data (e.g., long articles, logs).
- Why Foreign keys are not supported in partitioned tables
    - Due to referential integrity. Its difficult to maintain RI in partitioned tables
    - Performance issue. Checking FK slow down the performance
- UUID Vs Auto incremental PK
    - Distributed Systems: When multiple databases or nodes generate records independently.
    - Data Migrations: When merging or moving data across systems or environments without risking PK collisions.
    - Security-Sensitive Applications: When exposing IDs externally (e.g., in URLs or APIs) and you want to obscure patterns or table sizes.
    - Decoupled Systems: When the application and database are designed to work independently of one another.
- What happen if duplicate records are in table and then we will add unique key contraints 
    - MySQL will raise an error because the constraint cannot be enforced when duplicates exist
- What is the difference between TRUNCATE and DELETE statements?
    - TRUNCATE is a DDL (Data Definition Language) statement that removes all rows from a table efficiently and quickly. It also resets the auto-increment value for the table's primary key column, if it has one. Rollback is not possible with TRUNCATE.
        - TRUNCATE TABLE empties a table completely. It requires the DROP privilege. Logically, TRUNCATE TABLE is similar to a DELETE statement that deletes all rows, or a sequence of DROP TABLE and CREATE TABLE statements.
    - DELETE is a DML (Data Manipulation Language) statement that removes specific rows from a table based on a specified condition. DELETE removes rows one by one and can be rolled back within a transaction. Rollback is possible with DELETE.
    
    ![alt text](<images/truncate-vs-delete.png>)

- Truncate Vs Delete
    - TRUNCATE is a DDL (Data Definition Language) statement and cannot be rolled back using the ROLLBACK command.
    - DELETE is a DML (Data Manipulation Language) statement and can be rolled back using the ROLLBACK command if executed within a transaction.
    - In summary, TRUNCATE is a faster operation that removes all rows from a table and resets auto-increment values, while DELETE is a slower operation that removes specific rows based on a condition and can be rolled back within a transaction. The choice between TRUNCATE and DELETE depends on the specific requirements and constraints of the situation.

- What happen if duplicate records are in table and then we will add unique key contraints 
    - If you attempt to add a UNIQUE key constraint to a column in a table that already contains duplicate records, MySQL will raise an error because the constraint cannot be enforced when duplicates exist.
-  What are the types of database partitioning, and which is most common in Rails applications?
    - Horizontal Partitioning (Sharding): Divides rows across multiple tables/databases.
    - Vertical Partitioning: Divides columns into separate tables.
    - Range Partitioning: Distributes data based on value ranges in a specific column.
    - List Partitioning: Groups rows by specific values in a column.
    - Hash Partitioning: Distributes data using a hash function.
    - Horizontal partitioning (sharding) is commonly used in Rails applications for scaling large datasets across multiple database instances.
- What are some challenges with implementing database partitioning in Rails?
    - Query complexity: Queries must consider partitioning logic (e.g., routing requests to the correct shard).
    - Active Record limitations: Rails Active Record does not natively support partitioning.
    - Data rebalancing: If a partition grows too large, rebalancing data can be difficult.
    - Index management: Proper indexing must be maintained for each partition.
    - Transaction management: Distributed transactions across partitions/shards can be challenging.
- In what scenarios would you avoid database partitioning in Rails?
    - When the dataset is small or manageable within a single database.
    - When the complexity introduced by partitioning outweighs its benefits.
    - For applications with low write traffic and a minimal need for scalability.
- How does Rails handle sharding?
    - Rails itself does not provide built-in support for sharding, but you can use gems like:
    - activerecord-multi-tenant: Helps manage multi-tenancy and database sharding.
    - octopus: Adds sharding and read/write split support.
        -
        ```ruby
        config/shards.yml
        development:
        shard1:
            adapter: mysql2
            database: shard1_db
            username: root
            password: password
            host: localhost
        shard2:
            adapter: mysql2
            database: shard2_db
            username: root
            password: password
            host: localhost

        Octopus.using(:shard1) do
            User.create(name: "John Doe")
        end

        # manage schema migrations across multiple partitions/shards in Rails?
        RAILS_ENV=production SHARD=shard1 rails db:migrate
        RAILS_ENV=production SHARD=shard2 rails db:migrate
        ```
    - Manual partitioning logic: You can programmatically route queries to different shards using custom database connections.
- How can partitioning affect Rails Active Record relationships?
    - Partitioning can complicate Active Record associations because related records might be in different partitions or shards. You can address this by:
    - Using manual joins or queries for cross-partition relationships.
    - Employing a middleware or service layer to manage inter-partition logic.
    - Limiting associations to the same shard.
- Advantages and Disadvantages of Stored Procedure
    - Advantages
        - Stored procedures are precompiled and stored in the database, resulting in faster execution compared to sending multiple SQL queries from the application layer.
        - By executing multiple SQL statements in a single procedure call, stored procedures reduce the need for multiple round trips between the application and the database.
        - Permissions can be set at the stored procedure level, restricting direct access to the underlying tables and improving security.
        - Procedures encapsulate logic that can be reused across multiple applications or modules, reducing redundancy.
        - Changes to database logic can be made in the stored procedure without altering the application code.
        - Centralized logic in stored procedures ensures uniform behavior across applications using the same database.
    - Disadvantages
        - Maintaining large and complex stored procedures can become difficult, especially in teams unfamiliar with the database language.
        - Procedures are often written in database-specific languages (e.g., PL/SQL for Oracle, T-SQL for SQL Server), making migrations to other databases more challenging.
        - Debugging stored procedures is less straightforward compared to application code due to limited tools and debugging capabilities.
        - Hardcoding business logic in stored procedures can make it less adaptable to changes compared to keeping logic in the application layer.
        - Managing stored procedure versions is more challenging than managing application code, especially in distributed environments.
        - Overusing stored procedures may lead to increased memory and processing overhead on the database server, affecting performance for other queries.
    - When to Use Stored Procedures
        - Use Cases:
            - Business-critical transactions requiring optimal performance.
            - Scenarios with complex database logic shared by multiple applications.
            - Environments where minimizing network traffic is essential.
        - Avoid:
            - When application logic is expected to change frequently.
            - In highly portable systems where database independence is a priority.
- View vs Stored procedure
    - Views: Views are used to create virtual tables that represent the result set of a SELECT query. They provide a way to simplify complex queries, abstract underlying table structures, and present data in a customized format. Views do not store data themselves but provide a logical representation of data from one or more tables.
    - Stored Procedures: Stored procedures are precompiled and stored blocks of SQL code that can contain a sequence of SQL statements, control-of-flow statements, and programming constructs. They are used to encapsulate and execute a set of SQL operations, often implementing business logic, data manipulation, or data validation tasks.
