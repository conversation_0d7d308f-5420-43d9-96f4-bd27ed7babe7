Q. Approach to convert legacy application into Microservices

Q. Single application server converted into multiple servers by horizontal scale to manage load balancing, any changes we have to do wrt to session management? 

Q. Various deployment strategy 
 - Feature Flag - LaunchDarkly 
 - Blue-Green Deployment 
    - Blue for current and Green for new version
 - Rolling Deployment 
    - Gradually replace old version with new version 
 - Canary Deployment 
    - Releases the new version to a small subset of users or servers before a full rollout.
 - Progressive Deployment 
    - Extends Canary deployment with automated monitoring and gradual rollout to more users or environments.
 - A/B Testing Deployment 
    - Similar to canary but focuses on running multiple versions of the application simultaneously to test user behavior.
 - Shadow Deployment 
    - The new version runs alongside the current version without serving production traffic.


### Choosing the Right Strategy
- **Zero downtime**	Blue-Green, Canary, Shadow
- **Cost-efficient deployment**	Rolling, Rolling with Batches
- **High user traffic**	Canary, <PERSON>, Feature Toggles
- **Real-world testing**	Shadow, A/B Testing
- **Low infrastructure complexity**	Recreate, Rolling
- **Quick rollback**	Blue-Green, Feature Toggles


##############################################

----------------------------------------------
2024-12-02
----------------------------------------------

Q1. What are the key differences between RESTful and GraphQL APIs? In what scenarios would you choose one over the other for a SaaS platform?

Q2. Imagine a scenario where the backend services experience a performance bottleneck during peak traffic. How would you identify and resolve the issue? Provide a step-by-step plan for optimization.

Q3. Write a Ruby method that takes an array of integers and returns the maximum sum of any contiguous subarray (Kadane's Algorithm). Include tests to validate your solution.

Kadane's Algorithm is an efficient way to find the maximum sum of a contiguous subarray in an array of integers. It solves the problem in O(n) time complexity, making it optimal for large arrays.

Key Idea<br/>
The algorithm maintains two variables as it iterates through the array:<br/>
     - 1. Current Sum (current_sum): The maximum sum of the subarray that ends at the current position.<br/>
     - 2. Maximum Sum (max_sum): The overall maximum sum encountered so far.<br/>
At each step, it decides whether to:<br/>
    - • Add the current element to the existing subarray (current_sum + element), or<br/>
    - • Start a new subarray with the current element (element).<br/>
The larger of the two becomes the new current_sum.<br/>
Time and Space Complexity<br/>
    - • Time Complexity: O(n), as the array is traversed only once.<br/>
    - • Space Complexity: O(1), since the algorithm uses only a fixed amount of extra memory.<br/>



```ruby
def kadane_max_subarray_sum(arr)
  return 0 if arr.empty?

  max_sum = arr[0]
  current_sum = arr[0]

  kadane_arr = []

  arr[1..].each do |num|
    #puts num
    current_sum = [num, current_sum + num].max
    if current_sum > max_sum
      kadane_arr << num
      max_sum = [max_sum, current_sum].max
    end
   
  end

  max_sum
  #puts kadane_arr.inspect 
end

puts kadane_max_subarray_sum([-2, 1, -3, 4, -1, 2, 1, -5, 4]) # Output: 6
puts kadane_max_subarray_sum([1, 2, 3, 4])                   # Output: 10
puts kadane_max_subarray_sum([-4, -1, -7, -8])               # Output: -1
puts kadane_max_subarray_sum([-2, 3, 1, -4, 5, 2, -1, 2]) # Output: 8
puts kadane_max_subarray_sum([1,-3,2,1,-1]) # Output: 3

puts kadane_max_subarray_sum([-1, 2, 3, -5, 6, 7, -7, 4,-1]) #Output 13
puts kadane_max_subarray_sum([-1, 2, 3, -5, 6, 7, -7, 8,-1]) #Output 14


```

Q4. You are tasked with migrating a legacy monolithic Ruby on Rails application to a microservices architecture. What steps would you take to ensure a smooth transition? Highlight potential challenges and how you would address them.

Q5. As a team leader, how would you handle a situation where one of your team members is consistently missing deadlines, impacting the overall delivery? Provide an example of how you have successfully managed a similar scenario in the past.

----------------------------------------------
2024-11-29
----------------------------------------------
Q1. Write a Ruby on Rails method to handle a common REST API endpoint. Suppose you are given a GET /api/orders/:id endpoint that retrieves order details for a specific order. Implement the Rails controller method to handle this endpoint. The order should include details about items, pricing, and the user associated with the order. Consider using eager loading to optimize for performance.

```ruby
# REST API endpoint - /api/orders/:id - to get order details like items, pricing etc based on id param. Controller method to handle such things.
module Api
  class OrdersController < ApplicationController
    # GET /api/orders/:id
    def show
      # Find the order by its ID
      order = Order.find_by(id: params[:id])

      if order
        # Return the order details as JSON
        render json: { status: "success", data: order }, status: :ok
      else
        # Handle case where order is not found
        render json: { status: "error", message: "Order not found" }, status: :not_found
      end
    rescue StandardError => e
      # Handle any unexpected errors
      render json: { status: "error", message: e.message }, status: :internal_server_error
    end
  end
end

```
Q2. Explain how you manage state between React components in a large-scale application. How do you handle state when passing data from a parent component to deeply nested child components?

Q3. Describe a time when you collaborated with developers to bring a design to life. How did you handle any discrepancies between the initial design and the final implementation? What tools or methods did you use to ensure accurate design implementation?

Q4. The tech landscape is constantly evolving, with new design trends and technologies emerging frequently. How do you stay updated on emerging design trends, and can you provide an example of a recent design technology or trend that you have successfully implemented in a project?

Q5. You are tasked with conducting a usability evaluation for a newly implemented feature. Describe the process you would follow, the types of data you would collect, and how you would use this information to iterate on the design.

----------------------------------------------
2024-11-08
----------------------------------------------
Q1. Describe your recent experience with similar projects

Q2. In a high-traffic application using SQL Server (or similar RDBMS), what specific strategies would you use to improve database performance? Describe your approach to indexing, caching, and optimizing complex queries. How would you handle database scaling to ensure continued performance as data grows?

Q3. Imagine you are leading the architecture for a customer-facing e-commerce application that experiences high traffic during peak sale events. Midway through a major sale, you notice the system is slowing down due to high database load and increased API response times. How would you identify and address the root cause of these performance issues? What changes would you consider in the architecture to handle such high traffic events more smoothly in the future?

Q4. Imagine you’re working on a Ruby on Rails application that is experiencing rapid user growth, and you’re beginning to encounter performance bottlenecks, particularly with response times and database queries. How would you approach identifying and optimizing performance issues within a Rails application? What specific Rails tools, techniques, or design patterns would you apply to handle high traffic, optimize database queries, and manage caching?

Q5. You're tasked with creating a Rails API endpoint that accepts a list of order IDs and marks each order as "processed." Each order should only be processed if it hasn’t already been marked as such. Write a Rails controller action in Ruby that: Accepts a JSON array of order_ids. For each order_id, checks if the order is already marked as "processed" in the database. If not, marks it as "processed." Ensure that your code is efficient, handling cases where there might be a large number of order_ids submitted at once.

```ruby

input:
{
  "order_ids": [101, 102, 301, 432, 589, 690]
}
output:
{
  "message": "3 orders marked as processed."
}


class OrdersController < ApplicationController
  def process_orders
    order_ids = params[:order_ids] # Expecting a JSON array of order IDs

    # Fetch orders that are not already marked as processed
    orders_to_process = Order.where(id: order_ids, processed: false)

    # Update orders in a batch to mark them as processed
    updated_count = orders_to_process.update_all(processed: true, processed_at: Time.current)

    if updated_count.positive?
      render json: { message: "#{updated_count} orders marked as processed." }, status: :ok
    else
      render json: { message: "No orders were updated." }, status: :unprocessable_entity
    end
  rescue => e
    render json: { error: "An error occurred: #{e.message}" }, status: :internal_server_error
  end
end

```

