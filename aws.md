- Serverless and Server based cloud applications
    - Serverless: In a serverless architecture, the cloud provider manages the infrastructure, automatically provisioning and scaling resources as needed. Developers focus on writing code and defining functions, without worrying about the underlying servers.
        - AWS Services:
            - AWS Lambda: Serverless compute service to run code in response to events.
            - Amazon API Gateway: Create and manage APIs that trigger Lambda functions.
            - AWS Step Functions: Coordinate multiple AWS services into serverless workflows.
            - Amazon DynamoDB: Fully managed NoSQL database service that integrates well with serverless applications.
            - Amazon S3: Object storage service for storing files and data, often used in serverless architectures.
            - Amazon EventBridge: Serverless event bus service to connect applications using events.
        - Advantages:
            - Reduced Operational Overhead: No need to manage servers, leading to simplified operations.
            - Cost-Efficiency: Pay only for the actual compute time used.
            - Scalability: Automatic scaling handles varying workloads seamlessly.
            - Rapid Development: Focus on writing code and deploying functions quickly.
        - Use Cases:
            - Microservices: Building small, independently deployable services.
            - Data Processing: Real-time or batch processing of data streams.
            - Mobile Backends: APIs and backend services for mobile applications.
            - IoT Applications: Managing and processing data from IoT devices.
            - Event-Driven Applications: Responding to events such as database changes or incoming messages.
    - Serverbase: In a server-based architecture, applications are deployed on virtual machines or containers where developers have control over the server environment, including operating system, middleware, and runtime.
        - AWS Services:
            - Amazon EC2 (Elastic Compute Cloud): Virtual servers in the cloud with full control over the OS and runtime.
            - Amazon ECS (Elastic Container Service): Container orchestration service for deploying and managing containers.
            - Amazon EKS (Elastic Kubernetes Service): Managed Kubernetes service for running containerized applications.
            - Amazon RDS (Relational Database Service): Managed relational databases.
            - Elastic Load Balancing (ELB): Distribute traffic across multiple servers to ensure availability and reliability.
        - Advantages:
            - Full Control: Complete control over the server environment, allowing for custom configurations and optimizations.
            - Persistent State: Easier to maintain state within the application without external services.
            - Compatibility: Suitable for applications that require specific OS-level configurations or legacy applications that cannot be easily re-architected.
        - Use Cases:
            - Enterprise Applications: Complex, monolithic applications requiring custom configurations.
            - Legacy Applications: Applications that cannot be easily adapted to a serverless model.
            - High-Performance Computing: Workloads requiring specific hardware configurations or low-latency networking.
            - Custom Middleware: Applications requiring specific middleware or runtime environments that are not supported by serverless platforms.

        ![alt text](<images/serverless-vs-serverbase.png>)

- What is an AWS Security Group?
    - An AWS Security Group acts as a virtual firewall for your EC2 instances to control inbound and outbound traffic. You can define rules to allow or deny specific types of traffic, such as HTTP, SSH, or custom protocols, based on source or destination IP addresses and port numbers.
- How does an AWS Security Group differ from a Network ACL (NACL)?
    ![alt text](<images/aws-security-group-vs-nacl.png>)
- What is the default behavior of a security group?
    - Inbound Traffic: Denied by default; you need to explicitly allow traffic.
    - Outbound Traffic: Allowed by default; you can restrict it with custom rules.
- How many rules can you add to a security group?
    - A single security group supports up to 60 inbound rules and 60 outbound rules by default.
    - This limit can be increased up to 125 rules per direction by requesting a service limit increase.
- Can an EC2 instance be associated with multiple security groups?
    - Yes, an EC2 instance can be associated with up to five security groups. The rules from all associated groups are aggregated to determine the allowed traffic.
- What happens if you remove all rules from a security group?
    - Inbound Traffic: All inbound traffic is blocked.
    - Outbound Traffic: All outbound traffic is blocked (except for the default behavior of allowing all outbound traffic in certain cases).
- Can security groups be used across regions?
    - No, security groups are region-specific. You cannot use a security group created in one region for instances in another region.
- Can security group rules reference another security group?
    - Yes, security group rules can reference another security group as a source or destination. This allows you to control traffic between instances associated with different security groups.
- How would you troubleshoot connectivity issues related to security groups?
    - Verify Security Group Rules: Check the inbound and outbound rules for the necessary ports and protocols.
    - Check VPC and Subnet Configuration: Ensure the instances are in the correct subnet and VPC.
    - Validate Instance Associations: Confirm the instance is associated with the correct security group.
    - Use Network Troubleshooting Tools: Use telnet, curl, or AWS Reachability Analyzer to test connectivity.
    - Monitor CloudWatch Logs: Inspect logs for connection rejections due to security group misconfigurations.
- Can you block specific IP addresses using a security group?
    - No, security groups support only "allow" rules. To block specific IP addresses, you must use a Network ACL (NACL).
- What happens if you delete a security group currently in use?
    - You cannot delete a security group that is actively associated with an instance. You must first detach the security group from all instances before deleting it.
- What is the difference between inbound and outbound rules in a security group?
    - Inbound Rules
        - Control incoming traffic to instances.
        - Allow HTTP traffic (port 80) from anywhere.
    - Outbound Rules
        - Control outgoing traffic from instances.
        - Allow outbound database traffic to a specific IP.
- How do you restrict SSH access to an EC2 instance?
    - Update the inbound rule of the associated security group.
    - Allow port 22 only from trusted IP addresses (e.g., your office IP)
- What are some security best practices for security groups?
    - Use the principle of least privilege; allow only necessary traffic.
    - Restrict SSH and RDP access to specific IP addresses.
    - Regularly audit security group rules.
    - Avoid using the default security group.
    - Use descriptive names and tags for security groups.
    - Monitor changes to security groups using AWS CloudTrail.
- How can you automate security group management?
    - You can use Infrastructure as Code (IaC) tools like:
        - AWS CloudFormation: Define security groups as part of your stack.
        - Terraform: Manage security groups declaratively.
        - AWS CLI/SDK: Programmatically create or update security groups.
- What are ephemeral ports, and why are they important in security group rules?
    - Ephemeral ports are temporary ports assigned by the operating system for client-server communication (e.g., responses to requests).
    - AWS recommends allowing ephemeral ports (1024–65535) in outbound rules for applications that require dynamic connections.

- What is AWS Lambda?
    - AWS Lambda is a serverless compute service that allows users to run code without provisioning or managing servers. It automatically scales based on demand and is event-driven, meaning it executes code in response to AWS events like S3 uploads, DynamoDB updates, or API Gateway requests.

- How does AWS Lambda work?
    - You upload your code to AWS Lambda or write it in the AWS Lambda console.
    - You define an event source (such as an API Gateway, S3 event, or DynamoDB stream).
    - When the event occurs, AWS Lambda automatically triggers your function.
    - AWS Lambda runs your function in an isolated environment and scales as needed.
    - Once execution is complete, it shuts down the environment.

- Key Feature of AWS Lambda
    - Serverless: No need to provision or manage servers.
    - Event-Driven: Executes based on triggers from AWS services.
    - Auto-Scaling: Automatically adjusts to workload demand.
    - Pay-per-Use: Charges only for execution time.
    - Multiple Language Support: Supports Python, Node.js, Java, Go, .NET, and more.
    - Integrated with AWS Services: Works with S3, DynamoDB, API Gateway, CloudWatch, etc.

- What are the supported programming languages in AWS Lambda?
    - Node.js, Python, Java, Go, C# (.NET Core), Ruby

- What are the different ways to trigger AWS Lambda?
    - AWS Lambda can be triggered by various event sources, including:
        - API Gateway (for HTTP requests)
        - Amazon S3 (on file upload or deletion)
        - Amazon DynamoDB Streams (on data changes)
        - Amazon SNS (on topic messages)
        - Amazon SQS (for queue processing)
        - Amazon CloudWatch Events (for scheduled tasks)
        - AWS Step Functions (for workflow automation)

- What is the maximum execution time for an AWS Lambda function?
    - The maximum execution time for a Lambda function is 15 minutes (900 seconds) per invocation.

- Can AWS Lambda run continuously?
    - No, AWS Lambda has a maximum execution time of 15 minutes. For continuous tasks, consider:
    - AWS Step Functions (for chaining multiple Lambdas).
    - Amazon EC2 or ECS (for long-running processes).    

- How does AWS Lambda scale?
    - AWS Lambda automatically scales horizontally by running multiple instances of the function in parallel. If a function is invoked multiple times simultaneously, AWS Lambda spins up new instances as needed.

- How does AWS Lambda pricing work?
    - AWS Lambda pricing is based on:
        - Compute Time: Charged per millisecond of execution time.
        - Memory Allocation: Higher memory means a higher cost.
        - Number of Requests: Free tier includes 1 million requests per month.

- What is AWS SAM?
    - AWS Serverless Application Model (SAM) is a framework for deploying serverless applications, including Lambda, API Gateway, and DynamoDB. It simplifies deployment with YAML templates.

- How do you optimize AWS Lambda performance?
    - Reduce cold starts using Provisioned Concurrency.
    - Minimize package size by excluding unnecessary dependencies.
    - Use Lambda Layers to manage dependencies efficiently.
    - Optimize memory allocation (higher memory improves CPU performance).
    - Enable function caching using Amazon ElastiCache or DynamoDB.    

- Does php support in aws lambda?
    - PHP is not natively supported in AWS Lambda, but you can run PHP on Lambda using custom runtimes or Lambda Layers.
    - While AWS Lambda does not natively support PHP, you can use Bref or custom runtimes to deploy PHP applications successfully. 
    - https://bref.sh/