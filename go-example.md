### You want to build a system that can support multiple payment gateways (like PayPal, Stripe, Razorpay). 
### Each has different internal logic, but your app should treat them uniformly.
```go
package main

import "fmt"

// PaymentGateway interface
type PaymentGateway interface {
	Pay() error
}

// Stripe implements PaymentGateway
type Stripe struct {
	AccountID string
	Amount    float64
}

func (s Stripe) Pay() error {
	fmt.Printf("Paid ₹%.2f using Stripe (Account: %s)\n", s.Amount, s.AccountID)
	return nil
}

// PayPal implements PaymentGateway
type PayPal struct {
	Email  string
	Amount float64
}

func (p PayPal) Pay() error {
	fmt.Printf("Paid ₹%.2f using PayPal (Email: %s)\n", p.Amount, p.Email)
	return nil
}

func ProcessPayment(pg PaymentGateway) {
	err := pg.Pay()
	if err != nil {
		fmt.Println("Payment failed:", err)
	}
}

func main() {
	stripe := Stripe{AccountID: "stripe_acc_123", Amount: 499.99}
	paypal := PayPal{Email: "<EMAIL>", Amount: 899.50}

	ProcessPayment(stripe)
	ProcessPayment(paypal)

	// Or use in a slice of PaymentGateway (polymorphism in action)
	payments := []PaymentGateway{stripe, paypal}
	for _, pg := range payments {
		ProcessPayment(pg)
	}
}


/* Command 
go run main.go
OR 
go run .
OR 
go build main.go
./main 

*/

/* Output
Paid ₹499.99 using Stripe (Account: stripe_acc_123)
Paid ₹899.50 using PayPal (Email: <EMAIL>)
Paid ₹499.99 using Stripe (Account: stripe_acc_123)
Paid ₹899.50 using PayPal (Email: <EMAIL>)
*/

```
---------------------------------------------------------------------------------------------------------------------
### References
---------------------------------------------------------------------------------------------------------------------
- https://go.dev/learn/
- https://go.dev/doc/install
- https://go.dev/doc/tutorial/getting-started
- https://pkg.go.dev/search?q=quote
- https://go.dev/doc/modules/developing
- https://go.dev/doc/tutorial/workspaces
