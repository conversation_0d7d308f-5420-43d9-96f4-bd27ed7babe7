- https://www.djangoproject.com/
- https://flask.palletsprojects.com/en/stable/
- https://fastapi.tiangolo.com/
- https://www.fullstack.cafe/interview-questions/python

- Python is an interpreted language. That means that, unlike languages like C and its variants, Python does not need to be compiled before it is run. Other interpreted languages include PHP and Ruby.
- Python is dynamically typed, this means that you don't need to state the types of variables when you declare them or anything like that. You can do things like x=111 and then x="I'm a string" without error
- Python is well suited to object orientated programming in that it allows the definition of classes along with composition and inheritance. Python does not have access specifiers (like C++'s public, private), the justification for this point is given as "we are all adults here"
- In Python, functions are first-class objects. This means that they can be assigned to variables, returned from other functions and passed into functions. Classes are also first class objects
- Writing Python code is quick but running it is often slower than compiled languages. Fortunately, Python allows the inclusion of C based extensions so bottlenecks can be optimised away and often are. The numpy package is a good example of this, it's really quite quick because a lot of the number crunching it does isn't actually done by Python

- Python Version History
-   | Year      | Version(s) Released             | Key Features / Notes                                                 |                                     |
	| --------- | ------------------------------- | -------------------------------------------------------------------- | ----------------------------------- |
	| 1991      | Python 0.9.0                    | First public release by Guido van Rossum                             |                                     |
	| 1994      | Python 1.0                      | Functional programming tools: `lambda`, `map()`, `filter()`          |                                     |
	| 2000      | Python 2.0                      | List comprehensions, garbage collection (GC) via reference counting  |                                     |
	| 2001–2009 | Python 2.x series               | Continued 2.x improvements (Unicode, decorators, etc.)               |                                     |
	| 2008      | Python 3.0 (aka Python 3000)    | Not backward compatible; improved Unicode, print as function, etc.   |                                     |
	| 2010      | Python 3.1, 2.7                 | `argparse`, ordered dictionaries (3.1); last major 2.x version (2.7) |                                     |
	| 2011      | Python 3.2                      | `concurrent.futures`, `lzma`, PEP 3148                               |                                     |
	| 2012      | Python 3.3                      | `yield from`, `venv`, `faulthandler`                                 |                                     |
	| 2014      | Python 3.4                      | `asyncio` (PEP 3156), `pathlib`, `enum`, `statistics`                |                                     |
	| 2015      | Python 3.5                      | `async` / `await` syntax, `typing`, `matrix multiplication` (`@`)    |                                     |
	| 2016      | Python 3.6                      | f-strings, underscores in numeric literals, `secrets` module         |                                     |
	| 2018      | Python 3.7                      | `dataclasses`, postponed evaluation of annotations (PEP 563)         |                                     |
	| 2019      | Python 3.8                      | Assignment expressions (`:=`), positional-only args                  |                                     |
	| 2020      | Python 3.9                      | Dictionary merge (\`                                                 | `), new string methods, `zoneinfo\` |
	| 2021      | Python 3.10                     | Structural pattern matching (`match` / `case`), precise error msgs   |                                     |
	| 2022      | Python 3.11                     | Performance improvements (\~10–60%), fine-grained error locations    |                                     |
	| 2023      | Python 3.12                     | More perf boosts, removal of old deprecated features                 |                                     |
	| 2024      | Python 3.13 (expected Oct 2024) | In development, continues perf, simplification, security work        |                                     |
	| 2025      | Python 3.14 (planned)           | Ongoing development, nothing finalized yet                           |                                     |


- Django Version History
- 	| Year     | Version(s) Released   | Key Features / Notes                                                           |
	| -------- | --------------------- | ------------------------------------------------------------------------------ |
	| **2005** | Django 0.90           | First public release (July 21, 2005)                                           |
	| **2006** | Django 0.95           | Improved ORM, initial admin                                                    |
	| **2008** | Django 1.0            | Stable 1.x API, middleware, generic views, admin overhaul                      |
	| **2009** | Django 1.1            | Aggregates in ORM, new-style middleware                                        |
	| **2010** | Django 1.2            | CSRF protection, multiple DB support                                           |
	| **2011** | Django 1.3            | Class-based views (CBVs), static files framework                               |
	| **2012** | Django 1.4            | Time zone support, new project layout                                          |
	| **2013** | Django 1.5, 1.6       | Custom user models (1.5), improved transactions (1.6)                          |
	| **2014** | Django 1.7            | Built-in migrations via South, AppConfig system                                |
	| **2015** | Django 1.8 (LTS), 1.9 | Template engine refactor (Jinja2 support), first LTS                           |
	| **2016** | Django 1.10           | New middleware style, improved security                                        |
	| **2017** | Django 1.11 (LTS)     | New style CBVs, compatibility for long-term usage                              |
	| **2018** | Django 2.0, 2.1       | Python 3 only, URL path converters, `path()` and `re_path()`                   |
	| **2019** | Django 2.2 (LTS), 3.0 | `TextChoices`, ASGI support begins (in 3.0)                                    |
	| **2020** | Django 3.1            | Async views, middleware, database backends                                     |
	| **2021** | Django 3.2 (LTS)      | `@cached_property` on models, Redis cache, default auto field = `BigAutoField` |
	| **2022** | Django 4.0, 4.1       | Functional unique constraints, ORM expressions improvements, async handlers    |
	| **2023** | Django 4.2 (LTS)      | Async ORM support, formset validation, Redis session backend                   |
	| **2024** | Django 5.0            | Removal of deprecated features, full async signal support                      |
	| **2025** | Django 5.1 (planned)  | Expected in August 2025 (focus on performance, async maturity)                 |


- Flask Version History
- 	| Year     | Version(s) Released  | Key Features / Notes                                                                    |
	| -------- | -------------------- | --------------------------------------------------------------------------------------- |
	| **2010** | Flask 0.1            | 🎉 Initial public release by Armin Ronacher                                             |
	| **2011** | Flask 0.6 – 0.7      | Improved request handling, `flaskext` namespace introduced                              |
	| **2012** | Flask 0.8 – 0.9      | Request hooks, `g` object improvements, testing enhancements                            |
	| **2013** | Flask 0.10           | Minor improvements, better debugger, blueprint enhancements                             |
	| **2014** | Flask 0.11           | New CLI (`flask run`), delayed app loading, better blueprints                           |
	| **2015** | Flask 0.12           | Python 3 support stabilization, minor bug fixes                                         |
	| **2018** | Flask 1.0            | First major version! Built-in CLI, better error handling, `app.test_client()`           |
	| **2019** | Flask 1.1            | Async support begins (limited), better JSON support, PEP 8 compliance                   |
	| **2021** | Flask 2.0            | Full Python 3.6+ only, async route support, decorators updated, `@before_request` async |
	| **2022** | Flask 2.1 – 2.2      | Support for `slots`, better debugging, `app.json` customization                         |
	| **2023** | Flask 2.3            | Deprecation cleanup, improved async handling, `with_appcontext()` decorator             |
	| **2024** | Flask 3.0 (May 2024) | **Fully async-first** design, removal of legacy APIs, Python 3.8+ only                  |
	| **2025** | Flask 3.x (upcoming) | Future improvements to async routing, type hints, extensions                            |

- FastAPI Version History
- 	| Year     | Version(s) Released                  | Key Features / Notes                                                                                                                    |
	| -------- | ------------------------------------ | --------------------------------------------------------------------------------------------------------------------------------------- |
	| **2018** | `0.1.x – 0.10.x` (Initial release)   | 🔹 Created by **Sebastián Ramírez** <br>🔹 Async support from the start <br>🔹 Pydantic-based data validation                           |
	| **2019** | `0.11.x – 0.42.x`                    | 🔹 Rapid growth in popularity <br>🔹 Built-in **automatic OpenAPI (Swagger UI)** <br>🔹 Dependency injection system added               |
	| **2020** | `0.50.x – 0.61.x`                    | 🔹 First major adoption by companies <br>🔹 OAuth2 + JWT examples <br>🔹 Starlette integration improved                                 |
	| **2021** | `0.62.x – 0.68.x`                    | 🔹 Background tasks enhancements <br>🔹 WebSocket support matured <br>🔹 Type hint improvements                                         |
	| **2022** | `0.69.x – 0.79.x`                    | 🔹 Dependency override improvements <br>🔹 JSON serialization updates <br>🔹 GitHub stars exploded (60k+)                               |
	| **2023** | `0.80.x – 0.100.x+`                  | 🔹 `Annotated` support (PEP 593) <br>🔹 Dependency injection improvements <br>🔹 FastAPI Users v10 (external)                           |
	| **2024** | `0.101.x – 0.110.x`                  | 🔹 TypeAdapter (via Pydantic v2) integration <br>🔹 Improved startup/shutdown handling <br>🔹 Refined WebSocket routing                 |
	| **2025** | ⏳ `1.0.0` **Expected** mid–late 2025 | 🛠️ Under active development <br>🔹 Core refactor for FastAPI 1.0 <br>🔹 Full Pydantic v2 adoption <br>🔹 Improved testing architecture |


- Flask vs Django vs FastAPI — Comparison Table
- 	| Feature                   | **Django**                               | **Flask**                                | 		**FastAPI**                                 |
	| ------------------------- | ---------------------------------------- | ---------------------------------------- | ------------------------------------------- |
	| **Initial Release**       | 2005                                     | 2010                                     | 2018                                        |
	| **Type**                  | Full-stack web framework                 | Microframework                           | Modern async API framework                  |
	| **Language**              | Python                                   | Python                                   | Python                                      |
	| **Design Philosophy**     | "Batteries included"                     | Minimal, extendable                      | Modern, async-first, API-centric            |
	| **Routing**               | URL patterns, class-based views          | Simple function decorators               | Type-safe function decorators               |
	| **ORM**                   | Built-in (Django ORM)                    | Optional (e.g. SQLAlchemy)               | Optional (e.g. SQLAlchemy, Tortoise)        |
	| **Admin Panel**           | Yes (Auto-generated)                     | No (3rd party needed)                    | No (3rd party or custom needed)             |
	| **Templating**            | Built-in (Django Templates)              | Built-in (Jinja2)                        | Optional (Jinja2 or others)                 |
	| **Forms Handling**        | Django Forms, ModelForms                 | WTForms (3rd party)                      | Depends on Pydantic or manual forms         |
	| **Authentication System** | Built-in (auth, permissions, sessions)   | 3rd party extensions                     | OAuth2 / JWT with `fastapi-users`, etc.     |
	| **Async Support**         | Full support since 3.1+ (views, ORM WIP) | Basic since 2.0, better in 3.0+          | Built-in (async by default)                 |
	| **Performance**           | Medium                                   | High (but sync)                          | 🔥 Very high (async-native)                 |
	| **API Development**       | REST via Django REST Framework           | Flask-RESTful or Flask-RestX (3rd party) | First-class API support with OpenAPI docs   |
	| **Swagger/OpenAPI**       | With DRF or 3rd party                    | Flask-RESTX or custom setup              | Built-in (OpenAPI + Swagger UI)             |
	| **Learning Curve**        | Moderate (many conventions)              | Low (minimal core)                       | Moderate (if familiar with typing/Pydantic) |
	| **Use Cases**             | Full websites, admin apps                | Lightweight web apps, MVPs               | Modern APIs, microservices, ML apps         |
	| **Community / Ecosystem** | Very large, mature                       | Large, flexible                          | Growing fast, strong among API developers   |
	| **LTS Releases**          | Yes                                      | No                                       | No                                          |
	| **Use If**          | You need a full-stack solution with admin, ORM, and tight integration, You're building large-scale apps or CMS-like systems                                      | You prefer flexibility and minimalism, You're building something lightweight or microservices                                       | You're building high-performance APIs, You want async-first support and automatic docs (Swagger)                                          |

- Django and Python Compatibility Table
- 	| Django Version | Supported Python Versions  | Notes                                              |
	| -------------- | -------------------------- | -------------------------------------------------- |
	| 5.0            | 3.10, 3.11, 3.12           | Latest stable release (Dec 2023)                   |
	| 4.2 (LTS)      | 3.8, 3.9, 3.10, 3.11, 3.12 | LTS support until at least April 2026              |
	| 4.1            | 3.8, 3.9, 3.10, 3.11       | Security support ended in April 2024               |
	| 4.0            | 3.8, 3.9, 3.10             | Ended support Dec 2022                             |
	| 3.2 (LTS)      | 3.6, 3.7, 3.8, 3.9         | LTS ended April 2024                               |
	| 3.1            | 3.6, 3.7, 3.8              | Ended support April 2022                           |
	| 3.0            | 3.6, 3.7, 3.8              | Ended support April 2021                           |
	| 2.2 (LTS)      | 3.5, 3.6, 3.7, 3.8         | LTS ended April 2022                               |
	| 2.1            | 3.5, 3.6, 3.7              | Ended support Dec 2019                             |
	| 2.0            | 3.4, 3.5, 3.6, 3.7         | Ended support April 2019                           |
	| 1.11 (LTS)     | 2.7, 3.4, 3.5, 3.6         | Last version supporting Python 2.7, EOL April 2020 |


- 

---------------------------------------------------------------------------------------------------------------------
---------------------------------------------------------------------------------------------------------------------

---------------------------------------------------------------------------------------------------------------------
### routes qs?
---------------------------------------------------------------------------------------------------------------------
- How are URLs mapped to views in Django?
	- In Django, URL routing is defined in urls.py files using the path() or re_path() functions
	- ```python
		from django.urls import path
		from . import views

		urlpatterns = [
			path('hello/', views.hello_view),
		]
	```
	- This maps the /hello/ URL to the hello_view function in views.py
- How does routing work in Flask?
	- In Flask, routes are defined using the @app.route() decorator:
	- ```python
		from flask import Flask
		app = Flask(__name__)

		@app.route("/hello")
		def hello():
		return "Hello, World!"
	```
	- The URL /hello is routed to the hello() function. You can also define route parameters using <param> syntax, like /user/<username>.

- How are path parameters handled in FastAPI?
	- FastAPI uses Python type hints for route parameters
	- ```python
		from fastapi import FastAPI
		app = FastAPI()

		@app.get("/items/{item_id}")
		def read_item(item_id: int):
			return {"item_id": item_id}
	```
	- Here, item_id is extracted from the path and validated as an int automatically. FastAPI uses Pydantic for data validation.

- What is Pydantic?
	- Pydantic is a data validation and parsing library for Python, built on top of Python type hints.
	- Key Features
		- 	| Feature                  | Description                                                             |
			| ------------------------ | ----------------------------------------------------------------------- |
			| **Type safety**          | Uses Python's `typing` system (`str`, `int`, `List`, `Optional`, etc.)  |
			| **Automatic validation** | Validates and converts incoming data (e.g., string `"123"` → int `123`) |
			| **Error messages**       | Provides structured, human-readable validation errors                   |
			| **Serialization**        | Easily serialize to/from `dict`, JSON                                   |
			| **Nested models**        | Supports deeply nested models and reuse                                 |
			| **Fast**                 | Built for performance with `dataclasses`, Cython, and caching           |


- Can Django handle method-based routing like GET/POST inside urls.py?"
	- No, method routing is handled inside the view functions via request.method. However, class-based views (CBVs) like View, TemplateView, or APIView allow defining methods like get(), post(), etc., for routing by HTTP verb.

- What is a wildcard or catch-all route?
	- A wildcard route captures all URL patterns that haven't been matched earlier
	- Django: Use regex or path("<path:subpath>/")
	- Flask: @app.route('/<path:path>')
	- FastAPI: @app.get("/{full_path:path}")

- What is reverse URL resolution (Django specific)?
	- Django allows you to reference URLs by name, not hardcoded strings. This improves maintainability.
	- ```python
		# urls.py
		path('profile/<int:id>/', views.profile_view, name='profile')

		# In templates or views
		reverse('profile', args=[10])  # returns /profile/10/
	```

- What is a route prefix or blueprint?
	- Flask: Blueprints group routes under a common prefix.
	- Django: Nested include() can group URL patterns.
	- FastAPI: APIRouter() allows route grouping and prefixing.
	- ```python
		router = APIRouter(prefix="/users")
		@router.get("/")
		def list_users():
			return [...]

		app.include_router(router)
	```
- Compare how routing is handled in Django, Flask, and FastAPI
	- 	| Framework | Routing Mechanism                            | Example                     |
		| --------- | -------------------------------------------- | --------------------------- |
		| Django    | `path()` or `re_path()` in `urls.py`         | `path('blog/', views.blog)` |
		| Flask     | `@app.route()` decorator                     | `@app.route('/home')`       |
		| FastAPI   | `@app.get()`, `@app.post()` with path params | `@app.get('/items/{id}')`   |

- Can you nest routes in any of these frameworks?
	- Flask: Not natively, but can mimic nesting with Blueprints and prefixes.
	- Django: Nest via include() in urls.py.
	- FastAPI: Easily nest routers inside routers using include_router().


---------------------------------------------------------------------------------------------------------------------
### Security technique
---------------------------------------------------------------------------------------------------------------------
# Python Security Interview Questions

## 1. How do you securely handle passwords in Python applications?

**Answer:**  
Use libraries like `bcrypt`, `passlib`, or `hashlib` with secure hashing algorithms (e.g., PBKDF2, bcrypt, scrypt, Argon2). Never store plain-text passwords.

Example with bcrypt:

```python
import bcrypt

hashed = bcrypt.hashpw(b"password123", bcrypt.gensalt())
bcrypt.checkpw(b"password123", hashed)
```

---

## 2. How do you prevent code injection in Python applications?

**Answer:**  
- Avoid `eval()`, `exec()`, and `input()` without sanitization.
- Use parameterized queries with databases.
- Validate and sanitize all external input.

---

## 3. What are common security vulnerabilities in Python applications?

**Answer:**
- Insecure deserialization
- Command injection
- SQL injection
- Path traversal
- XSS (in web apps)
- CSRF (in web apps)
- Insecure use of `pickle`, `yaml.load()`, or `eval`

---

## 4. How can you securely deserialize data in Python?

**Answer:**  
Avoid using `pickle` or use `pickle` with trusted sources only. Prefer `json` for untrusted input. For YAML, use `yaml.safe_load()`.

```python
import yaml
data = yaml.safe_load(untrusted_input)
```

---

## 5. How do you secure file operations in Python?

**Answer:**
- Use `os.path.abspath()` and check paths to prevent directory traversal.
- Sanitize filenames with `secure_filename()` from Flask or similar logic.
- Avoid accepting arbitrary paths from users.

---

## 6. How do you prevent command injection in Python?

**Answer:**  
Use `subprocess.run()` with arguments as a list and `shell=False`.

```python
import subprocess
subprocess.run(["ls", "-l"], check=True)
```

Never pass user input directly into shell commands.

---

## 7. How do you manage secrets in a Python application?

**Answer:**
- Use environment variables
- Use `python-decouple` or `python-dotenv`
- Integrate with secret managers (AWS Secrets Manager, HashiCorp Vault)
- Never hardcode secrets in code or config files

---

## 8. How do you secure web applications built with Python frameworks?

**Answer:**
- Use secure session and CSRF settings
- Apply input validation and output escaping
- Use HTTPS everywhere
- Avoid exposing stack traces in production
- Apply secure headers (HSTS, CSP, etc.)

---

## 9. How do you audit a Python codebase for security issues?

**Answer:**
- Use static analysis tools like `bandit`, `pylint`, `flake8`
- Perform dependency checks with `safety`, `pip-audit`
- Conduct regular code reviews and penetration testing

---

## 10. As a Technical Architect, how do you enforce secure coding practices in Python projects?

**Answer:**
- Establish security coding guidelines
- Mandate use of secure libraries and practices
- Integrate security testing into CI/CD
- Perform threat modeling and risk assessments
- Train developers regularly on security best practices


# Python Django Security Interview Questions 

## 1. What are the primary security features provided by Django?

**Answer:**  
Django provides several built-in security features:
- SQL injection protection via ORM
- Cross-Site Scripting (XSS) protection via template auto-escaping
- Cross-Site Request Forgery (CSRF) protection using CSRF middleware and tokens
- Clickjacking protection via `X-Frame-Options` headers
- Secure password hashing with PBKDF2, Argon2, etc.
- HTTPS support via `SECURE_SSL_REDIRECT`, HSTS

---

## 2. How does Django prevent SQL injection?

**Answer:**  
Django ORM automatically escapes parameters when constructing SQL queries, preventing direct injection. Use parameterized queries and avoid raw SQL unless absolutely necessary.

---

## 3. What is CSRF and how does Django handle it?

**Answer:**  
Cross-Site Request Forgery (CSRF) tricks users into executing unwanted actions. Django uses a CSRF middleware and token system to validate that POST requests come from trusted sources.

In templates:

```html
<form method="post">
  {% csrf_token %}
</form>
```

---

## 4. What are best practices for securing user passwords in Django?

**Answer:**  
- Use `make_password()` and `check_password()` utilities
- Rely on Django's default password hasher (PBKDF2 or Argon2)
- Enforce strong password policies via validators
- Use `AbstractBaseUser` for custom auth systems

---

## 5. How does Django prevent XSS attacks?

**Answer:**  
Django auto-escapes variables in templates by default:

```html
{{ user_input }}  # This will be escaped
```

To deliberately allow HTML, use `|safe`, but this should be avoided unless trusted.

---

## 6. How do you implement HTTPS and secure cookies in Django?

**Answer:**

In `settings.py`:

```python
SECURE_SSL_REDIRECT = True
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
```

---

## 7. What is clickjacking and how is it prevented in Django?

**Answer:**  
Clickjacking tricks users into clicking hidden elements. Django prevents it by setting:

```python
X_FRAME_OPTIONS = 'DENY'
```

You can use the `@xframe_options_exempt` or `@xframe_options_deny` decorators for specific views.

---

## 8. How do you secure file uploads in Django?

**Answer:**

- Validate file type and size before saving
- Store uploaded files in private locations
- Avoid storing executable files
- Use anti-virus scanning tools on uploads
- Sanitize filenames using `django.utils.text.get_valid_filename`

---

## 9. How can you guard against mass assignment or field injection vulnerabilities?

**Answer:**  
Use Django forms or serializers to explicitly define allowed fields. Avoid using `Model.objects.update(**request.POST)` directly without filtering input.

---

## 10. As a Technical Architect, how would you enforce security across a Django project?

**Answer:**

- Apply secure defaults in `settings.py`
- Enforce HTTPS and secure cookies
- Review 3rd party packages for vulnerabilities
- Set up security headers (CSP, HSTS, etc.)
- Regularly run security audits using tools like `bandit` or `safety`
- Implement logging and intrusion detection


# Security-Related Python Flask Interview Questions for Technical Architect Role

## 1. How do you protect a Flask application against Cross-Site Scripting (XSS)?
**Answer:** Use Flask's built-in template rendering (Jinja2), which escapes input by default. Also, validate and sanitize any user-generated content before displaying it. Use CSP headers via Flask-Talisman or similar.

---

## 2. How do you prevent SQL Injection in Flask?
**Answer:** Use parameterized queries via ORM tools like SQLAlchemy. Avoid concatenating SQL strings manually. For raw SQL, always use bound parameters.

---

## 3. How can CSRF attacks be prevented in Flask?
**Answer:** Use Flask-WTF or Flask-SeaSurf to generate CSRF tokens and validate them in every form submission. Set the `WTF_CSRF_ENABLED` configuration to True.

---

## 4. How would you handle authentication securely in Flask?
**Answer:** Use Flask-Login or Flask-JWT-Extended for session or token-based authentication. Store passwords using hashing algorithms like bcrypt (via `werkzeug.security` or `passlib`).

---

## 5. What is Flask-Talisman and how does it help secure a Flask app?
**Answer:** Flask-Talisman is a Flask extension that sets HTTP headers for security, including Content Security Policy (CSP), Strict Transport Security (HSTS), X-Frame-Options, etc.

---

## 6. How do you enforce HTTPS in a Flask app?
**Answer:** Use a reverse proxy (like Nginx) with SSL termination or Flask-Talisman to enforce HTTPS redirection and set HSTS headers.

---

## 7. How would you protect sensitive configuration and secret keys in a Flask project?
**Answer:** Use environment variables to store secrets, not hard-coded strings. Use libraries like `python-decouple` or `dynaconf`, and consider using a secrets manager (AWS Secrets Manager, HashiCorp Vault, etc.).

---

## 8. How can you protect APIs from abuse (rate limiting) in Flask?
**Answer:** Use Flask-Limiter to apply rate limiting to endpoints. It supports various backends like Redis and in-memory.

---

## 9. How would you implement Role-Based Access Control (RBAC) in a Flask application?
**Answer:** Use Flask-Principal or Flask-Security to define roles and manage permissions. Decorators can be used to enforce access control on endpoints.

---

## 10. How do you prevent session hijacking in Flask?
**Answer:** Use secure cookies (`SESSION_COOKIE_SECURE=True`), HttpOnly flags (`SESSION_COOKIE_HTTPONLY=True`), and consider rotating session tokens. Monitor IP/User-Agent if necessary.

---


# Security-Related Python FastAPI Interview Questions for Technical Architect Role

## 1. How do you handle authentication in FastAPI?
**Answer:** FastAPI supports OAuth2 with Password (and hashing) and JWT token authentication. Use `fastapi.security.OAuth2PasswordBearer` for token-based flows, and third-party packages like `fastapi-users` for more comprehensive user management.

---

## 2. How can you implement role-based access control (RBAC) in FastAPI?
**Answer:** Create a dependency that checks the current user's role or permissions. You can use the `Depends` mechanism to enforce role checks on protected routes.

---

## 3. How do you secure API endpoints in FastAPI?
**Answer:** Use dependency injection with `Depends` to include security checks such as authentication, authorization, and scopes. Use OAuth2, JWT tokens, or API key-based systems for endpoint protection.

---

## 4. How can you prevent Cross-Site Request Forgery (CSRF) in FastAPI?
**Answer:** Since FastAPI is typically used with APIs and JWT tokens (stateless), CSRF is less of a concern. However, if using session-based authentication with cookies, CSRF tokens can be added manually using middleware.

---

## 5. How do you prevent SQL Injection in FastAPI?
**Answer:** Use SQLAlchemy or other ORM libraries with parameterized queries. Avoid string interpolation in SQL statements. ORM libraries handle SQL injection protection by design.

---

## 6. How can you enforce HTTPS and secure headers in FastAPI?
**Answer:** Use a reverse proxy like Nginx or Traefik to enforce HTTPS. Additionally, use `starlette.middleware.httpsredirect.HTTPSRedirectMiddleware` and security headers via packages like `Secure` or `fastapi.middleware`.

---

## 7. How would you manage and secure environment secrets in FastAPI?
**Answer:** Use `pydantic.BaseSettings` to manage environment variables securely. Load secrets from `.env` files or a secret manager like AWS Secrets Manager or HashiCorp Vault.

---

## 8. What measures do you take to secure FastAPI against XSS attacks?
**Answer:** Avoid returning raw HTML or unsafe JavaScript. Validate and sanitize user input using libraries like `bleach`. Use response models and built-in escaping features when rendering HTML.

---

## 9. How can you prevent DDoS or brute-force attacks on a FastAPI application?
**Answer:** Implement rate limiting using packages like `slowapi`. Use middleware to restrict excessive requests and combine with CAPTCHA or IP whitelisting as needed.

---

## 10. How do you test the security of a FastAPI application?
**Answer:** Use tools like OWASP ZAP or Postman for API security testing. Write unit and integration tests for authorization and authentication. Also, enable and monitor logs for unauthorized access attempts.

---


---------------------------------------------------------------------------------------------------------------------
### Performance technique
---------------------------------------------------------------------------------------------------------------------

# Django Performance Optimization

## 1. What are the common performance bottlenecks in a Django application?

**Hint:** Think of everything that adds latency to a request.

**Answer:**
- Unoptimized database queries (N+1, lack of indexes)
- Slow template rendering
- Serving static files via Django in production
- Synchronous/blocking code in views
- Missing or ineffective caching
- Overuse or poor ordering of middleware

---

## 2. How do you optimize Django ORM queries?

**Hint:** Look into what causes excessive DB hits.

**Answer:**
- Use `select_related()` for foreign key joins
- Use `prefetch_related()` for many-to-many
- Avoid `.all()` unless necessary
- Use `.only()` and `.defer()` to load fewer fields
- Replace `.count()` with `.exists()` when checking presence
- Add `db_index=True` to frequently filtered fields

---

## 3. What database optimizations would you suggest?

**Hint:** Start from indexing and think about query cost.

**Answer:**
- Add indexes on filtered/sorted fields
- Use `QuerySet.explain()` to analyze slow queries
- Archive large tables
- Use connection pooling
- Avoid unbounded `ORDER BY`

---

## 4. What caching strategies do you recommend?

**Hint:** Think full page, fragments, and data caching.

**Answer:**
- Per-view caching with `@cache_page`
- Template fragment caching with `{% cache %}`
- Low-level caching using `cache.get()` / `cache.set()`
- Redis/Memcached as backend
- Cache querysets or computed results
- Set proper TTL and smart invalidation

---

## 5. How would you design Django for high concurrency?

**Hint:** Think async, load balancing, task queues.

**Answer:**
- Use async views for I/O-heavy endpoints
- Offload tasks to Celery/Django-Q
- Use Gunicorn/Uvicorn behind Nginx
- Enable DB connection pooling
- Horizontal scaling with load balancers
- CDN for static/media

---

## 6. How do you identify performance issues?

**Hint:** Tools, monitoring, and profiling.

**Answer:**
- Django Debug Toolbar / Silk for profiling
- Use `QuerySet.explain()` for query plan
- Use cProfile, line_profiler
- Monitor with New Relic, Sentry, Datadog
- Track DB time, cache hit ratio, memory usage

---

## 7. What role does async play in Django performance?

**Hint:** Compare sync vs async behavior in request handling.

**Answer:**
- Async views allow more concurrent requests with fewer threads
- Best for I/O-bound tasks (e.g., external APIs, file I/O)
- Requires async-compatible middleware and DB drivers
- Django 3.1+ supports async views natively

---

## 8. How to reduce API response time in Django?

**Hint:** Think DRF and network optimization.

**Answer:**
- Optimize serializers (`depth`, `SerializerMethodField`)
- Use `select_related()`/`prefetch_related()`
- Use pagination and throttling
- GZipMiddleware to compress responses
- Cache common GET responses

---

## 9. How do you scale Django for millions of users?

**Hint:** Think horizontally, decoupled services.

**Answer:**
- Horizontal scaling via load balancer
- Use Redis for caching/session storage
- Read replicas for DB scaling
- Sharding for large datasets
- Use Celery for background jobs
- Use CDN for media delivery

---

## 10. How do you optimize static and media file delivery?

**Hint:** Focus on storage and delivery methods.

**Answer:**
- Use WhiteNoise for static in simple setups
- CDN (e.g., Cloudflare, CloudFront)
- Offload media to S3 or similar
- Set cache headers (gzip, brotli compression)

---

## Tools to Mention

- **Profiling**: Django Debug Toolbar, Silk, cProfile
- **Monitoring**: Sentry, New Relic, Prometheus
- **Scaling**: Nginx, Gunicorn/Uvicorn, Kubernetes
- **Async Tasks**: Celery, Dramatiq
- **DB Tuning**: EXPLAIN, PgHero

---


# Django Performance Techniques with Examples

---

## 1. Per-view Caching with `@cache_page`

```python
from django.views.decorators.cache import cache_page
from django.http import JsonResponse

@cache_page(60 * 15)  # Cache for 15 minutes
def my_view(request):
    return JsonResponse({'data': 'fresh result'})
```

---

## 2. Offload Tasks to Celery/Django-Q

### Celery Example:

1. Install:
```bash
pip install celery redis
```

2. `celery.py` setup:
```python
import os
from celery import Celery

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "myproject.settings")
app = Celery("myproject")
app.config_from_object("django.conf:settings", namespace="CELERY")
app.autodiscover_tasks()
```

3. Task in `tasks.py`:
```python
from celery import shared_task

@shared_task
def send_email(user_id):
    ...
```

4. Call task:
```python
send_email.delay(user.id)
```

---

## 3. Use Gunicorn/Uvicorn Behind Nginx

### Gunicorn:
```bash
gunicorn myproject.wsgi:application --bind 0.0.0.0:8000
```

### Uvicorn:
```bash
uvicorn myproject.asgi:application --host 0.0.0.0 --port 8000
```

### Nginx config:
```nginx
server {
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

---

## 4. Horizontal Scaling with Load Balancers

Use HAProxy, AWS ELB, or Nginx to distribute traffic.

```bash
backend django_app
    balance roundrobin
    server app1 127.0.0.1:8001 check
    server app2 127.0.0.1:8002 check
```

---

## 5. Django Debug Toolbar / Silk for Profiling

### Debug Toolbar:
```bash
pip install django-debug-toolbar
```

```python
INSTALLED_APPS += ['debug_toolbar']
MIDDLEWARE = ['debug_toolbar.middleware.DebugToolbarMiddleware'] + MIDDLEWARE
INTERNAL_IPS = ['127.0.0.1']
```

### Silk:
```bash
pip install django-silk
```

---

## 6. Use Pagination and Throttling (DRF)

### Pagination:
```python
REST_FRAMEWORK = {
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 10,
}
```

### Throttling:
```python
REST_FRAMEWORK.update({
    'DEFAULT_THROTTLE_CLASSES': [
        'rest_framework.throttling.UserRateThrottle'
    ],
    'DEFAULT_THROTTLE_RATES': {
        'user': '100/day',
    }
})
```

---

## 7. Use GZipMiddleware to Compress Responses

```python
MIDDLEWARE = ['django.middleware.gzip.GZipMiddleware'] + MIDDLEWARE
```

---

## 8. Cache Common GET Responses

```python
from django.views.decorators.cache import cache_page

@cache_page(60 * 10)
def product_list(request):
    ...
```

---

## 9. Horizontal Scaling via Load Balancer (Clarified)

Use ELB, Nginx, or Kubernetes service to distribute to multiple Django containers or servers.

---

## 10. Use Redis for Caching/Session Storage

```bash
pip install django-redis
```

```python
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": "redis://127.0.0.1:6379/1",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        }
    }
}
SESSION_ENGINE = "django.contrib.sessions.backends.cache"
SESSION_CACHE_ALIAS = "default"
```

---

## 11. Use WhiteNoise for Static in Simple Setups

```bash
pip install whitenoise
```

```python
MIDDLEWARE = ['whitenoise.middleware.WhiteNoiseMiddleware'] + MIDDLEWARE
STATICFILES_STORAGE = "whitenoise.storage.CompressedManifestStaticFilesStorage"
```

---
---------------------------------------------------------------------------------------------------------------------
### multi-threading
---------------------------------------------------------------------------------------------------------------------
- Threading vs Multiprocessing: Key Differences
	- 	| Feature       | `threading`                             | `multiprocessing`                           |
		| ------------- | --------------------------------------- | ------------------------------------------- |
		| Concurrency   | Yes                                     | Yes                                         |
		| Parallelism   | No (limited by GIL)                     | Yes (bypasses GIL)                          |
		| GIL Impact    | Affected (only 1 thread runs at a time) | Not affected (each process has its own GIL) |
		| Memory Space  | Shared                                  | Separate                                    |
		| Communication | Shared memory, locks, queues            | Pipes, queues, shared memory                |
		| Suitable For  | I/O-bound tasks                         | CPU-bound tasks                             |
		| Crashes       | One thread crash may affect others      | One process crash does not affect others    |
	- When to Use
	- 	| Scenario               | Use This          |
		| ---------------------- | ----------------- |
		| Downloading files      | `threading`       |
		| Scraping websites      | `threading`       |
		| CPU-intensive tasks    | `multiprocessing` |
		| Image/Video processing | `multiprocessing` |
		| Database query pooling | `threading`       |
	

---------------------------------------------------------------------------------------------------------------------
### OOPs 
---------------------------------------------------------------------------------------------------------------------
# Python Django OOP Interview Questions 

## 1. How is Object-Oriented Programming (OOP) applied in Django?

**Answer:**  
Django is built on the principles of OOP. Models in Django are Python classes that represent database tables. Views are class-based (in CBVs) or function-based. Inheritance, encapsulation, and polymorphism are used extensively throughout the framework for extensibility and code reuse.

---

## 2. Explain the use of inheritance in Django models and views.

**Answer:**  
Inheritance allows shared behavior to be abstracted. In models, base classes can define common fields:

```python
class TimeStampedModel(models.Model):
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True

class Product(TimeStampedModel):
    name = models.CharField(max_length=100)
```

In views, Django provides `View` base classes like `ListView`, `DetailView` which can be inherited and customized.

---

## 3. What is the difference between an abstract base class and a multi-table inheritance in Django?

**Answer:**

- **Abstract base classes**: Shared fields and methods; no separate database table created.
- **Multi-table inheritance**: Each model has its own database table and is linked via an implicit OneToOne field.

---

## 4. How is encapsulation used in Django?

**Answer:**  
Encapsulation hides implementation details. Django promotes it by encapsulating logic inside model methods, view classes, and custom managers.

Example:

```python
class Order(models.Model):
    ...

    def calculate_total(self):
        return sum(item.price for item in self.items.all())
```

---

## 5. How does Django support polymorphism?

**Answer:**  
Polymorphism is used in function and class-based views, middleware, serializers, etc., allowing different behaviors via the same interface.

Example: Using different serializers in the same view depending on request method:

```python
def get_serializer_class(self):
    if self.request.method == 'POST':
        return CreateSerializer
    return ReadOnlySerializer
```

---

## 6. How would you implement method overriding in Django class-based views?

**Answer:**  
Override standard methods like `get`, `post`, `get_queryset`, etc., to customize behavior.

```python
class CustomListView(ListView):
    def get_queryset(self):
        return super().get_queryset().filter(active=True)
```

---

## 7. What design patterns used in Django are based on OOP?

**Answer:**

- **MVC/MVT**: Django uses the Model-View-Template pattern.
- **Factory**: Used in ORM for object creation.
- **Singleton**: Django settings and certain utilities follow Singleton-like patterns.
- **Template Method**: CBVs implement this pattern to allow method overriding for customization.

---

## 8. How do mixins enhance OOP in Django?

**Answer:**  
Mixins provide reusable behavior. For example, `LoginRequiredMixin` adds authentication enforcement without repeating logic:

```python
class MyView(LoginRequiredMixin, View):
    ...
```

---

## 9. How do you architect reusable and extensible components in Django using OOP principles?

**Answer:**

- Use abstract base classes for models and reusable mixins for views.
- Use encapsulation to isolate business logic inside models or services.
- Prefer composition over inheritance where appropriate.
- Define interfaces or protocols with abstract base classes for loose coupling.

---

## 10. As an architect, how do you ensure maintainable OOP design in Django projects?

**Answer:**

- Enforce single responsibility principle in views/models.
- Favor reusable and testable components.
- Implement SOLID principles.
- Use custom managers and services for domain logic separation.

---------------------------------------------------------------------------------------------------------------------
### Technical Architect
---------------------------------------------------------------------------------------------------------------------
- How do you approach designing a scalable and resilient distributed system?
	- Understand the project and business requirements and creats functional, non-functional requirements documents, such as availability, scalability, and latency targets.
	- choose an appropriate architectural style (e.g., microservices, event-driven, or serverless)
	- design for failure using patterns like circuit breakers and retries, and ensure scalability using techniques like horizontal partitioning and load balancing.
	- leverage cloud-native services to improve reliability and performance, and ensure observability through logging, metrics, and tracing.

- What's your approach to selecting the right tech stack or framework?
	- I evaluate based on project needs: scalability, team expertise, ecosystem maturity, and long-term maintainability.
	- Django for rapid development of admin-heavy platforms
	- Flask for lightweight microservices
	- choose FastAPI when high-performance async APIs are required
	- infrastructure, I consider vendor lock-in, community support, and security when choosing between AWS, Azure, or GCP.
	- Based on Framework, follow all the required guideline and develop application around it
	- Apply SOLID principles
	- Apply appropreat design patterns
	- Modular approach by using microservice architecture, Microservices for modular scaling and team autonomy.
	- Event-driven architecture for decoupled and scalable interactions.
	- CQRS and Event Sourcing in cases requiring auditability and complex business logic.
	- Repository pattern for abstracting persistence logic in Python apps.

- Design a user management system that supports millions of users.
	- Architecture: Microservices (User Service, Auth Service, Admin Service)
	- Storage: PostgreSQL or Aurora for strong consistency; Redis for session tokens
	- Auth: OAuth2 / JWT for stateless auth
	- Scaling: Load balancer with auto-scaling groups; partitioned DB
	- Security: TLS, encrypted passwords, audit logging
	- Monitoring: Prometheus/Grafana + structured logging
	- CI/CD: Canary release with health checks on rollout
	
- How do you ensure systems are maintainable over time?
	- By prioritizing clean code, modular design, and documentation. I enforce API versioning, maintain backward compatibility, and automate tests to avoid regression. Regular tech debt reviews and architectural refactoring cycles also help in long-term sustainability.	

---------------------------------------------------------------------------------------------------------------------
### RESTFul API & Microservice Qs?
---------------------------------------------------------------------------------------------------------------------

---------------------------------------------------------------------------------------------------------------------
### Design Patterns
---------------------------------------------------------------------------------------------------------------------

---------------------------------------------------------------------------------------------------------------------
### Django
---------------------------------------------------------------------------------------------------------------------
- What is WSGI
	- WSGI (Web Server Gateway Interface) is a Python standard (PEP 3333) that defines how web servers (like Gunicorn or uWSGI) communicate with Python web applications, like Django.
	- It acts as a bridge between: The web server (e.g., NGINX + Gunicorn), and The Python web framework (e.g., Django, Flask)
	- Django is not a web server — it's an application framework. WSGI lets a web server run your Django application in a production environment.
	- How WSGI Works (Simplified Flow):
		- Client sends HTTP request → hits NGINX.
		- NGINX passes the request to a WSGI server like Gunicorn.
		- Gunicorn uses WSGI to hand over the request to Django.
		- Django processes it and returns a WSGI-compatible response.
		- Response flows back through Gunicorn → NGINX → browser.
	- In Django
		- The entry point for WSGI is wsgi.py in your project root:
		```python
			from django.core.wsgi import get_wsgi_application
			application = get_wsgi_application()
		```
		- This object (application) is what the WSGI server uses to interact with Django.

	- 	| Feature    | WSGI                    | ASGI                      |
		| ---------- | ----------------------- | ------------------------- |
		| Protocol   | Synchronous HTTP        | Async + HTTP, WebSockets  |
		| Use Case   | Traditional web apps    | Real-time apps, async I/O |
		| Django Use | Default in Django < 3.0 | Django 3.0+ supports ASGI |
		| Example    | Gunicorn, uWSGI         | Uvicorn, Daphne           |

	- ![alt text](<images/wsgi-vs-asgi-1.png>)

- What is ASGI
	- ASGI (Asynchronous Server Gateway Interface) is the successor to WSGI, designed to handle asynchronous web protocols such as WebSockets, HTTP/2, and long-lived connections, in addition to traditional HTTP.
	- It provides a standard interface between asynchronous Python web applications and servers — just like WSGI does for synchronous apps.
	- Why was ASGI introduced?
		- WSGI is synchronous-only, meaning it can’t natively handle:
			- WebSockets
			- Background tasks using async/await
			- High-concurrency or real-time data streaming
		- ASGI fills this gap, enabling Django (3.0+) and other frameworks to support modern use cases like:
			- Chat apps
			- Real-time dashboards
			- Async APIs
			- IoT and streaming
		- Key Components of ASGI
			-   | Component            | Role                                  |
				| -------------------- | ------------------------------------- |
				| **ASGI Server**      | Hosts the app (e.g., Uvicorn, Daphne) |
				| **ASGI App**         | Your web framework (e.g., Django)     |
				| **Protocol Support** | HTTP, WebSocket, etc.                 |
				| **Lifespan Events**  | Startup/shutdown hooks                |
		- Django >= 3.0 supports ASGI.
			- Your entrypoint becomes asgi.py instead of wsgi.py:
			- 
			```python
				# myproject/asgi.py
				import os
				from django.core.asgi import get_asgi_application

				os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'myproject.settings')
				application = get_asgi_application()

			```
		- Django can’t switch between WSGI and ASGI in the same process.
		- You’ll need to deploy two separate servers (or processes).
		- Tools like Django Channels use ASGI and can work alongside a WSGI setup.


- Walk through Django’s request-response cycle.
	- Browser sends a request to the server.
	- WSGI or ASGI interface receives it and passes it to Django.
	- Django settings determine the middleware stack (in MIDDLEWARE list).
	- Request flows top-down through middleware, which can modify or short-circuit it.
	- URL dispatcher (urls.py) matches the path to a view.
	- View function or class-based view (CBV) handles business logic.
	- If a template is rendered, context is passed and rendered with a response object.
	- The response flows back up through the middleware.
	- Final response is returned to the WSGI/ASGI handler, then to the client.

- What is the Global Interpreter Lock (GIL), and how does it affect Django?
	- The GIL allows only one thread to execute Python bytecode at a time.
	- This means Python threads are not truly concurrent for CPU-bound tasks.
	- For Django (WSGI), it’s less of an issue because
		- Web requests are I/O-bound (DB, network)
		- Django can use multi-process deployment (e.g., Gunicorn with multiple workers)
	- For async workloads: use ASGI + async views, or offload to Celery for CPU tasks.

- How would you scale a Django application?
	- App layer: Use Gunicorn + NGINX + multiple workers or ASGI servers (e.g., Uvicorn for async)
	- DB layer: Read replicas, horizontal partitioning (sharding), indexing
	- Caching: Redis for views/data; CDN for static/media
	- Task queues: Celery + Redis/RabbitMQ for async jobs
	- Monitoring: Prometheus + Grafana or New Relic, Sentry for errors
	- Infrastructure: Docker, Kubernetes, Terraform for scaling and IaC

- How do you secure Django in production?
	- Use SECURE_SSL_REDIRECT, SECURE_HSTS_SECONDS, CSRF_COOKIE_SECURE, etc.
	- Use HTTPS, sanitize user input (prevent XSS), enforce strong passwords
	- Restrict ALLOWED_HOSTS
	- Use Content Security Policy (CSP)
	- Apply all possible validations
	- Use Django's built-in tools + WAF/CDN layer (e.g., Cloudflare)

- How do you enforce architectural boundaries in large Django projects?
	- Use modular Django apps to represent domains
	- Apply domain-driven design (DDD) principles
	- Keep fat models, thin views or move logic to service layers
	- Shared contracts/interfaces to separate domains
	- Use custom validators, managers, and querysets to encapsulate logic
	- Static code analysis (e.g., flake8, mypy, pyright) and tests

- How would you design a multi-tenant SaaS application in Django?
	- Tenant Isolation Options:
		- Shared DB, separate schemas: Use django-tenants
		- Separate DB per tenant: More secure, complex migrations
	- Shared components (auth, billing, etc.) in a core app
	- Middleware to resolve tenant from domain or header
	- Tenant-aware querysets and managers
	- Feature flagging via something like django-waffle
	- Use signals and hooks to manage tenant lifecycle events (e.g., onboarding)

- How do you create a model in Django?
	- ```python
		from django.db import models

		class Book(models.Model):
			title = models.CharField(max_length=100)
			author = models.CharField(max_length=50)
	```

- How do you run migrations?
	- python manage.py makemigrations
	- python manage.py migrate

- How do you retrieve all objects from a model?
	- Book.objects.all()

- What does get() do and how is it different from filter()?
	- get() returns a single object and raises DoesNotExist or MultipleObjectsReturned if conditions aren't met.
	- filter() returns a queryset, even if it's empty.

- How do you update a record using the ORM?
	- ```python
		book = Book.objects.get(id=1)
		book.title = "New Title"
		book.save()

		OR 

		Book.objects.filter(id=1).update(title="New Title")
	```

-  What is the difference between F() and Q() objects?
	- F() allows referencing model fields directly for arithmetic or comparisons.
	- Q() enables complex query conditions with &, |, and ~.
	- ```python
		from django.db.models import F, Q
		Book.objects.filter(copies__gt=F('sold'))
		Book.objects.filter(Q(author="Alice") | Q(author="Bob"))
	```

- How do you perform aggregation in Django ORM?
	- ```python
		from django.db.models import Avg, Sum, Count
		Book.objects.aggregate(Avg('price'), Count('id'))
	```
- What are related managers and how do you use them?
	- ```python
		author = Author.objects.get(id=1)
		author.book_set.all()
	```

- What are annotations in Django ORM?
	- Annotations are used to add summary values to each object in a queryset.
	- ```python
		from django.db.models import Count
		Author.objects.annotate(num_books=Count('book'))
	```

- What is select_related and prefetch_related?
	- select_related uses SQL JOINs to reduce DB hits (for ForeignKey or OneToOneField).
	- prefetch_related uses separate queries and is better for ManyToMany or reverse ForeignKey.
	- ```python
		Book.objects.select_related('author')
		Book.objects.prefetch_related('genres')
	```

- How do you write raw SQL queries in Django?
	- ```python
		from django.db import connection
		cursor = connection.cursor()
		cursor.execute("SELECT * FROM app_book")

		OR 

		Book.objects.raw("SELECT * FROM app_book WHERE id = %s", [1])
	```

- What is the difference between exists() and count()?
	- exists() checks for presence and is optimized — returns a boolean.
	- count() counts all matching rows — returns an integer.
	- ```python
		Book.objects.filter(title="Django").exists()
		Book.objects.filter(title="Django").count()

	```

- How can you enforce uniqueness of a field or combination of fields?
	- Use unique=True in a field: email = models.EmailField(unique=True)
	- Use unique_together or constraints:
	- ```python
		class Meta:
	    unique_together = ('author', 'title')

	```

---------------------------------------------------------------------------------------------------------------------
### FastAPI
---------------------------------------------------------------------------------------------------------------------
- What is FastAPI?
	- FastAPI is a modern, high-performance web framework for building APIs with Python 3.7+ based on standard Python type hints. It's built on Starlette for the web parts and Pydantic for data validation.
- What are the key features of FastAPI?
	- Fast: Very high performance, on par with NodeJS and Go (thanks to ASGI and Starlette).
	- Type hinting support: Automatic request validation using Python types.
	- Automatic docs: Swagger UI and ReDoc are auto-generated.
	- Dependency Injection: Powerful DI system using Depends.
	- Asynchronous support: Built-in async/await support for I/O-bound operations
- What is Pydantic and how is it used in FastAPI?
	- Pydantic is a data validation and settings management library using Python type annotations. FastAPI uses Pydantic models to:
		- Validate request bodies
		- Enforce data types
		- Automatically generate OpenAPI docs.
		- ```python
			from pydantic import BaseModel
	
			class User(BaseModel):
			    name: str
			    age: int

   	  	  ```
- How do you define routes in FastAPI?
	- Using decorators like @app.get(), @app.post(), etc.
	- ```python
		from fastapi import FastAPI
	
		app = FastAPI()
		
		@app.get("/hello")
		def read_root():
		    return {"message": "Hello, world!"}

   	  ```
- How does FastAPI handle dependency injection?
	- FastAPI uses the Depends function to declare dependencies.
	- ```python
		from fastapi import Depends
		def get_db():
		    db = DBSession()
		    try:
			yield db
		    finally:
			db.close()
		
		@app.get("/items/")
		def read_items(db=Depends(get_db)):
		    return db.query(Item).all()

     	  ```
- What are background tasks in FastAPI?
	- FastAPI supports background tasks using BackgroundTasks. These are useful for sending emails, logging, or triggering jobs after the response is sent.
	- ```python
		from fastapi import BackgroundTasks
	
		def write_log(message: str):
		    with open("log.txt", "a") as f:
			f.write(message)
		
		@app.post("/log/")
		def log_event(message: str, background_tasks: BackgroundTasks):
		    background_tasks.add_task(write_log, message)
		    return {"message": "Log scheduled"}

     	  ```
- How to handle exceptions in FastAPI?
	- Using HTTPException or custom exception handlers.
	- ```python
		from fastapi import HTTPException
		@app.get("/item/{id}")
		def read_item(id: int):
		    if id not in database:
			raise HTTPException(status_code=404, detail="Item not found")
		    return database[id]

  	```
- How does FastAPI support async programming?
	- FastAPI can handle async def endpoints, enabling non-blocking I/O with libraries like httpx, aiomysql, etc.
	- ```python
		@app.get("/async")
		async def async_endpoint():
		await asyncio.sleep(1)
		return {"message": "Async response"}
	  ```
- How do you secure FastAPI routes with OAuth2 or JWT?
	- Use Depends and OAuth2PasswordBearer to secure routes.
	- ```python
		from fastapi.security import OAuth2PasswordBearer
		oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")
		
		@app.get("/users/me")
		def read_users_me(token: str = Depends(oauth2_scheme)):
		    return decode_jwt(token)
  ```
- How can you structure a large FastAPI project?
	- Split routers into modules.
	- Use APIRouter.
	- Use services and repositories for business logic and DB access.
	- Load environment config using Pydantic’s BaseSettings.
   	- ```css
		app/
		 ┣ main.py
		 ┣ routers/
		 ┃ ┗ users.py
		 ┣ models/
		 ┣ schemas/
		 ┣ services/
	 ┗ core/
   	  ```
- What is ASGI and how is it relevant to FastAPI?
  	- ASGI (Asynchronous Server Gateway Interface) is the asynchronous successor to WSGI. FastAPI runs on ASGI, allowing for async capabilities and WebSockets.
- How to implement middleware in FastAPI?
- What is the difference between Depends and direct parameter injection?
- How do you test FastAPI applications?
	- Use TestClient from fastapi.testclient with pytest.
- 
---------------------------------------------------------------------------------------------------------------------
### Other Qs?
---------------------------------------------------------------------------------------------------------------------
- How do you ensure code quality across teams?
	- Add appropreat plug-ins in editor (automated linting plug-ins)
	- Coding standards (as per framework guideline)
	- define architectural guidelines, establish a shared coding standard (e.g., PEP8 for Python),
	- Code review

- How do you handle disagreements in architectural decisions?
	- I encourage open discussion based on data and tradeoffs. I present pros/cons of alternatives and align the decision with business impact and technical sustainability. I also facilitate POCs if needed to validate choices. Ultimately, it’s about making informed, collaborative decisions that are transparent.

- How do you mentor junior engineers effectively?
	- I provide 1:1s focused on growth areas, give contextual feedback in PRs, and delegate ownership gradually. I organize architecture deep-dives and hands-on pairing sessions. I also encourage them to present in tech forums or brown-bag sessions to build confidence.
	- Code reviews with constructive feedback
	- Pair programming
	- Create onboarding guides and architecture diagrams
	- Encourage testing, docstrings, and code clarity
	- Give juniors ownership of small features, gradually increasing scope and make them accoutable for the feature/module

- Tell me about a time you introduced a major technical change.
	- At my previous role, I led a monolith to microservices migration. I proposed a phased rollout starting with stateless components. I documented service boundaries, introduced Docker-based CI/CD, and set up distributed tracing. We saw deployment frequency increase 3x and reduced incident response time by 40%.

- How do you stay updated with tech trends?
	- I follow engineering blogs (Netflix, Uber, ThoughtWorks), read architecture books (like Software Architecture Patterns), and participate in design communities. I also experiment with new tools in sandbox environments and mentor junior devs, which challenges me to stay current.

- What is the Global Interpreter Lock (GIL) in Python?
	- The GIL is a mutex (mutual exclusion lock) that allows only one thread to execute Python bytecode at a time, even on multi-core systems. It exists in CPython (the standard implementation) and simplifies memory management.

- Why does Python have a GIL?
	- CPython uses reference counting for memory management, which is not thread-safe by default. The GIL ensures memory safety without requiring complex locking mechanisms, making CPython easier to implement and maintain.

- What are the drawbacks of the GIL?
	- Prevents true parallel execution of threads for CPU-bound tasks
	- Limits multi-core utilization in multithreaded Python programs
	- Requires workarounds (e.g., multiprocessing, external libraries) for scalability

- How does GIL affect CPU-bound vs I/O-bound programs?
	- CPU-bound programs: GIL causes threads to compete, leading to poor parallelism.
	- I/O-bound programs: GIL is less of a bottleneck because the thread holding the GIL releases it during I/O operations (e.g., sleep, network).

-  How can you work around the GIL for CPU-bound tasks?
	- Use the multiprocessing module to spawn separate processes (each with its own Python interpreter and GIL).
	- Use native extensions (Cython, NumPy) to release the GIL during intensive C computations.
	- Offload CPU work to compiled C/C++ libraries.

- Does AsyncIO help with the GIL problem?
	- Not for CPU-bound tasks. AsyncIO helps with I/O concurrency, not parallel CPU execution. For CPU-bound workloads, use multiprocessing or offload to native code.

- When GIL is a bottleneck
	- In CPU-bound programs (e.g., heavy number crunching), GIL prevents threads from running in parallel, and multiprocessing is preferred.
	- 	| Scenario                          | GIL Impact | Recommended Approach                           |
		| --------------------------------- | ---------- | ---------------------------------------------- |
		| I/O-bound (e.g., HTTP, file I/O)  | Low        | `threading`, `asyncio`                         |
		| CPU-bound (e.g., ML, computation) | High       | `multiprocessing`, `Cython`, native extensions |

- How to make Python as Strict Typed Language.
	- Use Type Hints (PEP 484)
	- Enforce Type Checking with mypy
		- pip install mypy
		- mypy your_script.py
	- Use pyright or pylance (for VS Code)
		- pyright is a fast type checker from Microsoft.
		- pylance integrates pyright into Visual Studio Code with real-time checking.
	- Use pydantic for runtime type enforcement
		- For runtime strictness (especially in APIs):
		- ```python
			from pydantic import BaseModel

			class User(BaseModel):
				id: int
				name: str

			user = User(id=1, name="Alice")           # ✅ OK
			user = User(id="abc", name="Bob")         # ❌ Raises validation error
		```
	- Add --strict mode for mypy
		- mypy your_script.py --strict
	- Use typeguard or beartype for runtime type checks
	- 	| Tool        | Purpose                        | Type Checking |
		| ----------- | ------------------------------ | ------------- |
		| `mypy`      | Static analysis                | Compile-time  |
		| `pyright`   | Fast static analysis (VSCode)  | Compile-time  |
		| `pydantic`  | Data validation (FastAPI etc.) | Runtime       |
		| `typeguard` | Runtime type enforcement       | Runtime       |
		| `beartype`  | Runtime decorator-based typing | Runtime       |

- What is Lambda Function
	- A Lambda Function is a small anonymous function. A lambda function can take any number of arguments, but can only have one expression.
